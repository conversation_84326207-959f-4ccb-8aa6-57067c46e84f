{"ast": null, "code": "import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport { floor } from '@floating-ui/utils';\nimport { tabbable } from 'tabbable';\n\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\nfunction matchesFocusVisible(element) {\n  // We don't want to block focus from working with `visibleOnly`\n  // (JSDOM doesn't match `:focus-visible` when the element has `:focus`)\n  if (!element || isJSDOM()) return true;\n  try {\n    return element.matches(':focus-visible');\n  } catch (_e) {\n    return true;\n  }\n}\nfunction getFloatingFocusElement(floatingElement) {\n  if (!floatingElement) {\n    return null;\n  }\n  // Try to find the element that has `{...getFloatingProps()}` spread on it.\n  // This indicates the floating element is acting as a positioning wrapper, and\n  // so focus should be managed on the child element with the event handlers and\n  // aria props.\n  return floatingElement.hasAttribute(FOCUSABLE_ATTRIBUTE) ? floatingElement : floatingElement.querySelector(\"[\" + FOCUSABLE_ATTRIBUTE + \"]\") || floatingElement;\n}\nfunction getNodeChildren(nodes, id, onlyOpenChildren) {\n  if (onlyOpenChildren === void 0) {\n    onlyOpenChildren = true;\n  }\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = onlyOpenChildren ? nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    }) : nodes;\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getNodeChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\nfunction getNodeAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\nconst useInsertionEffect = SafeReact.useInsertionEffect;\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\nfunction isDifferentGridRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfListBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledListIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  let index = startingIndex;\n  do {\n    index += decrement ? -amount : amount;\n  } while (index >= 0 && index <= listRef.current.length - 1 && isListIndexDisabled(listRef, index, disabledIndices));\n  return index;\n}\nfunction getGridNavigatedIndex(listRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    rtl,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          decrement: true,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT) ? maxIndex : findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\n/** For each cell index, gets the item index that occupies that cell */\nfunction createGridCellMap(sizes, cols, dense) {\n  const cellMap = [];\n  let startIndex = 0;\n  sizes.forEach((_ref2, index) => {\n    let {\n      width,\n      height\n    } = _ref2;\n    if (width > cols) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[Floating UI]: Invalid grid - item width at index \" + index + \" is greater than grid columns\");\n      }\n    }\n    let itemPlaced = false;\n    if (dense) {\n      startIndex = 0;\n    }\n    while (!itemPlaced) {\n      const targetCells = [];\n      for (let i = 0; i < width; i++) {\n        for (let j = 0; j < height; j++) {\n          targetCells.push(startIndex + i + j * cols);\n        }\n      }\n      if (startIndex % cols + width <= cols && targetCells.every(cell => cellMap[cell] == null)) {\n        targetCells.forEach(cell => {\n          cellMap[cell] = index;\n        });\n        itemPlaced = true;\n      } else {\n        startIndex++;\n      }\n    }\n  });\n\n  // convert into a non-sparse array\n  return [...cellMap];\n}\n\n/** Gets cell index of an item's corner or -1 when index is -1. */\nfunction getGridCellIndexOfCorner(index, sizes, cellMap, cols, corner) {\n  if (index === -1) return -1;\n  const firstCellIndex = cellMap.indexOf(index);\n  const sizeItem = sizes[index];\n  switch (corner) {\n    case 'tl':\n      return firstCellIndex;\n    case 'tr':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + sizeItem.width - 1;\n    case 'bl':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + (sizeItem.height - 1) * cols;\n    case 'br':\n      return cellMap.lastIndexOf(index);\n  }\n}\n\n/** Gets all cell indices that correspond to the specified indices */\nfunction getGridCellIndices(indices, cellMap) {\n  return cellMap.flatMap((index, cellIndex) => indices.includes(index) ? [cellIndex] : []);\n}\nfunction isListIndexDisabled(listRef, index, disabledIndices) {\n  if (typeof disabledIndices === 'function') {\n    return disabledIndices(index);\n  } else if (disabledIndices) {\n    return disabledIndices.includes(index);\n  }\n  const element = listRef.current[index];\n  return element == null || element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';\n}\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, dir) {\n  const list = tabbable(container, getTabbableOptions());\n  const len = list.length;\n  if (len === 0) return;\n  const active = activeElement(getDocument(container));\n  const index = list.indexOf(active);\n  const nextIndex = index === -1 ? dir === 1 ? 0 : len - 1 : index + dir;\n  return list[nextIndex];\n}\nfunction getNextTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, 1) || referenceElement;\n}\nfunction getPreviousTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, -1) || referenceElement;\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\nexport { activeElement, contains, createGridCellMap, disableFocusInside, enableFocusInside, findNonDisabledListIndex, getDeepestNode, getDocument, getFloatingFocusElement, getGridCellIndexOfCorner, getGridCellIndices, getGridNavigatedIndex, getMaxListIndex, getMinListIndex, getNextTabbable, getNodeAncestors, getNodeChildren, getPlatform, getPreviousTabbable, getTabbableOptions, getTarget, getUserAgent, isAndroid, isDifferentGridRow, isEventTargetWithin, isIndexOutOfListBounds, isJSDOM, isListIndexDisabled, isMac, isMouseLikePointerType, isOutsideEvent, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, matchesFocusVisible, stopEvent, useEffectEvent, useLatestRef, index as useModernLayoutEffect };", "map": {"version": 3, "names": ["isShadowRoot", "isHTMLElement", "React", "useLayoutEffect", "useEffect", "floor", "tabbable", "getPlatform", "uaData", "navigator", "userAgentData", "platform", "getUserAgent", "Array", "isArray", "brands", "map", "_ref", "brand", "version", "join", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "test", "vendor", "isAndroid", "re", "isMac", "toLowerCase", "startsWith", "maxTouchPoints", "isJSDOM", "includes", "FOCUSABLE_ATTRIBUTE", "TYPEABLE_SELECTOR", "ARROW_LEFT", "ARROW_RIGHT", "ARROW_UP", "ARROW_DOWN", "activeElement", "doc", "_activeElement", "shadowRoot", "contains", "parent", "child", "rootNode", "getRootNode", "next", "parentNode", "host", "get<PERSON><PERSON><PERSON>", "event", "<PERSON><PERSON><PERSON>", "target", "isEventTargetWithin", "node", "e", "isRootElement", "element", "matches", "getDocument", "ownerDocument", "document", "isTypeableElement", "isTypeableCombobox", "getAttribute", "matchesFocusVisible", "_e", "getFloatingFocusElement", "floatingElement", "hasAttribute", "querySelector", "getNodeChildren", "nodes", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allChildren", "filter", "_node$context", "parentId", "context", "open", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "_current<PERSON><PERSON><PERSON><PERSON>", "some", "n", "_node$context2", "concat", "getDeepestNode", "deepestNodeId", "max<PERSON><PERSON><PERSON>", "findDeepest", "nodeId", "depth", "children", "for<PERSON>ach", "find", "getNodeAncestors", "_nodes$find", "allAncestors", "currentParentId", "currentNode", "stopEvent", "preventDefault", "stopPropagation", "isReactEvent", "isVirtualClick", "mozInputSource", "isTrusted", "pointerType", "type", "buttons", "detail", "isVirtualPointerEvent", "width", "height", "pressure", "isMouseLikePointerType", "strict", "values", "push", "undefined", "index", "SafeReact", "useLatestRef", "value", "ref", "useRef", "current", "useInsertionEffect", "useSafeInsertionEffect", "fn", "useEffectEvent", "callback", "process", "env", "NODE_ENV", "Error", "useCallback", "_len", "arguments", "args", "_key", "isDifferentGridRow", "cols", "prevRow", "Math", "isIndexOutOfListBounds", "listRef", "getMinListIndex", "disabledIndices", "findNonDisabledListIndex", "getMaxListIndex", "decrement", "startingIndex", "_temp", "amount", "isListIndexDisabled", "getGridNavigatedIndex", "orientation", "loop", "rtl", "minIndex", "maxIndex", "prevIndex", "stop", "nextIndex", "key", "col", "maxCol", "offset", "lastRow", "createGridCellMap", "sizes", "dense", "cellMap", "startIndex", "_ref2", "itemPlaced", "targetCells", "i", "j", "every", "cell", "getGridCellIndexOfCorner", "corner", "firstCellIndex", "indexOf", "sizeItem", "lastIndexOf", "getGridCellIndices", "indices", "flatMap", "cellIndex", "getTabbableOptions", "getShadowRoot", "displayCheck", "ResizeObserver", "toString", "getTabbableIn", "container", "dir", "list", "len", "active", "getNextTabbable", "referenceElement", "body", "getPreviousTabbable", "isOutsideEvent", "containerElement", "currentTarget", "relatedTarget", "disableFocusInside", "tabbableElements", "dataset", "tabindex", "setAttribute", "enableFocusInside", "elements", "querySelectorAll", "removeAttribute", "useModernLayoutEffect"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/@floating-ui/react/dist/floating-ui.react.utils.mjs"], "sourcesContent": ["import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport { floor } from '@floating-ui/utils';\nimport { tabbable } from 'tabbable';\n\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\n\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\nfunction matchesFocusVisible(element) {\n  // We don't want to block focus from working with `visibleOnly`\n  // (JSDOM doesn't match `:focus-visible` when the element has `:focus`)\n  if (!element || isJSDOM()) return true;\n  try {\n    return element.matches(':focus-visible');\n  } catch (_e) {\n    return true;\n  }\n}\nfunction getFloatingFocusElement(floatingElement) {\n  if (!floatingElement) {\n    return null;\n  }\n  // Try to find the element that has `{...getFloatingProps()}` spread on it.\n  // This indicates the floating element is acting as a positioning wrapper, and\n  // so focus should be managed on the child element with the event handlers and\n  // aria props.\n  return floatingElement.hasAttribute(FOCUSABLE_ATTRIBUTE) ? floatingElement : floatingElement.querySelector(\"[\" + FOCUSABLE_ATTRIBUTE + \"]\") || floatingElement;\n}\n\nfunction getNodeChildren(nodes, id, onlyOpenChildren) {\n  if (onlyOpenChildren === void 0) {\n    onlyOpenChildren = true;\n  }\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = onlyOpenChildren ? nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    }) : nodes;\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getNodeChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\nfunction getNodeAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\nconst useInsertionEffect = SafeReact.useInsertionEffect;\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\nfunction isDifferentGridRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfListBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledListIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  let index = startingIndex;\n  do {\n    index += decrement ? -amount : amount;\n  } while (index >= 0 && index <= listRef.current.length - 1 && isListIndexDisabled(listRef, index, disabledIndices));\n  return index;\n}\nfunction getGridNavigatedIndex(listRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    rtl,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          decrement: true,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT) ? maxIndex : findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\n/** For each cell index, gets the item index that occupies that cell */\nfunction createGridCellMap(sizes, cols, dense) {\n  const cellMap = [];\n  let startIndex = 0;\n  sizes.forEach((_ref2, index) => {\n    let {\n      width,\n      height\n    } = _ref2;\n    if (width > cols) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[Floating UI]: Invalid grid - item width at index \" + index + \" is greater than grid columns\");\n      }\n    }\n    let itemPlaced = false;\n    if (dense) {\n      startIndex = 0;\n    }\n    while (!itemPlaced) {\n      const targetCells = [];\n      for (let i = 0; i < width; i++) {\n        for (let j = 0; j < height; j++) {\n          targetCells.push(startIndex + i + j * cols);\n        }\n      }\n      if (startIndex % cols + width <= cols && targetCells.every(cell => cellMap[cell] == null)) {\n        targetCells.forEach(cell => {\n          cellMap[cell] = index;\n        });\n        itemPlaced = true;\n      } else {\n        startIndex++;\n      }\n    }\n  });\n\n  // convert into a non-sparse array\n  return [...cellMap];\n}\n\n/** Gets cell index of an item's corner or -1 when index is -1. */\nfunction getGridCellIndexOfCorner(index, sizes, cellMap, cols, corner) {\n  if (index === -1) return -1;\n  const firstCellIndex = cellMap.indexOf(index);\n  const sizeItem = sizes[index];\n  switch (corner) {\n    case 'tl':\n      return firstCellIndex;\n    case 'tr':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + sizeItem.width - 1;\n    case 'bl':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + (sizeItem.height - 1) * cols;\n    case 'br':\n      return cellMap.lastIndexOf(index);\n  }\n}\n\n/** Gets all cell indices that correspond to the specified indices */\nfunction getGridCellIndices(indices, cellMap) {\n  return cellMap.flatMap((index, cellIndex) => indices.includes(index) ? [cellIndex] : []);\n}\nfunction isListIndexDisabled(listRef, index, disabledIndices) {\n  if (typeof disabledIndices === 'function') {\n    return disabledIndices(index);\n  } else if (disabledIndices) {\n    return disabledIndices.includes(index);\n  }\n  const element = listRef.current[index];\n  return element == null || element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, dir) {\n  const list = tabbable(container, getTabbableOptions());\n  const len = list.length;\n  if (len === 0) return;\n  const active = activeElement(getDocument(container));\n  const index = list.indexOf(active);\n  const nextIndex = index === -1 ? dir === 1 ? 0 : len - 1 : index + dir;\n  return list[nextIndex];\n}\nfunction getNextTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, 1) || referenceElement;\n}\nfunction getPreviousTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, -1) || referenceElement;\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\nexport { activeElement, contains, createGridCellMap, disableFocusInside, enableFocusInside, findNonDisabledListIndex, getDeepestNode, getDocument, getFloatingFocusElement, getGridCellIndexOfCorner, getGridCellIndices, getGridNavigatedIndex, getMaxListIndex, getMinListIndex, getNextTabbable, getNodeAncestors, getNodeChildren, getPlatform, getPreviousTabbable, getTabbableOptions, getTarget, getUserAgent, isAndroid, isDifferentGridRow, isEventTargetWithin, isIndexOutOfListBounds, isJSDOM, isListIndexDisabled, isMac, isMouseLikePointerType, isOutsideEvent, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, matchesFocusVisible, stopEvent, useEffectEvent, useLatestRef, index as useModernLayoutEffect };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAQ,wBAAwB;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,QAAQ,QAAQ,UAAU;;AAEnC;AACA,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,MAAM,GAAGC,SAAS,CAACC,aAAa;EACtC,IAAIF,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACG,QAAQ,EAAE;IACrC,OAAOH,MAAM,CAACG,QAAQ;EACxB;EACA,OAAOF,SAAS,CAACE,QAAQ;AAC3B;AACA,SAASC,YAAYA,CAAA,EAAG;EACtB,MAAMJ,MAAM,GAAGC,SAAS,CAACC,aAAa;EACtC,IAAIF,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACN,MAAM,CAACO,MAAM,CAAC,EAAE;IAC1C,OAAOP,MAAM,CAACO,MAAM,CAACC,GAAG,CAACC,IAAI,IAAI;MAC/B,IAAI;QACFC,KAAK;QACLC;MACF,CAAC,GAAGF,IAAI;MACR,OAAOC,KAAK,GAAG,GAAG,GAAGC,OAAO;IAC9B,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACd;EACA,OAAOX,SAAS,CAACY,SAAS;AAC5B;AACA,SAASC,QAAQA,CAAA,EAAG;EAClB;EACA,OAAO,QAAQ,CAACC,IAAI,CAACd,SAAS,CAACe,MAAM,CAAC;AACxC;AACA,SAASC,SAASA,CAAA,EAAG;EACnB,MAAMC,EAAE,GAAG,UAAU;EACrB,OAAOA,EAAE,CAACH,IAAI,CAAChB,WAAW,CAAC,CAAC,CAAC,IAAImB,EAAE,CAACH,IAAI,CAACX,YAAY,CAAC,CAAC,CAAC;AAC1D;AACA,SAASe,KAAKA,CAAA,EAAG;EACf,OAAOpB,WAAW,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,KAAK,CAAC,IAAI,CAACpB,SAAS,CAACqB,cAAc;AACnF;AACA,SAASC,OAAOA,CAAA,EAAG;EACjB,OAAOnB,YAAY,CAAC,CAAC,CAACoB,QAAQ,CAAC,QAAQ,CAAC;AAC1C;AAEA,MAAMC,mBAAmB,GAAG,4BAA4B;AACxD,MAAMC,iBAAiB,GAAG,6CAA6C,GAAG,2EAA2E;AACrJ,MAAMC,UAAU,GAAG,WAAW;AAC9B,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,QAAQ,GAAG,SAAS;AAC1B,MAAMC,UAAU,GAAG,WAAW;AAE9B,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1B,IAAID,aAAa,GAAGC,GAAG,CAACD,aAAa;EACrC,OAAO,CAAC,CAACE,cAAc,GAAGF,aAAa,KAAK,IAAI,IAAI,CAACE,cAAc,GAAGA,cAAc,CAACC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,cAAc,CAACF,aAAa,KAAK,IAAI,EAAE;IACzJ,IAAIE,cAAc;IAClBF,aAAa,GAAGA,aAAa,CAACG,UAAU,CAACH,aAAa;EACxD;EACA,OAAOA,aAAa;AACtB;AACA,SAASI,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/B,IAAI,CAACD,MAAM,IAAI,CAACC,KAAK,EAAE;IACrB,OAAO,KAAK;EACd;EACA,MAAMC,QAAQ,GAAGD,KAAK,CAACE,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,KAAK,CAACE,WAAW,CAAC,CAAC;;EAEzE;EACA,IAAIH,MAAM,CAACD,QAAQ,CAACE,KAAK,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;;EAEA;EACA,IAAIC,QAAQ,IAAI9C,YAAY,CAAC8C,QAAQ,CAAC,EAAE;IACtC,IAAIE,IAAI,GAAGH,KAAK;IAChB,OAAOG,IAAI,EAAE;MACX,IAAIJ,MAAM,KAAKI,IAAI,EAAE;QACnB,OAAO,IAAI;MACb;MACA;MACAA,IAAI,GAAGA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACE,IAAI;IACrC;EACF;;EAEA;EACA,OAAO,KAAK;AACd;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,IAAI,cAAc,IAAIA,KAAK,EAAE;IAC3B,OAAOA,KAAK,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA;EACA;EACA,OAAOD,KAAK,CAACE,MAAM;AACrB;AACA,SAASC,mBAAmBA,CAACH,KAAK,EAAEI,IAAI,EAAE;EACxC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,KAAK;EACd;EACA,IAAI,cAAc,IAAIJ,KAAK,EAAE;IAC3B,OAAOA,KAAK,CAACC,YAAY,CAAC,CAAC,CAACrB,QAAQ,CAACwB,IAAI,CAAC;EAC5C;;EAEA;EACA,MAAMC,CAAC,GAAGL,KAAK;EACf,OAAOK,CAAC,CAACH,MAAM,IAAI,IAAI,IAAIE,IAAI,CAACb,QAAQ,CAACc,CAAC,CAACH,MAAM,CAAC;AACpD;AACA,SAASI,aAAaA,CAACC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,OAAO,CAAC,WAAW,CAAC;AACrC;AACA,SAASC,WAAWA,CAACL,IAAI,EAAE;EACzB,OAAO,CAACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACM,aAAa,KAAKC,QAAQ;AACjE;AACA,SAASC,iBAAiBA,CAACL,OAAO,EAAE;EAClC,OAAO1D,aAAa,CAAC0D,OAAO,CAAC,IAAIA,OAAO,CAACC,OAAO,CAAC1B,iBAAiB,CAAC;AACrE;AACA,SAAS+B,kBAAkBA,CAACN,OAAO,EAAE;EACnC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;EAC1B,OAAOA,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,KAAK,UAAU,IAAIF,iBAAiB,CAACL,OAAO,CAAC;AAClF;AACA,SAASQ,mBAAmBA,CAACR,OAAO,EAAE;EACpC;EACA;EACA,IAAI,CAACA,OAAO,IAAI5B,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;EACtC,IAAI;IACF,OAAO4B,OAAO,CAACC,OAAO,CAAC,gBAAgB,CAAC;EAC1C,CAAC,CAAC,OAAOQ,EAAE,EAAE;IACX,OAAO,IAAI;EACb;AACF;AACA,SAASC,uBAAuBA,CAACC,eAAe,EAAE;EAChD,IAAI,CAACA,eAAe,EAAE;IACpB,OAAO,IAAI;EACb;EACA;EACA;EACA;EACA;EACA,OAAOA,eAAe,CAACC,YAAY,CAACtC,mBAAmB,CAAC,GAAGqC,eAAe,GAAGA,eAAe,CAACE,aAAa,CAAC,GAAG,GAAGvC,mBAAmB,GAAG,GAAG,CAAC,IAAIqC,eAAe;AAChK;AAEA,SAASG,eAAeA,CAACC,KAAK,EAAEC,EAAE,EAAEC,gBAAgB,EAAE;EACpD,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAC/BA,gBAAgB,GAAG,IAAI;EACzB;EACA,IAAIC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAACtB,IAAI,IAAI;IACrC,IAAIuB,aAAa;IACjB,OAAOvB,IAAI,CAACwB,QAAQ,KAAKL,EAAE,KAAK,CAACI,aAAa,GAAGvB,IAAI,CAACyB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,aAAa,CAACG,IAAI,CAAC;EACvG,CAAC,CAAC;EACF,IAAIC,eAAe,GAAGN,WAAW;EACjC,OAAOM,eAAe,CAACC,MAAM,EAAE;IAC7BD,eAAe,GAAGP,gBAAgB,GAAGF,KAAK,CAACI,MAAM,CAACtB,IAAI,IAAI;MACxD,IAAI6B,gBAAgB;MACpB,OAAO,CAACA,gBAAgB,GAAGF,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,gBAAgB,CAACC,IAAI,CAACC,CAAC,IAAI;QACxF,IAAIC,cAAc;QAClB,OAAOhC,IAAI,CAACwB,QAAQ,KAAKO,CAAC,CAACZ,EAAE,KAAK,CAACa,cAAc,GAAGhC,IAAI,CAACyB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,cAAc,CAACN,IAAI,CAAC;MAC3G,CAAC,CAAC;IACJ,CAAC,CAAC,GAAGR,KAAK;IACVG,WAAW,GAAGA,WAAW,CAACY,MAAM,CAACN,eAAe,CAAC;EACnD;EACA,OAAON,WAAW;AACpB;AACA,SAASa,cAAcA,CAAChB,KAAK,EAAEC,EAAE,EAAE;EACjC,IAAIgB,aAAa;EACjB,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,SAASC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAClC,IAAIA,KAAK,GAAGH,QAAQ,EAAE;MACpBD,aAAa,GAAGG,MAAM;MACtBF,QAAQ,GAAGG,KAAK;IAClB;IACA,MAAMC,QAAQ,GAAGvB,eAAe,CAACC,KAAK,EAAEoB,MAAM,CAAC;IAC/CE,QAAQ,CAACC,OAAO,CAACpD,KAAK,IAAI;MACxBgD,WAAW,CAAChD,KAAK,CAAC8B,EAAE,EAAEoB,KAAK,GAAG,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ;EACAF,WAAW,CAAClB,EAAE,EAAE,CAAC,CAAC;EAClB,OAAOD,KAAK,CAACwB,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACmB,EAAE,KAAKgB,aAAa,CAAC;AACtD;AACA,SAASQ,gBAAgBA,CAACzB,KAAK,EAAEC,EAAE,EAAE;EACnC,IAAIyB,WAAW;EACf,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,eAAe,GAAG,CAACF,WAAW,GAAG1B,KAAK,CAACwB,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACmB,EAAE,KAAKA,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyB,WAAW,CAACpB,QAAQ;EAChH,OAAOsB,eAAe,EAAE;IACtB,MAAMC,WAAW,GAAG7B,KAAK,CAACwB,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACmB,EAAE,KAAK2B,eAAe,CAAC;IACnEA,eAAe,GAAGC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACvB,QAAQ;IACrE,IAAIuB,WAAW,EAAE;MACfF,YAAY,GAAGA,YAAY,CAACZ,MAAM,CAACc,WAAW,CAAC;IACjD;EACF;EACA,OAAOF,YAAY;AACrB;AAEA,SAASG,SAASA,CAACpD,KAAK,EAAE;EACxBA,KAAK,CAACqD,cAAc,CAAC,CAAC;EACtBrD,KAAK,CAACsD,eAAe,CAAC,CAAC;AACzB;AACA,SAASC,YAAYA,CAACvD,KAAK,EAAE;EAC3B,OAAO,aAAa,IAAIA,KAAK;AAC/B;;AAEA;AACA,SAASwD,cAAcA,CAACxD,KAAK,EAAE;EAC7B;EACA;EACA,IAAIA,KAAK,CAACyD,cAAc,KAAK,CAAC,IAAIzD,KAAK,CAAC0D,SAAS,EAAE;IACjD,OAAO,IAAI;EACb;EACA,IAAIrF,SAAS,CAAC,CAAC,IAAI2B,KAAK,CAAC2D,WAAW,EAAE;IACpC,OAAO3D,KAAK,CAAC4D,IAAI,KAAK,OAAO,IAAI5D,KAAK,CAAC6D,OAAO,KAAK,CAAC;EACtD;EACA,OAAO7D,KAAK,CAAC8D,MAAM,KAAK,CAAC,IAAI,CAAC9D,KAAK,CAAC2D,WAAW;AACjD;AACA,SAASI,qBAAqBA,CAAC/D,KAAK,EAAE;EACpC,IAAIrB,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK;EAC3B,OAAO,CAACN,SAAS,CAAC,CAAC,IAAI2B,KAAK,CAACgE,KAAK,KAAK,CAAC,IAAIhE,KAAK,CAACiE,MAAM,KAAK,CAAC,IAAI5F,SAAS,CAAC,CAAC,IAAI2B,KAAK,CAACgE,KAAK,KAAK,CAAC,IAAIhE,KAAK,CAACiE,MAAM,KAAK,CAAC,IAAIjE,KAAK,CAACkE,QAAQ,KAAK,CAAC,IAAIlE,KAAK,CAAC8D,MAAM,KAAK,CAAC,IAAI9D,KAAK,CAAC2D,WAAW,KAAK,OAAO;EACvM;EACA3D,KAAK,CAACgE,KAAK,GAAG,CAAC,IAAIhE,KAAK,CAACiE,MAAM,GAAG,CAAC,IAAIjE,KAAK,CAACkE,QAAQ,KAAK,CAAC,IAAIlE,KAAK,CAAC8D,MAAM,KAAK,CAAC,IAAI9D,KAAK,CAAC2D,WAAW,KAAK,OAAO;AACpH;AACA,SAASQ,sBAAsBA,CAACR,WAAW,EAAES,MAAM,EAAE;EACnD;EACA;EACA,MAAMC,MAAM,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;EAC/B,IAAI,CAACD,MAAM,EAAE;IACXC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAEC,SAAS,CAAC;EAC5B;EACA,OAAOF,MAAM,CAACzF,QAAQ,CAAC+E,WAAW,CAAC;AACrC;AAEA,IAAIa,KAAK,GAAG,OAAO7D,QAAQ,KAAK,WAAW,GAAG5D,eAAe,GAAGC,SAAS;;AAEzE;AACA,MAAMyH,SAAS,GAAG;EAChB,GAAG3H;AACL,CAAC;AAED,SAAS4H,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAMC,GAAG,GAAG9H,KAAK,CAAC+H,MAAM,CAACF,KAAK,CAAC;EAC/BH,KAAK,CAAC,MAAM;IACVI,GAAG,CAACE,OAAO,GAAGH,KAAK;EACrB,CAAC,CAAC;EACF,OAAOC,GAAG;AACZ;AACA,MAAMG,kBAAkB,GAAGN,SAAS,CAACM,kBAAkB;AACvD,MAAMC,sBAAsB,GAAGD,kBAAkB,KAAKE,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC;AACjE,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAChC,MAAMP,GAAG,GAAG9H,KAAK,CAAC+H,MAAM,CAAC,MAAM;IAC7B,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;IAClE;EACF,CAAC,CAAC;EACFP,sBAAsB,CAAC,MAAM;IAC3BJ,GAAG,CAACE,OAAO,GAAGK,QAAQ;EACxB,CAAC,CAAC;EACF,OAAOrI,KAAK,CAAC0I,WAAW,CAAC,YAAY;IACnC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAAC1D,MAAM,EAAE2D,IAAI,GAAG,IAAIlI,KAAK,CAACgI,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;IAC9B;IACA,OAAOhB,GAAG,CAACE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,GAAG,CAACE,OAAO,CAAC,GAAGa,IAAI,CAAC;EAC5D,CAAC,EAAE,EAAE,CAAC;AACR;AAEA,SAASE,kBAAkBA,CAACrB,KAAK,EAAEsB,IAAI,EAAEC,OAAO,EAAE;EAChD,OAAOC,IAAI,CAAC/I,KAAK,CAACuH,KAAK,GAAGsB,IAAI,CAAC,KAAKC,OAAO;AAC7C;AACA,SAASE,sBAAsBA,CAACC,OAAO,EAAE1B,KAAK,EAAE;EAC9C,OAAOA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI0B,OAAO,CAACpB,OAAO,CAAC9C,MAAM;AACrD;AACA,SAASmE,eAAeA,CAACD,OAAO,EAAEE,eAAe,EAAE;EACjD,OAAOC,wBAAwB,CAACH,OAAO,EAAE;IACvCE;EACF,CAAC,CAAC;AACJ;AACA,SAASE,eAAeA,CAACJ,OAAO,EAAEE,eAAe,EAAE;EACjD,OAAOC,wBAAwB,CAACH,OAAO,EAAE;IACvCK,SAAS,EAAE,IAAI;IACfC,aAAa,EAAEN,OAAO,CAACpB,OAAO,CAAC9C,MAAM;IACrCoE;EACF,CAAC,CAAC;AACJ;AACA,SAASC,wBAAwBA,CAACH,OAAO,EAAEO,KAAK,EAAE;EAChD,IAAI;IACFD,aAAa,GAAG,CAAC,CAAC;IAClBD,SAAS,GAAG,KAAK;IACjBH,eAAe;IACfM,MAAM,GAAG;EACX,CAAC,GAAGD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,IAAIjC,KAAK,GAAGgC,aAAa;EACzB,GAAG;IACDhC,KAAK,IAAI+B,SAAS,GAAG,CAACG,MAAM,GAAGA,MAAM;EACvC,CAAC,QAAQlC,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI0B,OAAO,CAACpB,OAAO,CAAC9C,MAAM,GAAG,CAAC,IAAI2E,mBAAmB,CAACT,OAAO,EAAE1B,KAAK,EAAE4B,eAAe,CAAC;EAClH,OAAO5B,KAAK;AACd;AACA,SAASoC,qBAAqBA,CAACV,OAAO,EAAErI,IAAI,EAAE;EAC5C,IAAI;IACFmC,KAAK;IACL6G,WAAW;IACXC,IAAI;IACJC,GAAG;IACHjB,IAAI;IACJM,eAAe;IACfY,QAAQ;IACRC,QAAQ;IACRC,SAAS;IACT9D,SAAS,EAAE+D,IAAI,GAAG;EACpB,CAAC,GAAGtJ,IAAI;EACR,IAAIuJ,SAAS,GAAGF,SAAS;EACzB,IAAIlH,KAAK,CAACqH,GAAG,KAAKpI,QAAQ,EAAE;IAC1BkI,IAAI,IAAI/D,SAAS,CAACpD,KAAK,CAAC;IACxB,IAAIkH,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBE,SAAS,GAAGH,QAAQ;IACtB,CAAC,MAAM;MACLG,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;QAC5CM,aAAa,EAAEY,SAAS;QACxBV,MAAM,EAAEZ,IAAI;QACZS,SAAS,EAAE,IAAI;QACfH;MACF,CAAC,CAAC;MACF,IAAIU,IAAI,KAAKI,SAAS,GAAGpB,IAAI,GAAGkB,QAAQ,IAAII,SAAS,GAAG,CAAC,CAAC,EAAE;QAC1D,MAAME,GAAG,GAAGJ,SAAS,GAAGpB,IAAI;QAC5B,MAAMyB,MAAM,GAAGN,QAAQ,GAAGnB,IAAI;QAC9B,MAAM0B,MAAM,GAAGP,QAAQ,IAAIM,MAAM,GAAGD,GAAG,CAAC;QACxC,IAAIC,MAAM,KAAKD,GAAG,EAAE;UAClBF,SAAS,GAAGH,QAAQ;QACtB,CAAC,MAAM;UACLG,SAAS,GAAGG,MAAM,GAAGD,GAAG,GAAGE,MAAM,GAAGA,MAAM,GAAG1B,IAAI;QACnD;MACF;IACF;IACA,IAAIG,sBAAsB,CAACC,OAAO,EAAEkB,SAAS,CAAC,EAAE;MAC9CA,SAAS,GAAGF,SAAS;IACvB;EACF;EACA,IAAIlH,KAAK,CAACqH,GAAG,KAAKnI,UAAU,EAAE;IAC5BiI,IAAI,IAAI/D,SAAS,CAACpD,KAAK,CAAC;IACxB,IAAIkH,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBE,SAAS,GAAGJ,QAAQ;IACtB,CAAC,MAAM;MACLI,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;QAC5CM,aAAa,EAAEU,SAAS;QACxBR,MAAM,EAAEZ,IAAI;QACZM;MACF,CAAC,CAAC;MACF,IAAIU,IAAI,IAAII,SAAS,GAAGpB,IAAI,GAAGmB,QAAQ,EAAE;QACvCG,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;UAC5CM,aAAa,EAAEU,SAAS,GAAGpB,IAAI,GAAGA,IAAI;UACtCY,MAAM,EAAEZ,IAAI;UACZM;QACF,CAAC,CAAC;MACJ;IACF;IACA,IAAIH,sBAAsB,CAACC,OAAO,EAAEkB,SAAS,CAAC,EAAE;MAC9CA,SAAS,GAAGF,SAAS;IACvB;EACF;;EAEA;EACA,IAAIL,WAAW,KAAK,MAAM,EAAE;IAC1B,MAAMd,OAAO,GAAG9I,KAAK,CAACiK,SAAS,GAAGpB,IAAI,CAAC;IACvC,IAAI9F,KAAK,CAACqH,GAAG,MAAMN,GAAG,GAAGhI,UAAU,GAAGC,WAAW,CAAC,EAAE;MAClDmI,IAAI,IAAI/D,SAAS,CAACpD,KAAK,CAAC;MACxB,IAAIkH,SAAS,GAAGpB,IAAI,KAAKA,IAAI,GAAG,CAAC,EAAE;QACjCsB,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;UAC5CM,aAAa,EAAEU,SAAS;UACxBd;QACF,CAAC,CAAC;QACF,IAAIU,IAAI,IAAIjB,kBAAkB,CAACuB,SAAS,EAAEtB,IAAI,EAAEC,OAAO,CAAC,EAAE;UACxDqB,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;YAC5CM,aAAa,EAAEU,SAAS,GAAGA,SAAS,GAAGpB,IAAI,GAAG,CAAC;YAC/CM;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAIU,IAAI,EAAE;QACfM,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;UAC5CM,aAAa,EAAEU,SAAS,GAAGA,SAAS,GAAGpB,IAAI,GAAG,CAAC;UAC/CM;QACF,CAAC,CAAC;MACJ;MACA,IAAIP,kBAAkB,CAACuB,SAAS,EAAEtB,IAAI,EAAEC,OAAO,CAAC,EAAE;QAChDqB,SAAS,GAAGF,SAAS;MACvB;IACF;IACA,IAAIlH,KAAK,CAACqH,GAAG,MAAMN,GAAG,GAAG/H,WAAW,GAAGD,UAAU,CAAC,EAAE;MAClDoI,IAAI,IAAI/D,SAAS,CAACpD,KAAK,CAAC;MACxB,IAAIkH,SAAS,GAAGpB,IAAI,KAAK,CAAC,EAAE;QAC1BsB,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;UAC5CM,aAAa,EAAEU,SAAS;UACxBX,SAAS,EAAE,IAAI;UACfH;QACF,CAAC,CAAC;QACF,IAAIU,IAAI,IAAIjB,kBAAkB,CAACuB,SAAS,EAAEtB,IAAI,EAAEC,OAAO,CAAC,EAAE;UACxDqB,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;YAC5CM,aAAa,EAAEU,SAAS,IAAIpB,IAAI,GAAGoB,SAAS,GAAGpB,IAAI,CAAC;YACpDS,SAAS,EAAE,IAAI;YACfH;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAIU,IAAI,EAAE;QACfM,SAAS,GAAGf,wBAAwB,CAACH,OAAO,EAAE;UAC5CM,aAAa,EAAEU,SAAS,IAAIpB,IAAI,GAAGoB,SAAS,GAAGpB,IAAI,CAAC;UACpDS,SAAS,EAAE,IAAI;UACfH;QACF,CAAC,CAAC;MACJ;MACA,IAAIP,kBAAkB,CAACuB,SAAS,EAAEtB,IAAI,EAAEC,OAAO,CAAC,EAAE;QAChDqB,SAAS,GAAGF,SAAS;MACvB;IACF;IACA,MAAMO,OAAO,GAAGxK,KAAK,CAACgK,QAAQ,GAAGnB,IAAI,CAAC,KAAKC,OAAO;IAClD,IAAIE,sBAAsB,CAACC,OAAO,EAAEkB,SAAS,CAAC,EAAE;MAC9C,IAAIN,IAAI,IAAIW,OAAO,EAAE;QACnBL,SAAS,GAAGpH,KAAK,CAACqH,GAAG,MAAMN,GAAG,GAAG/H,WAAW,GAAGD,UAAU,CAAC,GAAGkI,QAAQ,GAAGZ,wBAAwB,CAACH,OAAO,EAAE;UACxGM,aAAa,EAAEU,SAAS,GAAGA,SAAS,GAAGpB,IAAI,GAAG,CAAC;UAC/CM;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLgB,SAAS,GAAGF,SAAS;MACvB;IACF;EACF;EACA,OAAOE,SAAS;AAClB;;AAEA;AACA,SAASM,iBAAiBA,CAACC,KAAK,EAAE7B,IAAI,EAAE8B,KAAK,EAAE;EAC7C,MAAMC,OAAO,GAAG,EAAE;EAClB,IAAIC,UAAU,GAAG,CAAC;EAClBH,KAAK,CAAC9E,OAAO,CAAC,CAACkF,KAAK,EAAEvD,KAAK,KAAK;IAC9B,IAAI;MACFR,KAAK;MACLC;IACF,CAAC,GAAG8D,KAAK;IACT,IAAI/D,KAAK,GAAG8B,IAAI,EAAE;MAChB,IAAIV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAM,IAAIC,KAAK,CAAC,oDAAoD,GAAGf,KAAK,GAAG,+BAA+B,CAAC;MACjH;IACF;IACA,IAAIwD,UAAU,GAAG,KAAK;IACtB,IAAIJ,KAAK,EAAE;MACTE,UAAU,GAAG,CAAC;IAChB;IACA,OAAO,CAACE,UAAU,EAAE;MAClB,MAAMC,WAAW,GAAG,EAAE;MACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlE,KAAK,EAAEkE,CAAC,EAAE,EAAE;QAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlE,MAAM,EAAEkE,CAAC,EAAE,EAAE;UAC/BF,WAAW,CAAC3D,IAAI,CAACwD,UAAU,GAAGI,CAAC,GAAGC,CAAC,GAAGrC,IAAI,CAAC;QAC7C;MACF;MACA,IAAIgC,UAAU,GAAGhC,IAAI,GAAG9B,KAAK,IAAI8B,IAAI,IAAImC,WAAW,CAACG,KAAK,CAACC,IAAI,IAAIR,OAAO,CAACQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;QACzFJ,WAAW,CAACpF,OAAO,CAACwF,IAAI,IAAI;UAC1BR,OAAO,CAACQ,IAAI,CAAC,GAAG7D,KAAK;QACvB,CAAC,CAAC;QACFwD,UAAU,GAAG,IAAI;MACnB,CAAC,MAAM;QACLF,UAAU,EAAE;MACd;IACF;EACF,CAAC,CAAC;;EAEF;EACA,OAAO,CAAC,GAAGD,OAAO,CAAC;AACrB;;AAEA;AACA,SAASS,wBAAwBA,CAAC9D,KAAK,EAAEmD,KAAK,EAAEE,OAAO,EAAE/B,IAAI,EAAEyC,MAAM,EAAE;EACrE,IAAI/D,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAC3B,MAAMgE,cAAc,GAAGX,OAAO,CAACY,OAAO,CAACjE,KAAK,CAAC;EAC7C,MAAMkE,QAAQ,GAAGf,KAAK,CAACnD,KAAK,CAAC;EAC7B,QAAQ+D,MAAM;IACZ,KAAK,IAAI;MACP,OAAOC,cAAc;IACvB,KAAK,IAAI;MACP,IAAI,CAACE,QAAQ,EAAE;QACb,OAAOF,cAAc;MACvB;MACA,OAAOA,cAAc,GAAGE,QAAQ,CAAC1E,KAAK,GAAG,CAAC;IAC5C,KAAK,IAAI;MACP,IAAI,CAAC0E,QAAQ,EAAE;QACb,OAAOF,cAAc;MACvB;MACA,OAAOA,cAAc,GAAG,CAACE,QAAQ,CAACzE,MAAM,GAAG,CAAC,IAAI6B,IAAI;IACtD,KAAK,IAAI;MACP,OAAO+B,OAAO,CAACc,WAAW,CAACnE,KAAK,CAAC;EACrC;AACF;;AAEA;AACA,SAASoE,kBAAkBA,CAACC,OAAO,EAAEhB,OAAO,EAAE;EAC5C,OAAOA,OAAO,CAACiB,OAAO,CAAC,CAACtE,KAAK,EAAEuE,SAAS,KAAKF,OAAO,CAACjK,QAAQ,CAAC4F,KAAK,CAAC,GAAG,CAACuE,SAAS,CAAC,GAAG,EAAE,CAAC;AAC1F;AACA,SAASpC,mBAAmBA,CAACT,OAAO,EAAE1B,KAAK,EAAE4B,eAAe,EAAE;EAC5D,IAAI,OAAOA,eAAe,KAAK,UAAU,EAAE;IACzC,OAAOA,eAAe,CAAC5B,KAAK,CAAC;EAC/B,CAAC,MAAM,IAAI4B,eAAe,EAAE;IAC1B,OAAOA,eAAe,CAACxH,QAAQ,CAAC4F,KAAK,CAAC;EACxC;EACA,MAAMjE,OAAO,GAAG2F,OAAO,CAACpB,OAAO,CAACN,KAAK,CAAC;EACtC,OAAOjE,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACY,YAAY,CAAC,UAAU,CAAC,IAAIZ,OAAO,CAACO,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;AAChH;AAEA,MAAMkI,kBAAkB,GAAGA,CAAA,MAAO;EAChCC,aAAa,EAAE,IAAI;EACnBC,YAAY;EACZ;EACA;EACA;EACA,OAAOC,cAAc,KAAK,UAAU,IAAIA,cAAc,CAACC,QAAQ,CAAC,CAAC,CAACxK,QAAQ,CAAC,eAAe,CAAC,GAAG,MAAM,GAAG;AACzG,CAAC,CAAC;AACF,SAASyK,aAAaA,CAACC,SAAS,EAAEC,GAAG,EAAE;EACrC,MAAMC,IAAI,GAAGtM,QAAQ,CAACoM,SAAS,EAAEN,kBAAkB,CAAC,CAAC,CAAC;EACtD,MAAMS,GAAG,GAAGD,IAAI,CAACxH,MAAM;EACvB,IAAIyH,GAAG,KAAK,CAAC,EAAE;EACf,MAAMC,MAAM,GAAGvK,aAAa,CAACsB,WAAW,CAAC6I,SAAS,CAAC,CAAC;EACpD,MAAM9E,KAAK,GAAGgF,IAAI,CAACf,OAAO,CAACiB,MAAM,CAAC;EAClC,MAAMtC,SAAS,GAAG5C,KAAK,KAAK,CAAC,CAAC,GAAG+E,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGE,GAAG,GAAG,CAAC,GAAGjF,KAAK,GAAG+E,GAAG;EACtE,OAAOC,IAAI,CAACpC,SAAS,CAAC;AACxB;AACA,SAASuC,eAAeA,CAACC,gBAAgB,EAAE;EACzC,OAAOP,aAAa,CAAC5I,WAAW,CAACmJ,gBAAgB,CAAC,CAACC,IAAI,EAAE,CAAC,CAAC,IAAID,gBAAgB;AACjF;AACA,SAASE,mBAAmBA,CAACF,gBAAgB,EAAE;EAC7C,OAAOP,aAAa,CAAC5I,WAAW,CAACmJ,gBAAgB,CAAC,CAACC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAID,gBAAgB;AAClF;AACA,SAASG,cAAcA,CAAC/J,KAAK,EAAEsJ,SAAS,EAAE;EACxC,MAAMU,gBAAgB,GAAGV,SAAS,IAAItJ,KAAK,CAACiK,aAAa;EACzD,MAAMC,aAAa,GAAGlK,KAAK,CAACkK,aAAa;EACzC,OAAO,CAACA,aAAa,IAAI,CAAC3K,QAAQ,CAACyK,gBAAgB,EAAEE,aAAa,CAAC;AACrE;AACA,SAASC,kBAAkBA,CAACb,SAAS,EAAE;EACrC,MAAMc,gBAAgB,GAAGlN,QAAQ,CAACoM,SAAS,EAAEN,kBAAkB,CAAC,CAAC,CAAC;EAClEoB,gBAAgB,CAACvH,OAAO,CAACtC,OAAO,IAAI;IAClCA,OAAO,CAAC8J,OAAO,CAACC,QAAQ,GAAG/J,OAAO,CAACO,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE;IACjEP,OAAO,CAACgK,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;EACxC,CAAC,CAAC;AACJ;AACA,SAASC,iBAAiBA,CAAClB,SAAS,EAAE;EACpC,MAAMmB,QAAQ,GAAGnB,SAAS,CAACoB,gBAAgB,CAAC,iBAAiB,CAAC;EAC9DD,QAAQ,CAAC5H,OAAO,CAACtC,OAAO,IAAI;IAC1B,MAAM+J,QAAQ,GAAG/J,OAAO,CAAC8J,OAAO,CAACC,QAAQ;IACzC,OAAO/J,OAAO,CAAC8J,OAAO,CAACC,QAAQ;IAC/B,IAAIA,QAAQ,EAAE;MACZ/J,OAAO,CAACgK,YAAY,CAAC,UAAU,EAAED,QAAQ,CAAC;IAC5C,CAAC,MAAM;MACL/J,OAAO,CAACoK,eAAe,CAAC,UAAU,CAAC;IACrC;EACF,CAAC,CAAC;AACJ;AAEA,SAASxL,aAAa,EAAEI,QAAQ,EAAEmI,iBAAiB,EAAEyC,kBAAkB,EAAEK,iBAAiB,EAAEnE,wBAAwB,EAAE/D,cAAc,EAAE7B,WAAW,EAAEQ,uBAAuB,EAAEqH,wBAAwB,EAAEM,kBAAkB,EAAEhC,qBAAqB,EAAEN,eAAe,EAAEH,eAAe,EAAEwD,eAAe,EAAE5G,gBAAgB,EAAE1B,eAAe,EAAElE,WAAW,EAAE2M,mBAAmB,EAAEd,kBAAkB,EAAEjJ,SAAS,EAAEvC,YAAY,EAAEa,SAAS,EAAEwH,kBAAkB,EAAE1F,mBAAmB,EAAE8F,sBAAsB,EAAEtH,OAAO,EAAEgI,mBAAmB,EAAEpI,KAAK,EAAE4F,sBAAsB,EAAE4F,cAAc,EAAExG,YAAY,EAAEjD,aAAa,EAAEpC,QAAQ,EAAE2C,kBAAkB,EAAED,iBAAiB,EAAE4C,cAAc,EAAEO,qBAAqB,EAAEhD,mBAAmB,EAAEqC,SAAS,EAAE8B,cAAc,EAAER,YAAY,EAAEF,KAAK,IAAIoG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}