{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addWeeks } from \"./addWeeks.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link eachWeekOfInterval} function options.\n */\n\n/**\n * The {@link eachWeekOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the interval start date,\n * then the end interval date. If a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of weeks from the week of the interval start to the week of the interval end\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport function eachWeekOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  let reversed = +start > +end;\n  const startDateWeek = reversed ? startOfWeek(end, options) : startOfWeek(start, options);\n  const endDateWeek = reversed ? startOfWeek(start, options) : startOfWeek(end, options);\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n  const endTime = +endDateWeek.getTime();\n  let currentDate = startDateWeek;\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+currentDate <= endTime) {\n    currentDate.setHours(0);\n    dates.push(constructFrom(start, currentDate));\n    currentDate = addWeeks(currentDate, step);\n    currentDate.setHours(15);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekOfInterval;", "map": {"version": 3, "names": ["normalizeInterval", "addWeeks", "constructFrom", "startOfWeek", "eachWeekOfInterval", "interval", "options", "start", "end", "in", "reversed", "startDateWeek", "endDateWeek", "setHours", "endTime", "getTime", "currentDate", "step", "dates", "push", "reverse"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/eachWeekOfInterval.js"], "sourcesContent": ["import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addWeeks } from \"./addWeeks.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link eachWeekOfInterval} function options.\n */\n\n/**\n * The {@link eachWeekOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the interval start date,\n * then the end interval date. If a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of weeks from the week of the interval start to the week of the interval end\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport function eachWeekOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const startDateWeek = reversed\n    ? startOfWeek(end, options)\n    : startOfWeek(start, options);\n  const endDateWeek = reversed\n    ? startOfWeek(start, options)\n    : startOfWeek(end, options);\n\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n\n  const endTime = +endDateWeek.getTime();\n  let currentDate = startDateWeek;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    currentDate.setHours(0);\n    dates.push(constructFrom(start, currentDate));\n    currentDate = addWeeks(currentDate, step);\n    currentDate.setHours(15);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekOfInterval;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACpD,MAAM;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAGR,iBAAiB,CAACM,OAAO,EAAEG,EAAE,EAAEJ,QAAQ,CAAC;EAE/D,IAAIK,QAAQ,GAAG,CAACH,KAAK,GAAG,CAACC,GAAG;EAC5B,MAAMG,aAAa,GAAGD,QAAQ,GAC1BP,WAAW,CAACK,GAAG,EAAEF,OAAO,CAAC,GACzBH,WAAW,CAACI,KAAK,EAAED,OAAO,CAAC;EAC/B,MAAMM,WAAW,GAAGF,QAAQ,GACxBP,WAAW,CAACI,KAAK,EAAED,OAAO,CAAC,GAC3BH,WAAW,CAACK,GAAG,EAAEF,OAAO,CAAC;EAE7BK,aAAa,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC1BD,WAAW,CAACC,QAAQ,CAAC,EAAE,CAAC;EAExB,MAAMC,OAAO,GAAG,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC;EACtC,IAAIC,WAAW,GAAGL,aAAa;EAE/B,IAAIM,IAAI,GAAGX,OAAO,EAAEW,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZP,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMQ,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACF,WAAW,IAAIF,OAAO,EAAE;IAC9BE,WAAW,CAACH,QAAQ,CAAC,CAAC,CAAC;IACvBK,KAAK,CAACC,IAAI,CAACjB,aAAa,CAACK,KAAK,EAAES,WAAW,CAAC,CAAC;IAC7CA,WAAW,GAAGf,QAAQ,CAACe,WAAW,EAAEC,IAAI,CAAC;IACzCD,WAAW,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC1B;EAEA,OAAOH,QAAQ,GAAGQ,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGF,KAAK;AAC3C;;AAEA;AACA,eAAed,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}