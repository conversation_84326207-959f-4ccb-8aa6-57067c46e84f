{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\admin\\\\CreateEmployee.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport Form from \"../../components/Form\";\nimport { CreateEmployeeValidation } from \"../../validation/CreateEmployeeValidation\";\nimport { useDispatch } from \"react-redux\";\nimport { createEmployee } from \"../../redux/slices/employeeSlice\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateEmployee = ({\n  onClose\n}) => {\n  _s();\n  const initialValues = {\n    firstName: \"\",\n    lastName: \"\",\n    username: \"\",\n    email: \"\",\n    gender: \"\",\n    phone: \"\",\n    cccd: \"\",\n    birth: \"\"\n  };\n  const dispatch = useDispatch();\n  const onSubmit = async data => {\n    try {\n      const res = await dispatch(createEmployee(data));\n      toast.success(\"Tạo nhân viên thành công!\");\n      onClose(false);\n    } catch (err) {\n      toast.error(\"Tạo thất bại!\");\n      return {\n        success: false\n      };\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-md mx-auto p-6 bg-white relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-bold mb-4\",\n      children: \"T\\u1EA1o nh\\xE2n vi\\xEAn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      onSubmit: onSubmit,\n      initialValues: initialValues,\n      btn: \"T\\u1EA1o m\\u1EDBi\",\n      validation: CreateEmployeeValidation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"button\",\n      className: \"w-full mt-4 px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300\",\n      onClick: () => onClose(false),\n      children: \"H\\u1EE7y\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 9\n  }, this);\n};\n_s(CreateEmployee, \"rgTLoBID190wEKCp9+G8W6F7A5M=\", false, function () {\n  return [useDispatch];\n});\n_c = CreateEmployee;\nexport default CreateEmployee;\nvar _c;\n$RefreshReg$(_c, \"CreateEmployee\");", "map": {"version": 3, "names": ["React", "Form", "CreateEmployeeValidation", "useDispatch", "createEmployee", "toast", "jsxDEV", "_jsxDEV", "CreateEmployee", "onClose", "_s", "initialValues", "firstName", "lastName", "username", "email", "gender", "phone", "cccd", "birth", "dispatch", "onSubmit", "data", "res", "success", "err", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "btn", "validation", "type", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/admin/CreateEmployee.js"], "sourcesContent": ["import React from \"react\";\r\nimport Form from \"../../components/Form\";\r\nimport { CreateEmployeeValidation } from \"../../validation/CreateEmployeeValidation\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { createEmployee } from \"../../redux/slices/employeeSlice\";\r\nimport { toast } from \"react-toastify\";\r\n\r\n\r\n\r\nconst CreateEmployee = ({ onClose }) => {\r\n    const initialValues = { firstName: \"\", lastName: \"\", username: \"\", email: \"\", gender: \"\", phone: \"\", cccd: \"\", birth: \"\" };\r\n    const dispatch = useDispatch();\r\n\r\n    const onSubmit = async (data) => {\r\n        try {\r\n            const res = await dispatch(createEmployee(data));\r\n            toast.success(\"Tạo nhân viên thành công!\");\r\n            onClose(false);\r\n        } catch (err) {\r\n            toast.error(\"Tạo thất bại!\");\r\n            return { success: false };\r\n        }\r\n    }\r\n\r\n    return (\r\n        <div className=\"max-w-md mx-auto p-6 bg-white relative\">\r\n            <h2 className=\"text-xl font-bold mb-4\">Tạo nhân viên</h2>\r\n\r\n            <Form onSubmit={onSubmit} initialValues={initialValues} btn=\"Tạo mới\" validation={CreateEmployeeValidation}  ></Form>\r\n            <button\r\n                type=\"button\"\r\n                className=\"w-full mt-4 px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300\"\r\n                onClick={() => onClose(false)}\r\n            >\r\n                Hủy\r\n            </button>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default CreateEmployee;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,uBAAuB;AACxC,SAASC,wBAAwB,QAAQ,2CAA2C;AACpF,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAIvC,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAMC,aAAa,GAAG;IAAEC,SAAS,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAG,CAAC;EAC1H,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC7B,IAAI;MACA,MAAMC,GAAG,GAAG,MAAMH,QAAQ,CAAChB,cAAc,CAACkB,IAAI,CAAC,CAAC;MAChDjB,KAAK,CAACmB,OAAO,CAAC,2BAA2B,CAAC;MAC1Cf,OAAO,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACVpB,KAAK,CAACqB,KAAK,CAAC,eAAe,CAAC;MAC5B,OAAO;QAAEF,OAAO,EAAE;MAAM,CAAC;IAC7B;EACJ,CAAC;EAED,oBACIjB,OAAA;IAAKoB,SAAS,EAAC,wCAAwC;IAAAC,QAAA,gBACnDrB,OAAA;MAAIoB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEzDzB,OAAA,CAACN,IAAI;MAACoB,QAAQ,EAAEA,QAAS;MAACV,aAAa,EAAEA,aAAc;MAACsB,GAAG,EAAC,mBAAS;MAACC,UAAU,EAAEhC;IAAyB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS,CAAC,eACrHzB,OAAA;MACI4B,IAAI,EAAC,QAAQ;MACbR,SAAS,EAAC,2EAA2E;MACrFS,OAAO,EAAEA,CAAA,KAAM3B,OAAO,CAAC,KAAK,CAAE;MAAAmB,QAAA,EACjC;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAAtB,EAAA,CA7BKF,cAAc;EAAA,QAECL,WAAW;AAAA;AAAAkC,EAAA,GAF1B7B,cAAc;AA+BpB,eAAeA,cAAc;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}