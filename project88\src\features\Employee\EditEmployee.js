import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import Form from "../../components/Form";
import { CreateEmployeeValidation } from "../../validation/CreateEmployeeValidation";
import EmployeeService from "./EmployeeService";

const EditEmployee = () => {
    const { userId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [employee, setEmployee] = useState(null);
    const [loading, setLoading] = useState(true);

    const initialValues = {
        firstName: employee?.firstName || "",
        lastName: employee?.lastName || "",
        username: employee?.username || "",
        email: employee?.email || "",
        gender: employee?.gender || "",
        phone: employee?.phone || "",
        cccd: employee?.cccd || "",
        birth: employee?.birth || ""
    };

    useEffect(() => {
        const fetchEmployee = async () => {
            try {
                setLoading(true);
                const response = await EmployeeService.getEmployeeById(userId);
                setEmployee(response.data);
            } catch (error) {
                toast.error("Không thể tải thông tin nhân viên!");
                navigate("/list-employees");
            } finally {
                setLoading(false);
            }
        };

        if (userId) {
            fetchEmployee();
        }
    }, [userId, navigate]);

    const onSubmit = async (data) => {
        try {
            await EmployeeService.updateEmployee(userId, data);
            toast.success("Cập nhật nhân viên thành công!");
            navigate("/list-employees");
        } catch (error) {
            toast.error("Cập nhật thất bại!");
            return { success: false };
        }
    };

    const handleCancel = () => {
        navigate("/list-employees");
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-lg">Đang tải...</div>
            </div>
        );
    }

    if (!employee) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-lg text-red-600">Không tìm thấy nhân viên!</div>
            </div>
        );
    }

    return (
        <div className="max-w-md mx-auto p-6 bg-white relative mt-10">
            <h2 className="text-xl font-bold mb-4">Chỉnh sửa nhân viên</h2>
            
            <Form 
                onSubmit={onSubmit} 
                initialValues={initialValues} 
                btn="Cập nhật" 
                validation={CreateEmployeeValidation}
            />
            
            <button
                type="button"
                className="w-full mt-4 px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                onClick={handleCancel}
            >
                Hủy
            </button>
        </div>
    );
};

export default EditEmployee;
