{"ast": null, "code": "export function addLeadingZeros(number, targetLength) {\n  const sign = number < 0 ? \"-\" : \"\";\n  const output = Math.abs(number).toString().padStart(targetLength, \"0\");\n  return sign + output;\n}", "map": {"version": 3, "names": ["addLeadingZeros", "number", "targetLength", "sign", "output", "Math", "abs", "toString", "padStart"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/_lib/addLeadingZeros.js"], "sourcesContent": ["export function addLeadingZeros(number, targetLength) {\n  const sign = number < 0 ? \"-\" : \"\";\n  const output = Math.abs(number).toString().padStart(targetLength, \"0\");\n  return sign + output;\n}\n"], "mappings": "AAAA,OAAO,SAASA,eAAeA,CAACC,MAAM,EAAEC,YAAY,EAAE;EACpD,MAAMC,IAAI,GAAGF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;EAClC,MAAMG,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACN,YAAY,EAAE,GAAG,CAAC;EACtE,OAAOC,IAAI,GAAGC,MAAM;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}