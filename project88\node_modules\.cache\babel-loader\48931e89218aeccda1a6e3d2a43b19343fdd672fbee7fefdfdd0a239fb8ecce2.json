{"ast": null, "code": "import { createAsyncThunk, createSlice } from \"@reduxjs/toolkit\";\nimport EmployeeService from \"../../features/employee/EmployeeService\";\nexport const getAllEmployees = createAsyncThunk('employees/getEmployees', async ({\n  page,\n  size,\n  filter\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const params = {\n      name: filter.name || null\n    };\n    const res = await EmployeeService.getAllEmployees(page, size, params);\n    return res.data;\n  } catch (error) {\n    return rejectWithValue(\"Unexpected response from server.\");\n  }\n});\nexport const getEmployeeByUsername = createAsyncThunk('employees/getEmployeeByUsername', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const res = await EmployeeService.getEmployeeByUsername();\n    return res.data;\n  } catch (error) {\n    return rejectWithValue(\"Unexpected response from server.\");\n  }\n});\nexport const createEmployee = createAsyncThunk('employees/createEmployee', async (body, {\n  rejectWithValue\n}) => {\n  try {\n    const res = await EmployeeService.createEmployee(body);\n    return res.data;\n  } catch (error) {\n    return rejectWithValue(\"Unexpected response from server.\");\n  }\n});\nconst employeeSlice = createSlice({\n  name: 'employee',\n  initialState: {\n    employees: [],\n    employeeByUsername: '',\n    totalPages: 0,\n    totalElements: 0,\n    currentPage: 0,\n    message: null,\n    loading: false,\n    error: null\n  },\n  reducers: {},\n  extraReducers: builder => {\n    builder.addCase(getAllEmployees.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(getAllEmployees.fulfilled, (state, action) => {\n      state.loading = false;\n      state.employees = action.payload.content;\n      state.totalElements = action.payload.totalElements;\n      state.currentPage = action.payload.currentPage;\n      state.totalPages = action.payload.totalPages;\n    }).addCase(getAllEmployees.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    }).addCase(getEmployeeByUsername.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(getEmployeeByUsername.fulfilled, (state, action) => {\n      state.loading = false;\n      state.employeeByUsername = action.payload;\n    }).addCase(getEmployeeByUsername.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    }).addCase(createEmployee.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(createEmployee.fulfilled, (state, action) => {\n      state.loading = false;\n      state.message = action.payload;\n    }).addCase(createEmployee.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    });\n  }\n});\nexport default employeeSlice.reducer;", "map": {"version": 3, "names": ["createAsyncThunk", "createSlice", "EmployeeService", "getAllEmployees", "page", "size", "filter", "rejectWithValue", "params", "name", "res", "data", "error", "getEmployeeByUsername", "_", "createEmployee", "body", "employeeSlice", "initialState", "employees", "employeeByUsername", "totalPages", "totalElements", "currentPage", "message", "loading", "reducers", "extraReducers", "builder", "addCase", "pending", "state", "fulfilled", "action", "payload", "content", "rejected", "reducer"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/redux/slices/employeeSlice.js"], "sourcesContent": ["import { createAsyncThunk, createSlice } from \"@reduxjs/toolkit\"\r\nimport EmployeeService from \"../../features/employee/EmployeeService\"\r\n\r\nexport const getAllEmployees = createAsyncThunk('employees/getEmployees', async ({ page, size, filter }, { rejectWithValue }) => {\r\n    try {\r\n        const params = { name: filter.name || null };\r\n        const res = await EmployeeService.getAllEmployees(page, size, params);\r\n\r\n        return res.data;\r\n    } catch (error) {\r\n        return rejectWithValue(\"Unexpected response from server.\");\r\n    }\r\n})\r\n\r\nexport const getEmployeeByUsername = createAsyncThunk('employees/getEmployeeByUsername', async (_, { rejectWithValue }) => {\r\n    try {\r\n        const res = await EmployeeService.getEmployeeByUsername();\r\n        return res.data;\r\n    } catch (error) {\r\n        return rejectWithValue(\"Unexpected response from server.\");\r\n    }\r\n})\r\n\r\nexport const createEmployee = createAsyncThunk('employees/createEmployee', async (body, { rejectWithValue }) => {\r\n    try {\r\n        const res = await EmployeeService.createEmployee(body);\r\n        return res.data\r\n    } catch (error) {\r\n        return rejectWithValue(\"Unexpected response from server.\");\r\n\r\n    }\r\n})\r\n\r\nconst employeeSlice = createSlice({\r\n    name: 'employee',\r\n    initialState: {\r\n        employees: [],\r\n        employeeByUsername: '',\r\n        totalPages: 0,\r\n        totalElements: 0,\r\n        currentPage: 0,\r\n        message: null,\r\n        loading: false,\r\n        error: null\r\n    },\r\n    reducers: {},\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(getAllEmployees.pending, (state) => {\r\n                state.loading = true;\r\n                state.error = null;\r\n            })\r\n            .addCase(getAllEmployees.fulfilled, (state, action) => {\r\n                state.loading = false;\r\n                state.employees = action.payload.content;\r\n                state.totalElements = action.payload.totalElements;\r\n                state.currentPage = action.payload.currentPage;\r\n                state.totalPages = action.payload.totalPages;\r\n            })\r\n            .addCase(getAllEmployees.rejected, (state, action) => {\r\n                state.loading = false;\r\n                state.error = action.payload;\r\n            })\r\n            .addCase(getEmployeeByUsername.pending, (state) => {\r\n                state.loading = true;\r\n                state.error = null;\r\n            })\r\n            .addCase(getEmployeeByUsername.fulfilled, (state, action) => {\r\n                state.loading = false;\r\n                state.employeeByUsername = action.payload;\r\n            })\r\n            .addCase(getEmployeeByUsername.rejected, (state, action) => {\r\n                state.loading = false;\r\n                state.error = action.payload;\r\n            })\r\n            .addCase(createEmployee.pending, (state) => {\r\n                state.loading = true;\r\n                state.error = null;\r\n            })\r\n            .addCase(createEmployee.fulfilled, (state, action) => {\r\n                state.loading = false;\r\n                state.message = action.payload;\r\n            })\r\n            .addCase(createEmployee.rejected, (state, action) => {\r\n                state.loading = false;\r\n                state.error = action.payload\r\n            })\r\n    }\r\n})\r\n\r\nexport default employeeSlice.reducer;"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;AAChE,OAAOC,eAAe,MAAM,yCAAyC;AAErE,OAAO,MAAMC,eAAe,GAAGH,gBAAgB,CAAC,wBAAwB,EAAE,OAAO;EAAEI,IAAI;EAAEC,IAAI;EAAEC;AAAO,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC7H,IAAI;IACA,MAAMC,MAAM,GAAG;MAAEC,IAAI,EAAEH,MAAM,CAACG,IAAI,IAAI;IAAK,CAAC;IAC5C,MAAMC,GAAG,GAAG,MAAMR,eAAe,CAACC,eAAe,CAACC,IAAI,EAAEC,IAAI,EAAEG,MAAM,CAAC;IAErE,OAAOE,GAAG,CAACC,IAAI;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOL,eAAe,CAAC,kCAAkC,CAAC;EAC9D;AACJ,CAAC,CAAC;AAEF,OAAO,MAAMM,qBAAqB,GAAGb,gBAAgB,CAAC,iCAAiC,EAAE,OAAOc,CAAC,EAAE;EAAEP;AAAgB,CAAC,KAAK;EACvH,IAAI;IACA,MAAMG,GAAG,GAAG,MAAMR,eAAe,CAACW,qBAAqB,CAAC,CAAC;IACzD,OAAOH,GAAG,CAACC,IAAI;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOL,eAAe,CAAC,kCAAkC,CAAC;EAC9D;AACJ,CAAC,CAAC;AAEF,OAAO,MAAMQ,cAAc,GAAGf,gBAAgB,CAAC,0BAA0B,EAAE,OAAOgB,IAAI,EAAE;EAAET;AAAgB,CAAC,KAAK;EAC5G,IAAI;IACA,MAAMG,GAAG,GAAG,MAAMR,eAAe,CAACa,cAAc,CAACC,IAAI,CAAC;IACtD,OAAON,GAAG,CAACC,IAAI;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOL,eAAe,CAAC,kCAAkC,CAAC;EAE9D;AACJ,CAAC,CAAC;AAEF,MAAMU,aAAa,GAAGhB,WAAW,CAAC;EAC9BQ,IAAI,EAAE,UAAU;EAChBS,YAAY,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,kBAAkB,EAAE,EAAE;IACtBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,KAAK;IACdb,KAAK,EAAE;EACX,CAAC;EACDc,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC1B,eAAe,CAAC2B,OAAO,EAAGC,KAAK,IAAK;MACzCA,KAAK,CAACN,OAAO,GAAG,IAAI;MACpBM,KAAK,CAACnB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDiB,OAAO,CAAC1B,eAAe,CAAC6B,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MACnDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACZ,SAAS,GAAGc,MAAM,CAACC,OAAO,CAACC,OAAO;MACxCJ,KAAK,CAACT,aAAa,GAAGW,MAAM,CAACC,OAAO,CAACZ,aAAa;MAClDS,KAAK,CAACR,WAAW,GAAGU,MAAM,CAACC,OAAO,CAACX,WAAW;MAC9CQ,KAAK,CAACV,UAAU,GAAGY,MAAM,CAACC,OAAO,CAACb,UAAU;IAChD,CAAC,CAAC,CACDQ,OAAO,CAAC1B,eAAe,CAACiC,QAAQ,EAAE,CAACL,KAAK,EAAEE,MAAM,KAAK;MAClDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACnB,KAAK,GAAGqB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC,CACDL,OAAO,CAAChB,qBAAqB,CAACiB,OAAO,EAAGC,KAAK,IAAK;MAC/CA,KAAK,CAACN,OAAO,GAAG,IAAI;MACpBM,KAAK,CAACnB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDiB,OAAO,CAAChB,qBAAqB,CAACmB,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MACzDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACX,kBAAkB,GAAGa,MAAM,CAACC,OAAO;IAC7C,CAAC,CAAC,CACDL,OAAO,CAAChB,qBAAqB,CAACuB,QAAQ,EAAE,CAACL,KAAK,EAAEE,MAAM,KAAK;MACxDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACnB,KAAK,GAAGqB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC,CACDL,OAAO,CAACd,cAAc,CAACe,OAAO,EAAGC,KAAK,IAAK;MACxCA,KAAK,CAACN,OAAO,GAAG,IAAI;MACpBM,KAAK,CAACnB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDiB,OAAO,CAACd,cAAc,CAACiB,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAClDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACP,OAAO,GAAGS,MAAM,CAACC,OAAO;IAClC,CAAC,CAAC,CACDL,OAAO,CAACd,cAAc,CAACqB,QAAQ,EAAE,CAACL,KAAK,EAAEE,MAAM,KAAK;MACjDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACnB,KAAK,GAAGqB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,eAAejB,aAAa,CAACoB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}