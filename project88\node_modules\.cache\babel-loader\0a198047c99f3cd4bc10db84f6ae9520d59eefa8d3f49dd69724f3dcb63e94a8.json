{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\components\\\\EditUserModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport Form from \"./Form\";\nimport { EditUserValidation } from \"../validation/EditUserValidatiion\";\nimport UserApi from \"../api/UserApi\";\nimport { debounce } from \"lodash\";\nimport { editUserByEmployee, getAllUsers } from \"../redux/slices/userSlice\";\nimport { useDispatch } from \"react-redux\";\nimport { getAllEmployees } from \"../redux/slices/employeeSlice\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditUserModal = ({\n  user,\n  onClose,\n  onSave\n}) => {\n  _s();\n  const initialValues = {\n    email: (user === null || user === void 0 ? void 0 : user.email) || \"\",\n    phone: (user === null || user === void 0 ? void 0 : user.phone) || \"\"\n  };\n  const dispatch = useDispatch();\n  const onSubmit = async dataForm => {\n    // 1. So sánh và lọc chỉ những trường thay đổi\n    const patchData = {};\n    for (const key in dataForm) {\n      if (dataForm[key] !== initialValues[key]) {\n        patchData[key] = dataForm[key];\n      }\n    }\n\n    // 2. Nếu không có trường nào thay đổi\n    if (Object.keys(patchData).length === 0) {\n      toast.info(\"Không có thay đổi nào để cập nhật.\");\n      return {\n        success: false\n      };\n    }\n    try {\n      // 3. Gửi chỉ dữ liệu thay đổi\n      await dispatch(editUserByEmployee({\n        userID: user === null || user === void 0 ? void 0 : user.userID,\n        body: patchData\n      }));\n      toast.success(\"Cập nhật thành công! Kiểm tra email để active nếu có thay đổi email\");\n      await dispatch(getAllUsers({\n        page: 1,\n        size: 5,\n        filter: {\n          name: \"\"\n        }\n      }));\n      await dispatch(getAllEmployees({\n        page: 1,\n        size: 5,\n        filter: {\n          name: \"\"\n        }\n      }));\n      onClose(false);\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error(\"Lỗi khi submit:\", error);\n      toast.error(\"Cập nhật thất bại!\");\n      return {\n        success: false\n      };\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-md mx-auto p-6 bg-white relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-bold mb-4\",\n      children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin ng\\u01B0\\u1EDDi d\\xF9ng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center bg-gray-100 rounded-lg mb-6 p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center font-bold text-orange-500 text-xl mr-4\",\n        children: \"A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-bold text-base\",\n          children: user === null || user === void 0 ? void 0 : user.fullName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-600 text-sm\",\n          children: [\"STK: \", user === null || user === void 0 ? void 0 : user.cardNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      onSubmit: onSubmit,\n      initialValues: initialValues,\n      btn: \"C\\u1EADp nh\\u1EADt\",\n      validation: EditUserValidation(user === null || user === void 0 ? void 0 : user.email, user === null || user === void 0 ? void 0 : user.phone)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"button\",\n      className: \"w-full mt-4 px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300\",\n      onClick: () => onClose(false),\n      children: \"H\\u1EE7y\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 9\n  }, this);\n};\n_s(EditUserModal, \"rgTLoBID190wEKCp9+G8W6F7A5M=\", false, function () {\n  return [useDispatch];\n});\n_c = EditUserModal;\nexport default EditUserModal;\nvar _c;\n$RefreshReg$(_c, \"EditUserModal\");", "map": {"version": 3, "names": ["React", "useState", "Form", "EditUserValidation", "UserApi", "debounce", "editUserByEmployee", "getAllUsers", "useDispatch", "getAllEmployees", "toast", "jsxDEV", "_jsxDEV", "EditUserModal", "user", "onClose", "onSave", "_s", "initialValues", "email", "phone", "dispatch", "onSubmit", "dataForm", "patchData", "key", "Object", "keys", "length", "info", "success", "userID", "body", "page", "size", "filter", "name", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullName", "cardNumber", "btn", "validation", "type", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/components/EditUserModal.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport Form from \"./Form\";\r\nimport { EditUserValidation } from \"../validation/EditUserValidatiion\";\r\nimport UserApi from \"../api/UserApi\";\r\nimport { debounce } from \"lodash\";\r\nimport { editUserByEmployee, getAllUsers } from \"../redux/slices/userSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { getAllEmployees } from \"../redux/slices/employeeSlice\";\r\nimport { toast } from \"react-toastify\";\r\n\r\n\r\n\r\nconst EditUserModal = ({ user, onClose, onSave }) => {\r\n\r\n    const initialValues = { email: user?.email || \"\", phone: user?.phone || \"\" };\r\n\r\n    const dispatch = useDispatch();\r\n\r\n\r\n    const onSubmit = async (dataForm) => {\r\n        // 1. So sánh và lọc chỉ những trường thay đổi\r\n        const patchData = {};\r\n        for (const key in dataForm) {\r\n            if (dataForm[key] !== initialValues[key]) {\r\n                patchData[key] = dataForm[key];\r\n            }\r\n        }\r\n\r\n        // 2. <PERSON>ế<PERSON> không có trường nào thay đổi\r\n        if (Object.keys(patchData).length === 0) {\r\n            toast.info(\"Không có thay đổi nào để cập nhật.\");\r\n            return { success: false };\r\n        }\r\n\r\n        try {\r\n            // 3. Gửi chỉ dữ liệu thay đổi\r\n            await dispatch(editUserByEmployee({ userID: user?.userID, body: patchData }));\r\n            toast.success(\"Cập nhật thành công! Kiểm tra email để active nếu có thay đổi email\");\r\n\r\n            await dispatch(getAllUsers({ page: 1, size: 5, filter: { name: \"\" } }));\r\n            await dispatch(getAllEmployees({ page: 1, size: 5, filter: { name: \"\" } }));\r\n\r\n            onClose(false);\r\n            return { success: true };\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi submit:\", error);\r\n            toast.error(\"Cập nhật thất bại!\");\r\n            return { success: false };\r\n        }\r\n    };\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"max-w-md mx-auto p-6 bg-white relative\">\r\n            <h2 className=\"text-xl font-bold mb-4\">Chỉnh sửa thông tin người dùng</h2>\r\n            <div className=\"flex items-center bg-gray-100 rounded-lg mb-6 p-4\">\r\n                <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center font-bold text-orange-500 text-xl mr-4\">\r\n                    A\r\n                </div>\r\n                <div>\r\n                    <div className=\"font-bold text-base\">{user?.fullName}</div>\r\n                    <div className=\"text-gray-600 text-sm\">STK: {user?.cardNumber}</div>\r\n                </div>\r\n            </div>\r\n            <Form onSubmit={onSubmit} initialValues={initialValues} btn=\"Cập nhật\" validation={EditUserValidation(user?.email, user?.phone)} ></Form>\r\n            <button\r\n                type=\"button\"\r\n                className=\"w-full mt-4 px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300\"\r\n                onClick={() => onClose(false)}\r\n            >\r\n                Hủy\r\n            </button>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default EditUserModal;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,OAAOC,OAAO,MAAM,gBAAgB;AACpC,SAASC,QAAQ,QAAQ,QAAQ;AACjC,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,2BAA2B;AAC3E,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAIvC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAEjD,MAAMC,aAAa,GAAG;IAAEC,KAAK,EAAE,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,KAAI,EAAE;IAAEC,KAAK,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,KAAK,KAAI;EAAG,CAAC;EAE5E,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAG9B,MAAMc,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACjC;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,KAAK,MAAMC,GAAG,IAAIF,QAAQ,EAAE;MACxB,IAAIA,QAAQ,CAACE,GAAG,CAAC,KAAKP,aAAa,CAACO,GAAG,CAAC,EAAE;QACtCD,SAAS,CAACC,GAAG,CAAC,GAAGF,QAAQ,CAACE,GAAG,CAAC;MAClC;IACJ;;IAEA;IACA,IAAIC,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;MACrClB,KAAK,CAACmB,IAAI,CAAC,oCAAoC,CAAC;MAChD,OAAO;QAAEC,OAAO,EAAE;MAAM,CAAC;IAC7B;IAEA,IAAI;MACA;MACA,MAAMT,QAAQ,CAACf,kBAAkB,CAAC;QAAEyB,MAAM,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,MAAM;QAAEC,IAAI,EAAER;MAAU,CAAC,CAAC,CAAC;MAC7Ed,KAAK,CAACoB,OAAO,CAAC,qEAAqE,CAAC;MAEpF,MAAMT,QAAQ,CAACd,WAAW,CAAC;QAAE0B,IAAI,EAAE,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE;UAAEC,IAAI,EAAE;QAAG;MAAE,CAAC,CAAC,CAAC;MACvE,MAAMf,QAAQ,CAACZ,eAAe,CAAC;QAAEwB,IAAI,EAAE,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE;UAAEC,IAAI,EAAE;QAAG;MAAE,CAAC,CAAC,CAAC;MAE3ErB,OAAO,CAAC,KAAK,CAAC;MACd,OAAO;QAAEe,OAAO,EAAE;MAAK,CAAC;IAC5B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC3B,KAAK,CAAC2B,KAAK,CAAC,oBAAoB,CAAC;MACjC,OAAO;QAAEP,OAAO,EAAE;MAAM,CAAC;IAC7B;EACJ,CAAC;EAID,oBACIlB,OAAA;IAAK2B,SAAS,EAAC,wCAAwC;IAAAC,QAAA,gBACnD5B,OAAA;MAAI2B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAA8B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1EhC,OAAA;MAAK2B,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAC9D5B,OAAA;QAAK2B,SAAS,EAAC,4GAA4G;QAAAC,QAAA,EAAC;MAE5H;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNhC,OAAA;QAAA4B,QAAA,gBACI5B,OAAA;UAAK2B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAE1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B;QAAQ;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3DhC,OAAA;UAAK2B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,OAAK,EAAC1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,UAAU;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNhC,OAAA,CAACV,IAAI;MAACoB,QAAQ,EAAEA,QAAS;MAACJ,aAAa,EAAEA,aAAc;MAAC6B,GAAG,EAAC,oBAAU;MAACC,UAAU,EAAE7C,kBAAkB,CAACW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,EAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,KAAK;IAAE;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACzIhC,OAAA;MACIqC,IAAI,EAAC,QAAQ;MACbV,SAAS,EAAC,2EAA2E;MACrFW,OAAO,EAAEA,CAAA,KAAMnC,OAAO,CAAC,KAAK,CAAE;MAAAyB,QAAA,EACjC;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAA3B,EAAA,CA/DKJ,aAAa;EAAA,QAIEL,WAAW;AAAA;AAAA2C,EAAA,GAJ1BtC,aAAa;AAiEnB,eAAeA,aAAa;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}