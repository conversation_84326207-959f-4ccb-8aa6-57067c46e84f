{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\home\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport Header from \"../../components/Header\";\nimport Footer from \"../../components/Footer\";\nimport UserContent from \"../user/UserContent\";\nimport EmployeeContent from \"../employee/EmployeeContent\";\nimport AdminContent from \"../admin/AdminContent\";\nimport { getUserRole } from \"../../utils/auth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function HomePage() {\n  _s();\n  const [theme, setTheme] = useState(\"light\");\n  const role = getUserRole();\n  useEffect(() => {\n    document.body.className = theme === \"light\" ? \"bg-gray-50\" : \"bg-gray-900 text-white\";\n  }, [theme]);\n  const toggleTheme = () => {\n    setTheme(prev => prev === \"light\" ? \"dark\" : \"light\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen\",\n    children: [role === 'ROLE_USER' && /*#__PURE__*/_jsxDEV(UserContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 17\n    }, this), role === 'ROLE_EMPLOYEE' && /*#__PURE__*/_jsxDEV(EmployeeContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 17\n    }, this), role === 'ROLE_ADMIN' && /*#__PURE__*/_jsxDEV(AdminContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n}\n_s(HomePage, \"lm84LOZxHN0YC4jzvAwAP/18Sno=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "Footer", "UserContent", "EmployeeContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getUserRole", "jsxDEV", "_jsxDEV", "HomePage", "_s", "theme", "setTheme", "role", "document", "body", "className", "toggleTheme", "prev", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport Header from \"../../components/Header\";\r\nimport Footer from \"../../components/Footer\";\r\nimport UserContent from \"../user/UserContent\";\r\nimport EmployeeContent from \"../employee/EmployeeContent\";\r\nimport AdminContent from \"../admin/AdminContent\";\r\nimport { getUserRole } from \"../../utils/auth\";\r\n\r\n\r\n\r\nexport default function HomePage() {\r\n    const [theme, setTheme] = useState(\"light\");\r\n    const role = getUserRole();\r\n\r\n    useEffect(() => {\r\n        document.body.className = theme === \"light\" ? \"bg-gray-50\" : \"bg-gray-900 text-white\";\r\n    }, [theme]);\r\n\r\n    const toggleTheme = () => {\r\n        setTheme((prev) => (prev === \"light\" ? \"dark\" : \"light\"));\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-screen\">\r\n            {role === 'ROLE_USER' && (\r\n                <UserContent />)\r\n            }\r\n            {role === 'ROLE_EMPLOYEE' && (\r\n                <EmployeeContent />)\r\n            }\r\n            {role === 'ROLE_ADMIN' && (\r\n                <AdminContent />)\r\n            }\r\n        </div>\r\n    );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI/C,eAAe,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,OAAO,CAAC;EAC3C,MAAMc,IAAI,GAAGP,WAAW,CAAC,CAAC;EAE1BN,SAAS,CAAC,MAAM;IACZc,QAAQ,CAACC,IAAI,CAACC,SAAS,GAAGL,KAAK,KAAK,OAAO,GAAG,YAAY,GAAG,wBAAwB;EACzF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACtBL,QAAQ,CAAEM,IAAI,IAAMA,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG,OAAQ,CAAC;EAC7D,CAAC;EAED,oBACIV,OAAA;IAAKQ,SAAS,EAAC,wBAAwB;IAAAG,QAAA,GAClCN,IAAI,KAAK,WAAW,iBACjBL,OAAA,CAACL,WAAW;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE,EAEnBV,IAAI,KAAK,eAAe,iBACrBL,OAAA,CAACJ,eAAe;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE,EAEvBV,IAAI,KAAK,YAAY,iBAClBL,OAAA,CAACH,YAAY;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEpB,CAAC;AAEd;AAACb,EAAA,CAzBuBD,QAAQ;AAAAe,EAAA,GAARf,QAAQ;AAAA,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}