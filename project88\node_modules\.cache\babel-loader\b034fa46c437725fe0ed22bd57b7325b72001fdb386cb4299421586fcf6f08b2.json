{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfSecond} function options.\n */\n\n/**\n * @name endOfSecond\n * @category Second Helpers\n * @summary Return the end of a second for the given date.\n *\n * @description\n * Return the end of a second for the given date.\n * The result will be in the local timezone if no `in` option is specified.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a second\n *\n * @example\n * // The end of a second for 1 December 2014 22:15:45.400:\n * const result = endOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.999\n */\nexport function endOfSecond(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMilliseconds(999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfSecond;", "map": {"version": 3, "names": ["toDate", "endOfSecond", "date", "options", "_date", "in", "setMilliseconds"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/endOfSecond.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfSecond} function options.\n */\n\n/**\n * @name endOfSecond\n * @category Second Helpers\n * @summary Return the end of a second for the given date.\n *\n * @description\n * Return the end of a second for the given date.\n * The result will be in the local timezone if no `in` option is specified.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a second\n *\n * @example\n * // The end of a second for 1 December 2014 22:15:45.400:\n * const result = endOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.999\n */\nexport function endOfSecond(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMilliseconds(999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfSecond;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACE,eAAe,CAAC,GAAG,CAAC;EAC1B,OAAOF,KAAK;AACd;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}