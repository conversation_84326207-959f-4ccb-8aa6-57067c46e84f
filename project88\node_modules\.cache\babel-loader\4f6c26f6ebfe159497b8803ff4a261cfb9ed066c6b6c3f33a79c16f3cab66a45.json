{"ast": null, "code": "// src/utils/env.ts\nvar NOTHING = Symbol.for(\"immer-nothing\");\nvar DRAFTABLE = Symbol.for(\"immer-draftable\");\nvar DRAFT_STATE = Symbol.for(\"immer-state\");\n\n// src/utils/errors.ts\nvar errors = process.env.NODE_ENV !== \"production\" ? [\n// All error codes, starting by 0:\nfunction (plugin) {\n  return `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`;\n}, function (thing) {\n  return `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`;\n}, \"This object has been frozen and should not be mutated\", function (data) {\n  return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + data;\n}, \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\", \"Immer forbids circular references\", \"The first or second argument to `produce` must be a function\", \"The third argument to `produce` must be a function or undefined\", \"First argument to `createDraft` must be a plain object, an array, or an immerable object\", \"First argument to `finishDraft` must be a draft returned by `createDraft`\", function (thing) {\n  return `'current' expects a draft, got: ${thing}`;\n}, \"Object.defineProperty() cannot be used on an Immer draft\", \"Object.setPrototypeOf() cannot be used on an Immer draft\", \"Immer only supports deleting array indices\", \"Immer only supports setting array indices and the 'length' property\", function (thing) {\n  return `'original' expects a draft, got: ${thing}`;\n}\n// Note: if more errors are added, the errorOffset in Patches.ts should be increased\n// See Patches.ts for additional errors\n] : [];\nfunction die(error) {\n  if (process.env.NODE_ENV !== \"production\") {\n    const e = errors[error];\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    const msg = typeof e === \"function\" ? e.apply(null, args) : e;\n    throw new Error(`[Immer] ${msg}`);\n  }\n  throw new Error(`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`);\n}\n\n// src/utils/common.ts\nvar getPrototypeOf = Object.getPrototypeOf;\nfunction isDraft(value) {\n  return !!value && !!value[DRAFT_STATE];\n}\nfunction isDraftable(value) {\n  if (!value) return false;\n  return isPlainObject(value) || Array.isArray(value) || !!value[DRAFTABLE] || !!value.constructor?.[DRAFTABLE] || isMap(value) || isSet(value);\n}\nvar objectCtorString = Object.prototype.constructor.toString();\nfunction isPlainObject(value) {\n  if (!value || typeof value !== \"object\") return false;\n  const proto = getPrototypeOf(value);\n  if (proto === null) {\n    return true;\n  }\n  const Ctor = Object.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  if (Ctor === Object) return true;\n  return typeof Ctor == \"function\" && Function.toString.call(Ctor) === objectCtorString;\n}\nfunction original(value) {\n  if (!isDraft(value)) die(15, value);\n  return value[DRAFT_STATE].base_;\n}\nfunction each(obj, iter) {\n  if (getArchtype(obj) === 0 /* Object */) {\n    Reflect.ownKeys(obj).forEach(key => {\n      iter(key, obj[key], obj);\n    });\n  } else {\n    obj.forEach((entry, index) => iter(index, entry, obj));\n  }\n}\nfunction getArchtype(thing) {\n  const state = thing[DRAFT_STATE];\n  return state ? state.type_ : Array.isArray(thing) ? 1 /* Array */ : isMap(thing) ? 2 /* Map */ : isSet(thing) ? 3 /* Set */ : 0 /* Object */;\n}\nfunction has(thing, prop) {\n  return getArchtype(thing) === 2 /* Map */ ? thing.has(prop) : Object.prototype.hasOwnProperty.call(thing, prop);\n}\nfunction get(thing, prop) {\n  return getArchtype(thing) === 2 /* Map */ ? thing.get(prop) : thing[prop];\n}\nfunction set(thing, propOrOldValue, value) {\n  const t = getArchtype(thing);\n  if (t === 2 /* Map */) thing.set(propOrOldValue, value);else if (t === 3 /* Set */) {\n    thing.add(value);\n  } else thing[propOrOldValue] = value;\n}\nfunction is(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\nfunction isMap(target) {\n  return target instanceof Map;\n}\nfunction isSet(target) {\n  return target instanceof Set;\n}\nfunction latest(state) {\n  return state.copy_ || state.base_;\n}\nfunction shallowCopy(base, strict) {\n  if (isMap(base)) {\n    return new Map(base);\n  }\n  if (isSet(base)) {\n    return new Set(base);\n  }\n  if (Array.isArray(base)) return Array.prototype.slice.call(base);\n  const isPlain = isPlainObject(base);\n  if (strict === true || strict === \"class_only\" && !isPlain) {\n    const descriptors = Object.getOwnPropertyDescriptors(base);\n    delete descriptors[DRAFT_STATE];\n    let keys = Reflect.ownKeys(descriptors);\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const desc = descriptors[key];\n      if (desc.writable === false) {\n        desc.writable = true;\n        desc.configurable = true;\n      }\n      if (desc.get || desc.set) descriptors[key] = {\n        configurable: true,\n        writable: true,\n        // could live with !!desc.set as well here...\n        enumerable: desc.enumerable,\n        value: base[key]\n      };\n    }\n    return Object.create(getPrototypeOf(base), descriptors);\n  } else {\n    const proto = getPrototypeOf(base);\n    if (proto !== null && isPlain) {\n      return {\n        ...base\n      };\n    }\n    const obj = Object.create(proto);\n    return Object.assign(obj, base);\n  }\n}\nfunction freeze(obj) {\n  let deep = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj;\n  if (getArchtype(obj) > 1) {\n    obj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections;\n  }\n  Object.freeze(obj);\n  if (deep) Object.entries(obj).forEach(_ref => {\n    let [key, value] = _ref;\n    return freeze(value, true);\n  });\n  return obj;\n}\nfunction dontMutateFrozenCollections() {\n  die(2);\n}\nfunction isFrozen(obj) {\n  return Object.isFrozen(obj);\n}\n\n// src/utils/plugins.ts\nvar plugins = {};\nfunction getPlugin(pluginKey) {\n  const plugin = plugins[pluginKey];\n  if (!plugin) {\n    die(0, pluginKey);\n  }\n  return plugin;\n}\nfunction loadPlugin(pluginKey, implementation) {\n  if (!plugins[pluginKey]) plugins[pluginKey] = implementation;\n}\n\n// src/core/scope.ts\nvar currentScope;\nfunction getCurrentScope() {\n  return currentScope;\n}\nfunction createScope(parent_, immer_) {\n  return {\n    drafts_: [],\n    parent_,\n    immer_,\n    // Whenever the modified draft contains a draft from another scope, we\n    // need to prevent auto-freezing so the unowned draft can be finalized.\n    canAutoFreeze_: true,\n    unfinalizedDrafts_: 0\n  };\n}\nfunction usePatchesInScope(scope, patchListener) {\n  if (patchListener) {\n    getPlugin(\"Patches\");\n    scope.patches_ = [];\n    scope.inversePatches_ = [];\n    scope.patchListener_ = patchListener;\n  }\n}\nfunction revokeScope(scope) {\n  leaveScope(scope);\n  scope.drafts_.forEach(revokeDraft);\n  scope.drafts_ = null;\n}\nfunction leaveScope(scope) {\n  if (scope === currentScope) {\n    currentScope = scope.parent_;\n  }\n}\nfunction enterScope(immer2) {\n  return currentScope = createScope(currentScope, immer2);\n}\nfunction revokeDraft(draft) {\n  const state = draft[DRAFT_STATE];\n  if (state.type_ === 0 /* Object */ || state.type_ === 1 /* Array */) state.revoke_();else state.revoked_ = true;\n}\n\n// src/core/finalize.ts\nfunction processResult(result, scope) {\n  scope.unfinalizedDrafts_ = scope.drafts_.length;\n  const baseDraft = scope.drafts_[0];\n  const isReplaced = result !== void 0 && result !== baseDraft;\n  if (isReplaced) {\n    if (baseDraft[DRAFT_STATE].modified_) {\n      revokeScope(scope);\n      die(4);\n    }\n    if (isDraftable(result)) {\n      result = finalize(scope, result);\n      if (!scope.parent_) maybeFreeze(scope, result);\n    }\n    if (scope.patches_) {\n      getPlugin(\"Patches\").generateReplacementPatches_(baseDraft[DRAFT_STATE].base_, result, scope.patches_, scope.inversePatches_);\n    }\n  } else {\n    result = finalize(scope, baseDraft, []);\n  }\n  revokeScope(scope);\n  if (scope.patches_) {\n    scope.patchListener_(scope.patches_, scope.inversePatches_);\n  }\n  return result !== NOTHING ? result : void 0;\n}\nfunction finalize(rootScope, value, path) {\n  if (isFrozen(value)) return value;\n  const state = value[DRAFT_STATE];\n  if (!state) {\n    each(value, (key, childValue) => finalizeProperty(rootScope, state, value, key, childValue, path));\n    return value;\n  }\n  if (state.scope_ !== rootScope) return value;\n  if (!state.modified_) {\n    maybeFreeze(rootScope, state.base_, true);\n    return state.base_;\n  }\n  if (!state.finalized_) {\n    state.finalized_ = true;\n    state.scope_.unfinalizedDrafts_--;\n    const result = state.copy_;\n    let resultEach = result;\n    let isSet2 = false;\n    if (state.type_ === 3 /* Set */) {\n      resultEach = new Set(result);\n      result.clear();\n      isSet2 = true;\n    }\n    each(resultEach, (key, childValue) => finalizeProperty(rootScope, state, result, key, childValue, path, isSet2));\n    maybeFreeze(rootScope, result, false);\n    if (path && rootScope.patches_) {\n      getPlugin(\"Patches\").generatePatches_(state, path, rootScope.patches_, rootScope.inversePatches_);\n    }\n  }\n  return state.copy_;\n}\nfunction finalizeProperty(rootScope, parentState, targetObject, prop, childValue, rootPath, targetIsSet) {\n  if (process.env.NODE_ENV !== \"production\" && childValue === targetObject) die(5);\n  if (isDraft(childValue)) {\n    const path = rootPath && parentState && parentState.type_ !== 3 /* Set */ &&\n    // Set objects are atomic since they have no keys.\n    !has(parentState.assigned_, prop) ? rootPath.concat(prop) : void 0;\n    const res = finalize(rootScope, childValue, path);\n    set(targetObject, prop, res);\n    if (isDraft(res)) {\n      rootScope.canAutoFreeze_ = false;\n    } else return;\n  } else if (targetIsSet) {\n    targetObject.add(childValue);\n  }\n  if (isDraftable(childValue) && !isFrozen(childValue)) {\n    if (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n      return;\n    }\n    finalize(rootScope, childValue);\n    if ((!parentState || !parentState.scope_.parent_) && typeof prop !== \"symbol\" && Object.prototype.propertyIsEnumerable.call(targetObject, prop)) maybeFreeze(rootScope, childValue);\n  }\n}\nfunction maybeFreeze(scope, value) {\n  let deep = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n    freeze(value, deep);\n  }\n}\n\n// src/core/proxy.ts\nfunction createProxyProxy(base, parent) {\n  const isArray = Array.isArray(base);\n  const state = {\n    type_: isArray ? 1 /* Array */ : 0 /* Object */,\n\n    // Track which produce call this is associated with.\n    scope_: parent ? parent.scope_ : getCurrentScope(),\n    // True for both shallow and deep changes.\n    modified_: false,\n    // Used during finalization.\n    finalized_: false,\n    // Track which properties have been assigned (true) or deleted (false).\n    assigned_: {},\n    // The parent draft state.\n    parent_: parent,\n    // The base state.\n    base_: base,\n    // The base proxy.\n    draft_: null,\n    // set below\n    // The base copy with any updated values.\n    copy_: null,\n    // Called by the `produce` function.\n    revoke_: null,\n    isManual_: false\n  };\n  let target = state;\n  let traps = objectTraps;\n  if (isArray) {\n    target = [state];\n    traps = arrayTraps;\n  }\n  const {\n    revoke,\n    proxy\n  } = Proxy.revocable(target, traps);\n  state.draft_ = proxy;\n  state.revoke_ = revoke;\n  return proxy;\n}\nvar objectTraps = {\n  get(state, prop) {\n    if (prop === DRAFT_STATE) return state;\n    const source = latest(state);\n    if (!has(source, prop)) {\n      return readPropFromProto(state, source, prop);\n    }\n    const value = source[prop];\n    if (state.finalized_ || !isDraftable(value)) {\n      return value;\n    }\n    if (value === peek(state.base_, prop)) {\n      prepareCopy(state);\n      return state.copy_[prop] = createProxy(value, state);\n    }\n    return value;\n  },\n  has(state, prop) {\n    return prop in latest(state);\n  },\n  ownKeys(state) {\n    return Reflect.ownKeys(latest(state));\n  },\n  set(state, prop, value) {\n    const desc = getDescriptorFromProto(latest(state), prop);\n    if (desc?.set) {\n      desc.set.call(state.draft_, value);\n      return true;\n    }\n    if (!state.modified_) {\n      const current2 = peek(latest(state), prop);\n      const currentState = current2?.[DRAFT_STATE];\n      if (currentState && currentState.base_ === value) {\n        state.copy_[prop] = value;\n        state.assigned_[prop] = false;\n        return true;\n      }\n      if (is(value, current2) && (value !== void 0 || has(state.base_, prop))) return true;\n      prepareCopy(state);\n      markChanged(state);\n    }\n    if (state.copy_[prop] === value && (\n    // special case: handle new props with value 'undefined'\n    value !== void 0 || prop in state.copy_) ||\n    // special case: NaN\n    Number.isNaN(value) && Number.isNaN(state.copy_[prop])) return true;\n    state.copy_[prop] = value;\n    state.assigned_[prop] = true;\n    return true;\n  },\n  deleteProperty(state, prop) {\n    if (peek(state.base_, prop) !== void 0 || prop in state.base_) {\n      state.assigned_[prop] = false;\n      prepareCopy(state);\n      markChanged(state);\n    } else {\n      delete state.assigned_[prop];\n    }\n    if (state.copy_) {\n      delete state.copy_[prop];\n    }\n    return true;\n  },\n  // Note: We never coerce `desc.value` into an Immer draft, because we can't make\n  // the same guarantee in ES5 mode.\n  getOwnPropertyDescriptor(state, prop) {\n    const owner = latest(state);\n    const desc = Reflect.getOwnPropertyDescriptor(owner, prop);\n    if (!desc) return desc;\n    return {\n      writable: true,\n      configurable: state.type_ !== 1 /* Array */ || prop !== \"length\",\n      enumerable: desc.enumerable,\n      value: owner[prop]\n    };\n  },\n  defineProperty() {\n    die(11);\n  },\n  getPrototypeOf(state) {\n    return getPrototypeOf(state.base_);\n  },\n  setPrototypeOf() {\n    die(12);\n  }\n};\nvar arrayTraps = {};\neach(objectTraps, (key, fn) => {\n  arrayTraps[key] = function () {\n    arguments[0] = arguments[0][0];\n    return fn.apply(this, arguments);\n  };\n});\narrayTraps.deleteProperty = function (state, prop) {\n  if (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop))) die(13);\n  return arrayTraps.set.call(this, state, prop, void 0);\n};\narrayTraps.set = function (state, prop, value) {\n  if (process.env.NODE_ENV !== \"production\" && prop !== \"length\" && isNaN(parseInt(prop))) die(14);\n  return objectTraps.set.call(this, state[0], prop, value, state[0]);\n};\nfunction peek(draft, prop) {\n  const state = draft[DRAFT_STATE];\n  const source = state ? latest(state) : draft;\n  return source[prop];\n}\nfunction readPropFromProto(state, source, prop) {\n  const desc = getDescriptorFromProto(source, prop);\n  return desc ? `value` in desc ? desc.value :\n  // This is a very special case, if the prop is a getter defined by the\n  // prototype, we should invoke it with the draft as context!\n  desc.get?.call(state.draft_) : void 0;\n}\nfunction getDescriptorFromProto(source, prop) {\n  if (!(prop in source)) return void 0;\n  let proto = getPrototypeOf(source);\n  while (proto) {\n    const desc = Object.getOwnPropertyDescriptor(proto, prop);\n    if (desc) return desc;\n    proto = getPrototypeOf(proto);\n  }\n  return void 0;\n}\nfunction markChanged(state) {\n  if (!state.modified_) {\n    state.modified_ = true;\n    if (state.parent_) {\n      markChanged(state.parent_);\n    }\n  }\n}\nfunction prepareCopy(state) {\n  if (!state.copy_) {\n    state.copy_ = shallowCopy(state.base_, state.scope_.immer_.useStrictShallowCopy_);\n  }\n}\n\n// src/core/immerClass.ts\nvar Immer2 = class {\n  constructor(config) {\n    var _this = this;\n    this.autoFreeze_ = true;\n    this.useStrictShallowCopy_ = false;\n    /**\n     * The `produce` function takes a value and a \"recipe function\" (whose\n     * return value often depends on the base state). The recipe function is\n     * free to mutate its first argument however it wants. All mutations are\n     * only ever applied to a __copy__ of the base state.\n     *\n     * Pass only a function to create a \"curried producer\" which relieves you\n     * from passing the recipe function every time.\n     *\n     * Only plain objects and arrays are made mutable. All other objects are\n     * considered uncopyable.\n     *\n     * Note: This function is __bound__ to its `Immer` instance.\n     *\n     * @param {any} base - the initial state\n     * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n     * @param {Function} patchListener - optional function that will be called with all the patches produced here\n     * @returns {any} a new state, or the initial state if nothing was modified\n     */\n    this.produce = (base, recipe, patchListener) => {\n      if (typeof base === \"function\" && typeof recipe !== \"function\") {\n        const defaultBase = recipe;\n        recipe = base;\n        const self = this;\n        return function curriedProduce() {\n          let base2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : defaultBase;\n          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n          return self.produce(base2, draft => recipe.call(this, draft, ...args));\n        };\n      }\n      if (typeof recipe !== \"function\") die(6);\n      if (patchListener !== void 0 && typeof patchListener !== \"function\") die(7);\n      let result;\n      if (isDraftable(base)) {\n        const scope = enterScope(this);\n        const proxy = createProxy(base, void 0);\n        let hasError = true;\n        try {\n          result = recipe(proxy);\n          hasError = false;\n        } finally {\n          if (hasError) revokeScope(scope);else leaveScope(scope);\n        }\n        usePatchesInScope(scope, patchListener);\n        return processResult(result, scope);\n      } else if (!base || typeof base !== \"object\") {\n        result = recipe(base);\n        if (result === void 0) result = base;\n        if (result === NOTHING) result = void 0;\n        if (this.autoFreeze_) freeze(result, true);\n        if (patchListener) {\n          const p = [];\n          const ip = [];\n          getPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip);\n          patchListener(p, ip);\n        }\n        return result;\n      } else die(1, base);\n    };\n    this.produceWithPatches = (base, recipe) => {\n      if (typeof base === \"function\") {\n        return function (state) {\n          for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n            args[_key3 - 1] = arguments[_key3];\n          }\n          return _this.produceWithPatches(state, draft => base(draft, ...args));\n        };\n      }\n      let patches, inversePatches;\n      const result = this.produce(base, recipe, (p, ip) => {\n        patches = p;\n        inversePatches = ip;\n      });\n      return [result, patches, inversePatches];\n    };\n    if (typeof config?.autoFreeze === \"boolean\") this.setAutoFreeze(config.autoFreeze);\n    if (typeof config?.useStrictShallowCopy === \"boolean\") this.setUseStrictShallowCopy(config.useStrictShallowCopy);\n  }\n  createDraft(base) {\n    if (!isDraftable(base)) die(8);\n    if (isDraft(base)) base = current(base);\n    const scope = enterScope(this);\n    const proxy = createProxy(base, void 0);\n    proxy[DRAFT_STATE].isManual_ = true;\n    leaveScope(scope);\n    return proxy;\n  }\n  finishDraft(draft, patchListener) {\n    const state = draft && draft[DRAFT_STATE];\n    if (!state || !state.isManual_) die(9);\n    const {\n      scope_: scope\n    } = state;\n    usePatchesInScope(scope, patchListener);\n    return processResult(void 0, scope);\n  }\n  /**\n   * Pass true to automatically freeze all copies created by Immer.\n   *\n   * By default, auto-freezing is enabled.\n   */\n  setAutoFreeze(value) {\n    this.autoFreeze_ = value;\n  }\n  /**\n   * Pass true to enable strict shallow copy.\n   *\n   * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n   */\n  setUseStrictShallowCopy(value) {\n    this.useStrictShallowCopy_ = value;\n  }\n  applyPatches(base, patches) {\n    let i;\n    for (i = patches.length - 1; i >= 0; i--) {\n      const patch = patches[i];\n      if (patch.path.length === 0 && patch.op === \"replace\") {\n        base = patch.value;\n        break;\n      }\n    }\n    if (i > -1) {\n      patches = patches.slice(i + 1);\n    }\n    const applyPatchesImpl = getPlugin(\"Patches\").applyPatches_;\n    if (isDraft(base)) {\n      return applyPatchesImpl(base, patches);\n    }\n    return this.produce(base, draft => applyPatchesImpl(draft, patches));\n  }\n};\nfunction createProxy(value, parent) {\n  const draft = isMap(value) ? getPlugin(\"MapSet\").proxyMap_(value, parent) : isSet(value) ? getPlugin(\"MapSet\").proxySet_(value, parent) : createProxyProxy(value, parent);\n  const scope = parent ? parent.scope_ : getCurrentScope();\n  scope.drafts_.push(draft);\n  return draft;\n}\n\n// src/core/current.ts\nfunction current(value) {\n  if (!isDraft(value)) die(10, value);\n  return currentImpl(value);\n}\nfunction currentImpl(value) {\n  if (!isDraftable(value) || isFrozen(value)) return value;\n  const state = value[DRAFT_STATE];\n  let copy;\n  if (state) {\n    if (!state.modified_) return state.base_;\n    state.finalized_ = true;\n    copy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_);\n  } else {\n    copy = shallowCopy(value, true);\n  }\n  each(copy, (key, childValue) => {\n    set(copy, key, currentImpl(childValue));\n  });\n  if (state) {\n    state.finalized_ = false;\n  }\n  return copy;\n}\n\n// src/plugins/patches.ts\nfunction enablePatches() {\n  const errorOffset = 16;\n  if (process.env.NODE_ENV !== \"production\") {\n    errors.push('Sets cannot have \"replace\" patches.', function (op) {\n      return \"Unsupported patch operation: \" + op;\n    }, function (path) {\n      return \"Cannot apply patch, path doesn't resolve: \" + path;\n    }, \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\");\n  }\n  const REPLACE = \"replace\";\n  const ADD = \"add\";\n  const REMOVE = \"remove\";\n  function generatePatches_(state, basePath, patches, inversePatches) {\n    switch (state.type_) {\n      case 0 /* Object */:\n      case 2 /* Map */:\n        return generatePatchesFromAssigned(state, basePath, patches, inversePatches);\n      case 1 /* Array */:\n        return generateArrayPatches(state, basePath, patches, inversePatches);\n      case 3 /* Set */:\n        return generateSetPatches(state, basePath, patches, inversePatches);\n    }\n  }\n  function generateArrayPatches(state, basePath, patches, inversePatches) {\n    let {\n      base_,\n      assigned_\n    } = state;\n    let copy_ = state.copy_;\n    if (copy_.length < base_.length) {\n      ;\n      [base_, copy_] = [copy_, base_];\n      [patches, inversePatches] = [inversePatches, patches];\n    }\n    for (let i = 0; i < base_.length; i++) {\n      if (assigned_[i] && copy_[i] !== base_[i]) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: REPLACE,\n          path,\n          // Need to maybe clone it, as it can in fact be the original value\n          // due to the base/copy inversion at the start of this function\n          value: clonePatchValueIfNeeded(copy_[i])\n        });\n        inversePatches.push({\n          op: REPLACE,\n          path,\n          value: clonePatchValueIfNeeded(base_[i])\n        });\n      }\n    }\n    for (let i = base_.length; i < copy_.length; i++) {\n      const path = basePath.concat([i]);\n      patches.push({\n        op: ADD,\n        path,\n        // Need to maybe clone it, as it can in fact be the original value\n        // due to the base/copy inversion at the start of this function\n        value: clonePatchValueIfNeeded(copy_[i])\n      });\n    }\n    for (let i = copy_.length - 1; base_.length <= i; --i) {\n      const path = basePath.concat([i]);\n      inversePatches.push({\n        op: REMOVE,\n        path\n      });\n    }\n  }\n  function generatePatchesFromAssigned(state, basePath, patches, inversePatches) {\n    const {\n      base_,\n      copy_\n    } = state;\n    each(state.assigned_, (key, assignedValue) => {\n      const origValue = get(base_, key);\n      const value = get(copy_, key);\n      const op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD;\n      if (origValue === value && op === REPLACE) return;\n      const path = basePath.concat(key);\n      patches.push(op === REMOVE ? {\n        op,\n        path\n      } : {\n        op,\n        path,\n        value\n      });\n      inversePatches.push(op === ADD ? {\n        op: REMOVE,\n        path\n      } : op === REMOVE ? {\n        op: ADD,\n        path,\n        value: clonePatchValueIfNeeded(origValue)\n      } : {\n        op: REPLACE,\n        path,\n        value: clonePatchValueIfNeeded(origValue)\n      });\n    });\n  }\n  function generateSetPatches(state, basePath, patches, inversePatches) {\n    let {\n      base_,\n      copy_\n    } = state;\n    let i = 0;\n    base_.forEach(value => {\n      if (!copy_.has(value)) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: REMOVE,\n          path,\n          value\n        });\n        inversePatches.unshift({\n          op: ADD,\n          path,\n          value\n        });\n      }\n      i++;\n    });\n    i = 0;\n    copy_.forEach(value => {\n      if (!base_.has(value)) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: ADD,\n          path,\n          value\n        });\n        inversePatches.unshift({\n          op: REMOVE,\n          path,\n          value\n        });\n      }\n      i++;\n    });\n  }\n  function generateReplacementPatches_(baseValue, replacement, patches, inversePatches) {\n    patches.push({\n      op: REPLACE,\n      path: [],\n      value: replacement === NOTHING ? void 0 : replacement\n    });\n    inversePatches.push({\n      op: REPLACE,\n      path: [],\n      value: baseValue\n    });\n  }\n  function applyPatches_(draft, patches) {\n    patches.forEach(patch => {\n      const {\n        path,\n        op\n      } = patch;\n      let base = draft;\n      for (let i = 0; i < path.length - 1; i++) {\n        const parentType = getArchtype(base);\n        let p = path[i];\n        if (typeof p !== \"string\" && typeof p !== \"number\") {\n          p = \"\" + p;\n        }\n        if ((parentType === 0 /* Object */ || parentType === 1 /* Array */) && (p === \"__proto__\" || p === \"constructor\")) die(errorOffset + 3);\n        if (typeof base === \"function\" && p === \"prototype\") die(errorOffset + 3);\n        base = get(base, p);\n        if (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"));\n      }\n      const type = getArchtype(base);\n      const value = deepClonePatchValue(patch.value);\n      const key = path[path.length - 1];\n      switch (op) {\n        case REPLACE:\n          switch (type) {\n            case 2 /* Map */:\n              return base.set(key, value);\n            case 3 /* Set */:\n              die(errorOffset);\n            default:\n              return base[key] = value;\n          }\n        case ADD:\n          switch (type) {\n            case 1 /* Array */:\n              return key === \"-\" ? base.push(value) : base.splice(key, 0, value);\n            case 2 /* Map */:\n              return base.set(key, value);\n            case 3 /* Set */:\n              return base.add(value);\n            default:\n              return base[key] = value;\n          }\n        case REMOVE:\n          switch (type) {\n            case 1 /* Array */:\n              return base.splice(key, 1);\n            case 2 /* Map */:\n              return base.delete(key);\n            case 3 /* Set */:\n              return base.delete(patch.value);\n            default:\n              return delete base[key];\n          }\n        default:\n          die(errorOffset + 1, op);\n      }\n    });\n    return draft;\n  }\n  function deepClonePatchValue(obj) {\n    if (!isDraftable(obj)) return obj;\n    if (Array.isArray(obj)) return obj.map(deepClonePatchValue);\n    if (isMap(obj)) return new Map(Array.from(obj.entries()).map(_ref2 => {\n      let [k, v] = _ref2;\n      return [k, deepClonePatchValue(v)];\n    }));\n    if (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue));\n    const cloned = Object.create(getPrototypeOf(obj));\n    for (const key in obj) cloned[key] = deepClonePatchValue(obj[key]);\n    if (has(obj, DRAFTABLE)) cloned[DRAFTABLE] = obj[DRAFTABLE];\n    return cloned;\n  }\n  function clonePatchValueIfNeeded(obj) {\n    if (isDraft(obj)) {\n      return deepClonePatchValue(obj);\n    } else return obj;\n  }\n  loadPlugin(\"Patches\", {\n    applyPatches_,\n    generatePatches_,\n    generateReplacementPatches_\n  });\n}\n\n// src/plugins/mapset.ts\nfunction enableMapSet() {\n  class DraftMap extends Map {\n    constructor(target, parent) {\n      super();\n      this[DRAFT_STATE] = {\n        type_: 2 /* Map */,\n        parent_: parent,\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        modified_: false,\n        finalized_: false,\n        copy_: void 0,\n        assigned_: void 0,\n        base_: target,\n        draft_: this,\n        isManual_: false,\n        revoked_: false\n      };\n    }\n    get size() {\n      return latest(this[DRAFT_STATE]).size;\n    }\n    has(key) {\n      return latest(this[DRAFT_STATE]).has(key);\n    }\n    set(key, value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!latest(state).has(key) || latest(state).get(key) !== value) {\n        prepareMapCopy(state);\n        markChanged(state);\n        state.assigned_.set(key, true);\n        state.copy_.set(key, value);\n        state.assigned_.set(key, true);\n      }\n      return this;\n    }\n    delete(key) {\n      if (!this.has(key)) {\n        return false;\n      }\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareMapCopy(state);\n      markChanged(state);\n      if (state.base_.has(key)) {\n        state.assigned_.set(key, false);\n      } else {\n        state.assigned_.delete(key);\n      }\n      state.copy_.delete(key);\n      return true;\n    }\n    clear() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (latest(state).size) {\n        prepareMapCopy(state);\n        markChanged(state);\n        state.assigned_ = /* @__PURE__ */new Map();\n        each(state.base_, key => {\n          state.assigned_.set(key, false);\n        });\n        state.copy_.clear();\n      }\n    }\n    forEach(cb, thisArg) {\n      const state = this[DRAFT_STATE];\n      latest(state).forEach((_value, key, _map) => {\n        cb.call(thisArg, this.get(key), key, this);\n      });\n    }\n    get(key) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      const value = latest(state).get(key);\n      if (state.finalized_ || !isDraftable(value)) {\n        return value;\n      }\n      if (value !== state.base_.get(key)) {\n        return value;\n      }\n      const draft = createProxy(value, state);\n      prepareMapCopy(state);\n      state.copy_.set(key, draft);\n      return draft;\n    }\n    keys() {\n      return latest(this[DRAFT_STATE]).keys();\n    }\n    values() {\n      const iterator = this.keys();\n      return {\n        [Symbol.iterator]: () => this.values(),\n        next: () => {\n          const r = iterator.next();\n          if (r.done) return r;\n          const value = this.get(r.value);\n          return {\n            done: false,\n            value\n          };\n        }\n      };\n    }\n    entries() {\n      const iterator = this.keys();\n      return {\n        [Symbol.iterator]: () => this.entries(),\n        next: () => {\n          const r = iterator.next();\n          if (r.done) return r;\n          const value = this.get(r.value);\n          return {\n            done: false,\n            value: [r.value, value]\n          };\n        }\n      };\n    }\n    [(DRAFT_STATE, Symbol.iterator)]() {\n      return this.entries();\n    }\n  }\n  function proxyMap_(target, parent) {\n    return new DraftMap(target, parent);\n  }\n  function prepareMapCopy(state) {\n    if (!state.copy_) {\n      state.assigned_ = /* @__PURE__ */new Map();\n      state.copy_ = new Map(state.base_);\n    }\n  }\n  class DraftSet extends Set {\n    constructor(target, parent) {\n      super();\n      this[DRAFT_STATE] = {\n        type_: 3 /* Set */,\n        parent_: parent,\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        modified_: false,\n        finalized_: false,\n        copy_: void 0,\n        base_: target,\n        draft_: this,\n        drafts_: /* @__PURE__ */new Map(),\n        revoked_: false,\n        isManual_: false\n      };\n    }\n    get size() {\n      return latest(this[DRAFT_STATE]).size;\n    }\n    has(value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!state.copy_) {\n        return state.base_.has(value);\n      }\n      if (state.copy_.has(value)) return true;\n      if (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value))) return true;\n      return false;\n    }\n    add(value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!this.has(value)) {\n        prepareSetCopy(state);\n        markChanged(state);\n        state.copy_.add(value);\n      }\n      return this;\n    }\n    delete(value) {\n      if (!this.has(value)) {\n        return false;\n      }\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      markChanged(state);\n      return state.copy_.delete(value) || (state.drafts_.has(value) ? state.copy_.delete(state.drafts_.get(value)) : (/* istanbul ignore next */\n      false));\n    }\n    clear() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (latest(state).size) {\n        prepareSetCopy(state);\n        markChanged(state);\n        state.copy_.clear();\n      }\n    }\n    values() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      return state.copy_.values();\n    }\n    entries() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      return state.copy_.entries();\n    }\n    keys() {\n      return this.values();\n    }\n    [(DRAFT_STATE, Symbol.iterator)]() {\n      return this.values();\n    }\n    forEach(cb, thisArg) {\n      const iterator = this.values();\n      let result = iterator.next();\n      while (!result.done) {\n        cb.call(thisArg, result.value, result.value, this);\n        result = iterator.next();\n      }\n    }\n  }\n  function proxySet_(target, parent) {\n    return new DraftSet(target, parent);\n  }\n  function prepareSetCopy(state) {\n    if (!state.copy_) {\n      state.copy_ = /* @__PURE__ */new Set();\n      state.base_.forEach(value => {\n        if (isDraftable(value)) {\n          const draft = createProxy(value, state);\n          state.drafts_.set(value, draft);\n          state.copy_.add(draft);\n        } else {\n          state.copy_.add(value);\n        }\n      });\n    }\n  }\n  function assertUnrevoked(state) {\n    if (state.revoked_) die(3, JSON.stringify(latest(state)));\n  }\n  loadPlugin(\"MapSet\", {\n    proxyMap_,\n    proxySet_\n  });\n}\n\n// src/immer.ts\nvar immer = new Immer2();\nvar produce = immer.produce;\nvar produceWithPatches = immer.produceWithPatches.bind(immer);\nvar setAutoFreeze = immer.setAutoFreeze.bind(immer);\nvar setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer);\nvar applyPatches = immer.applyPatches.bind(immer);\nvar createDraft = immer.createDraft.bind(immer);\nvar finishDraft = immer.finishDraft.bind(immer);\nfunction castDraft(value) {\n  return value;\n}\nfunction castImmutable(value) {\n  return value;\n}\nexport { Immer2 as Immer, applyPatches, castDraft, castImmutable, createDraft, current, enableMapSet, enablePatches, finishDraft, freeze, DRAFTABLE as immerable, isDraft, isDraftable, NOTHING as nothing, original, produce, produceWithPatches, setAutoFreeze, setUseStrictShallowCopy };", "map": {"version": 3, "names": ["NOTHING", "Symbol", "for", "DRAFTABLE", "DRAFT_STATE", "errors", "process", "env", "NODE_ENV", "plugin", "thing", "data", "die", "error", "e", "_len", "arguments", "length", "args", "Array", "_key", "msg", "apply", "Error", "getPrototypeOf", "Object", "isDraft", "value", "isDraftable", "isPlainObject", "isArray", "constructor", "isMap", "isSet", "objectCtorString", "prototype", "toString", "proto", "Ctor", "hasOwnProperty", "call", "Function", "original", "base_", "each", "obj", "iter", "getArchtype", "Reflect", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "state", "type_", "has", "prop", "get", "set", "propOrOldValue", "t", "add", "is", "x", "y", "target", "Map", "Set", "latest", "copy_", "shallowCopy", "base", "strict", "slice", "<PERSON><PERSON><PERSON>", "descriptors", "getOwnPropertyDescriptors", "keys", "i", "desc", "writable", "configurable", "enumerable", "create", "assign", "freeze", "deep", "undefined", "isFrozen", "clear", "delete", "dontMutateFrozenCollections", "entries", "_ref", "plugins", "getPlugin", "pluginKey", "loadPlugin", "implementation", "currentScope", "getCurrentScope", "createScope", "parent_", "immer_", "drafts_", "canAutoFreeze_", "unfinalizedDrafts_", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "revokeDraft", "enterScope", "immer2", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "rootScope", "path", "childValue", "finalizeProperty", "scope_", "finalized_", "resultEach", "isSet2", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "assigned_", "concat", "res", "autoFreeze_", "propertyIsEnumerable", "createProxyProxy", "parent", "draft_", "isManual_", "traps", "objectTraps", "arrayTraps", "revoke", "proxy", "Proxy", "revocable", "source", "readPropFromProto", "peek", "prepareCopy", "createProxy", "getDescriptorFromProto", "current2", "currentState", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "deleteProperty", "getOwnPropertyDescriptor", "owner", "defineProperty", "setPrototypeOf", "fn", "parseInt", "useStrictShallowCopy_", "Immer2", "config", "_this", "produce", "recipe", "defaultBase", "self", "curriedProduce", "base2", "_len2", "_key2", "<PERSON><PERSON><PERSON><PERSON>", "p", "ip", "produceWithPatches", "_len3", "_key3", "patches", "inversePatches", "autoFreeze", "setAutoFreeze", "useStrictShallowCopy", "setUseStrictShallowCopy", "createDraft", "current", "finishDraft", "applyPatches", "patch", "op", "applyPatchesImpl", "applyPatches_", "proxyMap_", "proxySet_", "push", "currentImpl", "copy", "enablePatches", "errorOffset", "REPLACE", "ADD", "REMOVE", "basePath", "generatePatchesFromAssigned", "generateArrayPatches", "generateSetPatches", "clonePatchValueIfNeeded", "assignedValue", "origValue", "unshift", "baseValue", "replacement", "parentType", "join", "type", "deepClonePatchValue", "splice", "map", "from", "_ref2", "k", "v", "cloned", "enableMapSet", "DraftMap", "size", "assertUnrevoked", "prepareMapCopy", "cb", "thisArg", "_value", "_map", "values", "iterator", "next", "r", "done", "DraftSet", "prepareSetCopy", "JSON", "stringify", "immer", "bind", "castDraft", "castImmutable"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\utils\\env.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\utils\\errors.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\utils\\common.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\utils\\plugins.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\core\\scope.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\core\\finalize.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\core\\proxy.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\core\\immerClass.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\core\\current.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\plugins\\patches.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\plugins\\mapset.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\node_modules\\@reduxjs\\toolkit\\node_modules\\immer\\src\\immer.ts"], "sourcesContent": ["// Should be no imports here!\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: unique symbol = Symbol.for(\"immer-nothing\")\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = Symbol.for(\"immer-draftable\")\n\nexport const DRAFT_STATE: unique symbol = Symbol.for(\"immer-state\")\n", "export const errors =\n\tprocess.env.NODE_ENV !== \"production\"\n\t\t? [\n\t\t\t\t// All error codes, starting by 0:\n\t\t\t\tfunction(plugin: string) {\n\t\t\t\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t\t\t\t},\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t\t\t\t},\n\t\t\t\t\"This object has been frozen and should not be mutated\",\n\t\t\t\tfunction(data: any) {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\t\t\t\tdata\n\t\t\t\t\t)\n\t\t\t\t},\n\t\t\t\t\"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t\t\t\t\"Immer forbids circular references\",\n\t\t\t\t\"The first or second argument to `produce` must be a function\",\n\t\t\t\t\"The third argument to `produce` must be a function or undefined\",\n\t\t\t\t\"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t\t\t\t\"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'current' expects a draft, got: ${thing}`\n\t\t\t\t},\n\t\t\t\t\"Object.defineProperty() cannot be used on an Immer draft\",\n\t\t\t\t\"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t\t\t\t\"Immer only supports deleting array indices\",\n\t\t\t\t\"Immer only supports setting array indices and the 'length' property\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'original' expects a draft, got: ${thing}`\n\t\t\t\t}\n\t\t\t\t// Note: if more errors are added, the errorOffset in Patches.ts should be increased\n\t\t\t\t// See Patches.ts for additional errors\n\t\t  ]\n\t\t: []\n\nexport function die(error: number, ...args: any[]): never {\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\tconst e = errors[error]\n\t\tconst msg = typeof e === \"function\" ? e.apply(null, args as any) : e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\tArchType,\n\tdie,\n\tStrictMode\n} from \"../internal\"\n\nexport const getPrototypeOf = Object.getPrototypeOf\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(15, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/**\n * Each iterates a map, set or array.\n * Or, if any other kind of object, all of its own properties.\n * Regardless whether they are enumerable or symbols\n */\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void\n): void\nexport function each(obj: any, iter: any) {\n\tif (getArchtype(obj) === ArchType.Object) {\n\t\tReflect.ownKeys(obj).forEach(key => {\n\t\t\titer(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): ArchType {\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_\n\t\t: Array.isArray(thing)\n\t\t? ArchType.Array\n\t\t: isMap(thing)\n\t\t? ArchType.Map\n\t\t: isSet(thing)\n\t\t? ArchType.Set\n\t\t: ArchType.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === ArchType.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === ArchType.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === ArchType.Map) thing.set(propOrOldValue, value)\n\telse if (t === ArchType.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any, strict: StrictMode) {\n\tif (isMap(base)) {\n\t\treturn new Map(base)\n\t}\n\tif (isSet(base)) {\n\t\treturn new Set(base)\n\t}\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\n\tconst isPlain = isPlainObject(base)\n\n\tif (strict === true || (strict === \"class_only\" && !isPlain)) {\n\t\t// Perform a strict copy\n\t\tconst descriptors = Object.getOwnPropertyDescriptors(base)\n\t\tdelete descriptors[DRAFT_STATE as any]\n\t\tlet keys = Reflect.ownKeys(descriptors)\n\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\tconst key: any = keys[i]\n\t\t\tconst desc = descriptors[key]\n\t\t\tif (desc.writable === false) {\n\t\t\t\tdesc.writable = true\n\t\t\t\tdesc.configurable = true\n\t\t\t}\n\t\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t\t// with libraries that trap values, like mobx or vue\n\t\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\t\tif (desc.get || desc.set)\n\t\t\t\tdescriptors[key] = {\n\t\t\t\t\tconfigurable: true,\n\t\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\t\tvalue: base[key]\n\t\t\t\t}\n\t\t}\n\t\treturn Object.create(getPrototypeOf(base), descriptors)\n\t} else {\n\t\t// perform a sloppy copy\n\t\tconst proto = getPrototypeOf(base)\n\t\tif (proto !== null && isPlain) {\n\t\t\treturn {...base} // assumption: better inner class optimization than the assign below\n\t\t}\n\t\tconst obj = Object.create(proto)\n\t\treturn Object.assign(obj, base)\n\t}\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep)\n\t\t// See #590, don't recurse into non-enumerable / Symbol properties when freezing\n\t\t// So use Object.entries (only string-like, enumerables) instead of each()\n\t\tObject.entries(obj).forEach(([key, value]) => freeze(value, true))\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\tDrafted,\n\tImmerB<PERSON>State,\n\tAnyMap,\n\tAnySet,\n\tArchType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: readonly Patch[]): T\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(0, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ArchType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ArchType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\t<PERSON>L<PERSON>ener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tArchType,\n\tgetPlugin\n} from \"../internal\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (state.type_ === ArchType.Object || state.type_ === ArchType.Array)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tI<PERSON>Scope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchP<PERSON>,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tArchType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(value, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path)\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result = state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ArchType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (process.env.NODE_ENV !== \"production\" && childValue === targetObject)\n\t\tdie(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ArchType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// Immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\t// Per #590, we never freeze symbolic properties. Just to make sure don't accidentally interfere\n\t\t// with other frameworks.\n\t\tif (\n\t\t\t(!parentState || !parentState.scope_.parent_) &&\n\t\t\ttypeof prop !== \"symbol\" &&\n\t\t\tObject.prototype.propertyIsEnumerable.call(targetObject, prop)\n\t\t)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tgetPrototypeOf,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tArchType,\n\tImmerScope\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ArchType.Object\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ArchType.Array\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ArchType.Array : (ArchType.Object as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(value, state))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\tif (state.copy_) {\n\t\t\tdelete state.copy_[prop]\n\t\t}\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ArchType.Array || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop as any)))\n\t\tdie(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (\n\t\tprocess.env.NODE_ENV !== \"production\" &&\n\t\tprop !== \"length\" &&\n\t\tisNaN(parseInt(prop as any))\n\t)\n\t\tdie(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {\n\tbase_: any\n\tcopy_: any\n\tscope_: ImmerScope\n}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(\n\t\t\tstate.base_,\n\t\t\tstate.scope_.immer_.useStrictShallowCopy_\n\t\t)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessResult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport type StrictMode = boolean | \"class_only\";\n\nexport class Immer implements ProducersFns {\n\tautoFreeze_: boolean = true\n\tuseStrictShallowCopy_: StrictMode = false\n\n\tconstructor(config?: {\n\t\tautoFreeze?: boolean\n\t\tuseStrictShallowCopy?: StrictMode\n\t}) {\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t\tif (typeof config?.useStrictShallowCopy === \"boolean\")\n\t\t\tthis.setUseStrictShallowCopy(config!.useStrictShallowCopy)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(1, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (!state || !state.isManual_) die(9)\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to enable strict shallow copy.\n\t *\n\t * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n\t */\n\tsetUseStrictShallowCopy(value: StrictMode) {\n\t\tthis.useStrictShallowCopy_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: readonly Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: createProxyProxy(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tisFrozen\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(10, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value) || isFrozen(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tif (state) {\n\t\tif (!state.modified_) return state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_)\n\t} else {\n\t\tcopy = shallowCopy(value, true)\n\t}\n\t// recurse\n\teach(copy, (key, childValue) => {\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\tif (state) {\n\t\tstate.finalized_ = false\n\t}\n\treturn copy\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tProxyArrayState,\n\tMapState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tgetPrototypeOf,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tArchType,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING,\n\terrors\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst errorOffset = 16\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\terrors.push(\n\t\t\t'Sets cannot have \"replace\" patches.',\n\t\t\tfunction(op: string) {\n\t\t\t\treturn \"Unsupported patch operation: \" + op\n\t\t\t},\n\t\t\tfunction(path: string) {\n\t\t\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t\t\t},\n\t\t\t\"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n\t\t)\n\t}\n\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ArchType.Object:\n\t\t\tcase ArchType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ArchType.Array:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ArchType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tfor (let i = copy_.length - 1; base_.length <= i; --i) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tinversePatches.push({\n\t\t\t\top: REMOVE,\n\t\t\t\tpath\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: readonly Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === ArchType.Object || parentType === ArchType.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\")\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\tdie(errorOffset)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(errorOffset + 1, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tdie,\n\tArchType,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\tclass DraftMap extends Map {\n\t\t[DRAFT_STATE]: MapState\n\n\t\tconstructor(target: AnyMap, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tset(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tforEach(cb: (value: any, key: any, self: any) => void, thisArg?: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tget(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.entries()\n\t\t}\n\t}\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tclass DraftSet extends Set {\n\t\t[DRAFT_STATE]: SetState\n\t\tconstructor(target: AnySet, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tadd(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tforEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\t}\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tWritableDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\tProducer,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze,\n\tObjectish,\n\tStrictMode\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to enable strict shallow copy.\n *\n * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n */\nexport const setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\n"], "mappings": ";AAKO,IAAMA,OAAA,GAAyBC,MAAA,CAAOC,GAAA,CAAI,eAAe;AAUzD,IAAMC,SAAA,GAA2BF,MAAA,CAAOC,GAAA,CAAI,iBAAiB;AAE7D,IAAME,WAAA,GAA6BH,MAAA,CAAOC,GAAA,CAAI,aAAa;;;ACjB3D,IAAMG,MAAA,GACZC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eACtB;AAAA;AAEA,UAASC,MAAA,EAAgB;EACxB,OAAO,mBAAmBA,MAAA,mFAAyFA,MAAA;AACpH,GACA,UAASC,KAAA,EAAe;EACvB,OAAO,sJAAsJA,KAAA;AAC9J,GACA,yDACA,UAASC,IAAA,EAAW;EACnB,OACC,yHACAA,IAAA;AAEF,GACA,qHACA,qCACA,gEACA,mEACA,4FACA,6EACA,UAASD,KAAA,EAAe;EACvB,OAAO,mCAAmCA,KAAA;AAC3C,GACA,4DACA,4DACA,8CACA,uEACA,UAASA,KAAA,EAAe;EACvB,OAAO,oCAAoCA,KAAA;AAC5C;AAAA;AAAA;AAAA,CAGA,GACA,EAAC;AAEE,SAASE,IAAIC,KAAA,EAAsC;EACzD,IAAIP,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IAC1C,MAAMM,CAAA,GAAIT,MAAA,CAAOQ,KAAK;IAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAFcC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAGpC,MAAMC,GAAA,GAAM,OAAOP,CAAA,KAAM,aAAaA,CAAA,CAAEQ,KAAA,CAAM,MAAMJ,IAAW,IAAIJ,CAAA;IACnE,MAAM,IAAIS,KAAA,CAAM,WAAWF,GAAA,EAAK;EACjC;EACA,MAAM,IAAIE,KAAA,CACT,8BAA8BV,KAAA,yCAC/B;AACD;;;ACjCO,IAAMW,cAAA,GAAiBC,MAAA,CAAOD,cAAA;AAI9B,SAASE,QAAQC,KAAA,EAAqB;EAC5C,OAAO,CAAC,CAACA,KAAA,IAAS,CAAC,CAACA,KAAA,CAAMvB,WAAW;AACtC;AAIO,SAASwB,YAAYD,KAAA,EAAqB;EAChD,IAAI,CAACA,KAAA,EAAO,OAAO;EACnB,OACCE,aAAA,CAAcF,KAAK,KACnBR,KAAA,CAAMW,OAAA,CAAQH,KAAK,KACnB,CAAC,CAACA,KAAA,CAAMxB,SAAS,KACjB,CAAC,CAACwB,KAAA,CAAMI,WAAA,GAAc5B,SAAS,KAC/B6B,KAAA,CAAML,KAAK,KACXM,KAAA,CAAMN,KAAK;AAEb;AAEA,IAAMO,gBAAA,GAAmBT,MAAA,CAAOU,SAAA,CAAUJ,WAAA,CAAYK,QAAA,CAAS;AAExD,SAASP,cAAcF,KAAA,EAAqB;EAClD,IAAI,CAACA,KAAA,IAAS,OAAOA,KAAA,KAAU,UAAU,OAAO;EAChD,MAAMU,KAAA,GAAQb,cAAA,CAAeG,KAAK;EAClC,IAAIU,KAAA,KAAU,MAAM;IACnB,OAAO;EACR;EACA,MAAMC,IAAA,GACLb,MAAA,CAAOc,cAAA,CAAeC,IAAA,CAAKH,KAAA,EAAO,aAAa,KAAKA,KAAA,CAAMN,WAAA;EAE3D,IAAIO,IAAA,KAASb,MAAA,EAAQ,OAAO;EAE5B,OACC,OAAOa,IAAA,IAAQ,cACfG,QAAA,CAASL,QAAA,CAASI,IAAA,CAAKF,IAAI,MAAMJ,gBAAA;AAEnC;AAKO,SAASQ,SAASf,KAAA,EAA0B;EAClD,IAAI,CAACD,OAAA,CAAQC,KAAK,GAAGf,GAAA,CAAI,IAAIe,KAAK;EAClC,OAAOA,KAAA,CAAMvB,WAAW,EAAEuC,KAAA;AAC3B;AAWO,SAASC,KAAKC,GAAA,EAAUC,IAAA,EAAW;EACzC,IAAIC,WAAA,CAAYF,GAAG,sBAAuB;IACzCG,OAAA,CAAQC,OAAA,CAAQJ,GAAG,EAAEK,OAAA,CAAQC,GAAA,IAAO;MACnCL,IAAA,CAAKK,GAAA,EAAKN,GAAA,CAAIM,GAAG,GAAGN,GAAG;IACxB,CAAC;EACF,OAAO;IACNA,GAAA,CAAIK,OAAA,CAAQ,CAACE,KAAA,EAAYC,KAAA,KAAeP,IAAA,CAAKO,KAAA,EAAOD,KAAA,EAAOP,GAAG,CAAC;EAChE;AACD;AAGO,SAASE,YAAYrC,KAAA,EAAsB;EACjD,MAAM4C,KAAA,GAAgC5C,KAAA,CAAMN,WAAW;EACvD,OAAOkD,KAAA,GACJA,KAAA,CAAMC,KAAA,GACNpC,KAAA,CAAMW,OAAA,CAAQpB,KAAK,oBAEnBsB,KAAA,CAAMtB,KAAK,kBAEXuB,KAAA,CAAMvB,KAAK;AAGf;AAGO,SAAS8C,IAAI9C,KAAA,EAAY+C,IAAA,EAA4B;EAC3D,OAAOV,WAAA,CAAYrC,KAAK,oBACrBA,KAAA,CAAM8C,GAAA,CAAIC,IAAI,IACdhC,MAAA,CAAOU,SAAA,CAAUI,cAAA,CAAeC,IAAA,CAAK9B,KAAA,EAAO+C,IAAI;AACpD;AAGO,SAASC,IAAIhD,KAAA,EAA2B+C,IAAA,EAAwB;EAEtE,OAAOV,WAAA,CAAYrC,KAAK,oBAAqBA,KAAA,CAAMgD,GAAA,CAAID,IAAI,IAAI/C,KAAA,CAAM+C,IAAI;AAC1E;AAGO,SAASE,IAAIjD,KAAA,EAAYkD,cAAA,EAA6BjC,KAAA,EAAY;EACxE,MAAMkC,CAAA,GAAId,WAAA,CAAYrC,KAAK;EAC3B,IAAImD,CAAA,kBAAoBnD,KAAA,CAAMiD,GAAA,CAAIC,cAAA,EAAgBjC,KAAK,WAC9CkC,CAAA,kBAAoB;IAC5BnD,KAAA,CAAMoD,GAAA,CAAInC,KAAK;EAChB,OAAOjB,KAAA,CAAMkD,cAAc,IAAIjC,KAAA;AAChC;AAGO,SAASoC,GAAGC,CAAA,EAAQC,CAAA,EAAiB;EAE3C,IAAID,CAAA,KAAMC,CAAA,EAAG;IACZ,OAAOD,CAAA,KAAM,KAAK,IAAIA,CAAA,KAAM,IAAIC,CAAA;EACjC,OAAO;IACN,OAAOD,CAAA,KAAMA,CAAA,IAAKC,CAAA,KAAMA,CAAA;EACzB;AACD;AAGO,SAASjC,MAAMkC,MAAA,EAA+B;EACpD,OAAOA,MAAA,YAAkBC,GAAA;AAC1B;AAGO,SAASlC,MAAMiC,MAAA,EAA+B;EACpD,OAAOA,MAAA,YAAkBE,GAAA;AAC1B;AAEO,SAASC,OAAOf,KAAA,EAAwB;EAC9C,OAAOA,KAAA,CAAMgB,KAAA,IAAShB,KAAA,CAAMX,KAAA;AAC7B;AAGO,SAAS4B,YAAYC,IAAA,EAAWC,MAAA,EAAoB;EAC1D,IAAIzC,KAAA,CAAMwC,IAAI,GAAG;IAChB,OAAO,IAAIL,GAAA,CAAIK,IAAI;EACpB;EACA,IAAIvC,KAAA,CAAMuC,IAAI,GAAG;IAChB,OAAO,IAAIJ,GAAA,CAAII,IAAI;EACpB;EACA,IAAIrD,KAAA,CAAMW,OAAA,CAAQ0C,IAAI,GAAG,OAAOrD,KAAA,CAAMgB,SAAA,CAAUuC,KAAA,CAAMlC,IAAA,CAAKgC,IAAI;EAE/D,MAAMG,OAAA,GAAU9C,aAAA,CAAc2C,IAAI;EAElC,IAAIC,MAAA,KAAW,QAASA,MAAA,KAAW,gBAAgB,CAACE,OAAA,EAAU;IAE7D,MAAMC,WAAA,GAAcnD,MAAA,CAAOoD,yBAAA,CAA0BL,IAAI;IACzD,OAAOI,WAAA,CAAYxE,WAAkB;IACrC,IAAI0E,IAAA,GAAO9B,OAAA,CAAQC,OAAA,CAAQ2B,WAAW;IACtC,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAID,IAAA,CAAK7D,MAAA,EAAQ8D,CAAA,IAAK;MACrC,MAAM5B,GAAA,GAAW2B,IAAA,CAAKC,CAAC;MACvB,MAAMC,IAAA,GAAOJ,WAAA,CAAYzB,GAAG;MAC5B,IAAI6B,IAAA,CAAKC,QAAA,KAAa,OAAO;QAC5BD,IAAA,CAAKC,QAAA,GAAW;QAChBD,IAAA,CAAKE,YAAA,GAAe;MACrB;MAIA,IAAIF,IAAA,CAAKtB,GAAA,IAAOsB,IAAA,CAAKrB,GAAA,EACpBiB,WAAA,CAAYzB,GAAG,IAAI;QAClB+B,YAAA,EAAc;QACdD,QAAA,EAAU;QAAA;QACVE,UAAA,EAAYH,IAAA,CAAKG,UAAA;QACjBxD,KAAA,EAAO6C,IAAA,CAAKrB,GAAG;MAChB;IACF;IACA,OAAO1B,MAAA,CAAO2D,MAAA,CAAO5D,cAAA,CAAegD,IAAI,GAAGI,WAAW;EACvD,OAAO;IAEN,MAAMvC,KAAA,GAAQb,cAAA,CAAegD,IAAI;IACjC,IAAInC,KAAA,KAAU,QAAQsC,OAAA,EAAS;MAC9B,OAAO;QAAC,GAAGH;MAAI;IAChB;IACA,MAAM3B,GAAA,GAAMpB,MAAA,CAAO2D,MAAA,CAAO/C,KAAK;IAC/B,OAAOZ,MAAA,CAAO4D,MAAA,CAAOxC,GAAA,EAAK2B,IAAI;EAC/B;AACD;AAUO,SAASc,OAAUzC,GAAA,EAAoC;EAAA,IAA1B0C,IAAA,GAAAvE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAwE,SAAA,GAAAxE,SAAA,MAAgB;EACnD,IAAIyE,QAAA,CAAS5C,GAAG,KAAKnB,OAAA,CAAQmB,GAAG,KAAK,CAACjB,WAAA,CAAYiB,GAAG,GAAG,OAAOA,GAAA;EAC/D,IAAIE,WAAA,CAAYF,GAAG,IAAI,GAAoB;IAC1CA,GAAA,CAAIc,GAAA,GAAMd,GAAA,CAAIiB,GAAA,GAAMjB,GAAA,CAAI6C,KAAA,GAAQ7C,GAAA,CAAI8C,MAAA,GAASC,2BAAA;EAC9C;EACAnE,MAAA,CAAO6D,MAAA,CAAOzC,GAAG;EACjB,IAAI0C,IAAA,EAGH9D,MAAA,CAAOoE,OAAA,CAAQhD,GAAG,EAAEK,OAAA,CAAQ4C,IAAA;IAAA,IAAC,CAAC3C,GAAA,EAAKxB,KAAK,IAAAmE,IAAA;IAAA,OAAMR,MAAA,CAAO3D,KAAA,EAAO,IAAI,CAAC;EAAA;EAClE,OAAOkB,GAAA;AACR;AAEA,SAAS+C,4BAAA,EAA8B;EACtChF,GAAA,CAAI,CAAC;AACN;AAEO,SAAS6E,SAAS5C,GAAA,EAAmB;EAC3C,OAAOpB,MAAA,CAAOgE,QAAA,CAAS5C,GAAG;AAC3B;;;AC5MA,IAAMkD,OAAA,GAoBF,CAAC;AAIE,SAASC,UACfC,SAAA,EACiC;EACjC,MAAMxF,MAAA,GAASsF,OAAA,CAAQE,SAAS;EAChC,IAAI,CAACxF,MAAA,EAAQ;IACZG,GAAA,CAAI,GAAGqF,SAAS;EACjB;EAEA,OAAOxF,MAAA;AACR;AAEO,SAASyF,WACfD,SAAA,EACAE,cAAA,EACO;EACP,IAAI,CAACJ,OAAA,CAAQE,SAAS,GAAGF,OAAA,CAAQE,SAAS,IAAIE,cAAA;AAC/C;;;AC5BA,IAAIC,YAAA;AAEG,SAASC,gBAAA,EAAkB;EACjC,OAAOD,YAAA;AACR;AAEA,SAASE,YACRC,OAAA,EACAC,MAAA,EACa;EACb,OAAO;IACNC,OAAA,EAAS,EAAC;IACVF,OAAA;IACAC,MAAA;IAAA;IAAA;IAGAE,cAAA,EAAgB;IAChBC,kBAAA,EAAoB;EACrB;AACD;AAEO,SAASC,kBACfC,KAAA,EACAC,aAAA,EACC;EACD,IAAIA,aAAA,EAAe;IAClBd,SAAA,CAAU,SAAS;IACnBa,KAAA,CAAME,QAAA,GAAW,EAAC;IAClBF,KAAA,CAAMG,eAAA,GAAkB,EAAC;IACzBH,KAAA,CAAMI,cAAA,GAAiBH,aAAA;EACxB;AACD;AAEO,SAASI,YAAYL,KAAA,EAAmB;EAC9CM,UAAA,CAAWN,KAAK;EAChBA,KAAA,CAAMJ,OAAA,CAAQvD,OAAA,CAAQkE,WAAW;EAEjCP,KAAA,CAAMJ,OAAA,GAAU;AACjB;AAEO,SAASU,WAAWN,KAAA,EAAmB;EAC7C,IAAIA,KAAA,KAAUT,YAAA,EAAc;IAC3BA,YAAA,GAAeS,KAAA,CAAMN,OAAA;EACtB;AACD;AAEO,SAASc,WAAWC,MAAA,EAAc;EACxC,OAAQlB,YAAA,GAAeE,WAAA,CAAYF,YAAA,EAAckB,MAAK;AACvD;AAEA,SAASF,YAAYG,KAAA,EAAgB;EACpC,MAAMjE,KAAA,GAAoBiE,KAAA,CAAMnH,WAAW;EAC3C,IAAIkD,KAAA,CAAMC,KAAA,uBAA6BD,KAAA,CAAMC,KAAA,oBAC5CD,KAAA,CAAMkE,OAAA,CAAQ,OACVlE,KAAA,CAAMmE,QAAA,GAAW;AACvB;;;AC3DO,SAASC,cAAcC,MAAA,EAAad,KAAA,EAAmB;EAC7DA,KAAA,CAAMF,kBAAA,GAAqBE,KAAA,CAAMJ,OAAA,CAAQxF,MAAA;EACzC,MAAM2G,SAAA,GAAYf,KAAA,CAAMJ,OAAA,CAAS,CAAC;EAClC,MAAMoB,UAAA,GAAaF,MAAA,KAAW,UAAaA,MAAA,KAAWC,SAAA;EACtD,IAAIC,UAAA,EAAY;IACf,IAAID,SAAA,CAAUxH,WAAW,EAAE0H,SAAA,EAAW;MACrCZ,WAAA,CAAYL,KAAK;MACjBjG,GAAA,CAAI,CAAC;IACN;IACA,IAAIgB,WAAA,CAAY+F,MAAM,GAAG;MAExBA,MAAA,GAASI,QAAA,CAASlB,KAAA,EAAOc,MAAM;MAC/B,IAAI,CAACd,KAAA,CAAMN,OAAA,EAASyB,WAAA,CAAYnB,KAAA,EAAOc,MAAM;IAC9C;IACA,IAAId,KAAA,CAAME,QAAA,EAAU;MACnBf,SAAA,CAAU,SAAS,EAAEiC,2BAAA,CACpBL,SAAA,CAAUxH,WAAW,EAAEuC,KAAA,EACvBgF,MAAA,EACAd,KAAA,CAAME,QAAA,EACNF,KAAA,CAAMG,eACP;IACD;EACD,OAAO;IAENW,MAAA,GAASI,QAAA,CAASlB,KAAA,EAAOe,SAAA,EAAW,EAAE;EACvC;EACAV,WAAA,CAAYL,KAAK;EACjB,IAAIA,KAAA,CAAME,QAAA,EAAU;IACnBF,KAAA,CAAMI,cAAA,CAAgBJ,KAAA,CAAME,QAAA,EAAUF,KAAA,CAAMG,eAAgB;EAC7D;EACA,OAAOW,MAAA,KAAW3H,OAAA,GAAU2H,MAAA,GAAS;AACtC;AAEA,SAASI,SAASG,SAAA,EAAuBvG,KAAA,EAAYwG,IAAA,EAAkB;EAEtE,IAAI1C,QAAA,CAAS9D,KAAK,GAAG,OAAOA,KAAA;EAE5B,MAAM2B,KAAA,GAAoB3B,KAAA,CAAMvB,WAAW;EAE3C,IAAI,CAACkD,KAAA,EAAO;IACXV,IAAA,CAAKjB,KAAA,EAAO,CAACwB,GAAA,EAAKiF,UAAA,KACjBC,gBAAA,CAAiBH,SAAA,EAAW5E,KAAA,EAAO3B,KAAA,EAAOwB,GAAA,EAAKiF,UAAA,EAAYD,IAAI,CAChE;IACA,OAAOxG,KAAA;EACR;EAEA,IAAI2B,KAAA,CAAMgF,MAAA,KAAWJ,SAAA,EAAW,OAAOvG,KAAA;EAEvC,IAAI,CAAC2B,KAAA,CAAMwE,SAAA,EAAW;IACrBE,WAAA,CAAYE,SAAA,EAAW5E,KAAA,CAAMX,KAAA,EAAO,IAAI;IACxC,OAAOW,KAAA,CAAMX,KAAA;EACd;EAEA,IAAI,CAACW,KAAA,CAAMiF,UAAA,EAAY;IACtBjF,KAAA,CAAMiF,UAAA,GAAa;IACnBjF,KAAA,CAAMgF,MAAA,CAAO3B,kBAAA;IACb,MAAMgB,MAAA,GAASrE,KAAA,CAAMgB,KAAA;IAKrB,IAAIkE,UAAA,GAAab,MAAA;IACjB,IAAIc,MAAA,GAAQ;IACZ,IAAInF,KAAA,CAAMC,KAAA,kBAAwB;MACjCiF,UAAA,GAAa,IAAIpE,GAAA,CAAIuD,MAAM;MAC3BA,MAAA,CAAOjC,KAAA,CAAM;MACb+C,MAAA,GAAQ;IACT;IACA7F,IAAA,CAAK4F,UAAA,EAAY,CAACrF,GAAA,EAAKiF,UAAA,KACtBC,gBAAA,CAAiBH,SAAA,EAAW5E,KAAA,EAAOqE,MAAA,EAAQxE,GAAA,EAAKiF,UAAA,EAAYD,IAAA,EAAMM,MAAK,CACxE;IAEAT,WAAA,CAAYE,SAAA,EAAWP,MAAA,EAAQ,KAAK;IAEpC,IAAIQ,IAAA,IAAQD,SAAA,CAAUnB,QAAA,EAAU;MAC/Bf,SAAA,CAAU,SAAS,EAAE0C,gBAAA,CACpBpF,KAAA,EACA6E,IAAA,EACAD,SAAA,CAAUnB,QAAA,EACVmB,SAAA,CAAUlB,eACX;IACD;EACD;EACA,OAAO1D,KAAA,CAAMgB,KAAA;AACd;AAEA,SAAS+D,iBACRH,SAAA,EACAS,WAAA,EACAC,YAAA,EACAnF,IAAA,EACA2E,UAAA,EACAS,QAAA,EACAC,WAAA,EACC;EACD,IAAIxI,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB4H,UAAA,KAAeQ,YAAA,EAC3DhI,GAAA,CAAI,CAAC;EACN,IAAIc,OAAA,CAAQ0G,UAAU,GAAG;IACxB,MAAMD,IAAA,GACLU,QAAA,IACAF,WAAA,IACAA,WAAA,CAAapF,KAAA;IAAA;IACb,CAACC,GAAA,CAAKmF,WAAA,CAA8CI,SAAA,EAAYtF,IAAI,IACjEoF,QAAA,CAAUG,MAAA,CAAOvF,IAAI,IACrB;IAEJ,MAAMwF,GAAA,GAAMlB,QAAA,CAASG,SAAA,EAAWE,UAAA,EAAYD,IAAI;IAChDxE,GAAA,CAAIiF,YAAA,EAAcnF,IAAA,EAAMwF,GAAG;IAG3B,IAAIvH,OAAA,CAAQuH,GAAG,GAAG;MACjBf,SAAA,CAAUxB,cAAA,GAAiB;IAC5B,OAAO;EACR,WAAWoC,WAAA,EAAa;IACvBF,YAAA,CAAa9E,GAAA,CAAIsE,UAAU;EAC5B;EAEA,IAAIxG,WAAA,CAAYwG,UAAU,KAAK,CAAC3C,QAAA,CAAS2C,UAAU,GAAG;IACrD,IAAI,CAACF,SAAA,CAAU1B,MAAA,CAAO0C,WAAA,IAAehB,SAAA,CAAUvB,kBAAA,GAAqB,GAAG;MAMtE;IACD;IACAoB,QAAA,CAASG,SAAA,EAAWE,UAAU;IAI9B,KACE,CAACO,WAAA,IAAe,CAACA,WAAA,CAAYL,MAAA,CAAO/B,OAAA,KACrC,OAAO9C,IAAA,KAAS,YAChBhC,MAAA,CAAOU,SAAA,CAAUgH,oBAAA,CAAqB3G,IAAA,CAAKoG,YAAA,EAAcnF,IAAI,GAE7DuE,WAAA,CAAYE,SAAA,EAAWE,UAAU;EACnC;AACD;AAEA,SAASJ,YAAYnB,KAAA,EAAmBlF,KAAA,EAA0B;EAAA,IAAd4D,IAAA,GAAAvE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAwE,SAAA,GAAAxE,SAAA,MAAO;EAE1D,IAAI,CAAC6F,KAAA,CAAMN,OAAA,IAAWM,KAAA,CAAML,MAAA,CAAO0C,WAAA,IAAerC,KAAA,CAAMH,cAAA,EAAgB;IACvEpB,MAAA,CAAO3D,KAAA,EAAO4D,IAAI;EACnB;AACD;;;ACjHO,SAAS6D,iBACf5E,IAAA,EACA6E,MAAA,EACyB;EACzB,MAAMvH,OAAA,GAAUX,KAAA,CAAMW,OAAA,CAAQ0C,IAAI;EAClC,MAAMlB,KAAA,GAAoB;IACzBC,KAAA,EAAOzB,OAAA;;IAAA;IAEPwG,MAAA,EAAQe,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;IAAA;IAEjDyB,SAAA,EAAW;IAAA;IAEXS,UAAA,EAAY;IAAA;IAEZQ,SAAA,EAAW,CAAC;IAAA;IAEZxC,OAAA,EAAS8C,MAAA;IAAA;IAET1G,KAAA,EAAO6B,IAAA;IAAA;IAEP8E,MAAA,EAAQ;IAAA;IAAA;IAERhF,KAAA,EAAO;IAAA;IAEPkD,OAAA,EAAS;IACT+B,SAAA,EAAW;EACZ;EAQA,IAAIrF,MAAA,GAAYZ,KAAA;EAChB,IAAIkG,KAAA,GAA2CC,WAAA;EAC/C,IAAI3H,OAAA,EAAS;IACZoC,MAAA,GAAS,CAACZ,KAAK;IACfkG,KAAA,GAAQE,UAAA;EACT;EAEA,MAAM;IAACC,MAAA;IAAQC;EAAK,IAAIC,KAAA,CAAMC,SAAA,CAAU5F,MAAA,EAAQsF,KAAK;EACrDlG,KAAA,CAAMgG,MAAA,GAASM,KAAA;EACftG,KAAA,CAAMkE,OAAA,GAAUmC,MAAA;EAChB,OAAOC,KAAA;AACR;AAKO,IAAMH,WAAA,GAAwC;EACpD/F,IAAIJ,KAAA,EAAOG,IAAA,EAAM;IAChB,IAAIA,IAAA,KAASrD,WAAA,EAAa,OAAOkD,KAAA;IAEjC,MAAMyG,MAAA,GAAS1F,MAAA,CAAOf,KAAK;IAC3B,IAAI,CAACE,GAAA,CAAIuG,MAAA,EAAQtG,IAAI,GAAG;MAEvB,OAAOuG,iBAAA,CAAkB1G,KAAA,EAAOyG,MAAA,EAAQtG,IAAI;IAC7C;IACA,MAAM9B,KAAA,GAAQoI,MAAA,CAAOtG,IAAI;IACzB,IAAIH,KAAA,CAAMiF,UAAA,IAAc,CAAC3G,WAAA,CAAYD,KAAK,GAAG;MAC5C,OAAOA,KAAA;IACR;IAGA,IAAIA,KAAA,KAAUsI,IAAA,CAAK3G,KAAA,CAAMX,KAAA,EAAOc,IAAI,GAAG;MACtCyG,WAAA,CAAY5G,KAAK;MACjB,OAAQA,KAAA,CAAMgB,KAAA,CAAOb,IAAW,IAAI0G,WAAA,CAAYxI,KAAA,EAAO2B,KAAK;IAC7D;IACA,OAAO3B,KAAA;EACR;EACA6B,IAAIF,KAAA,EAAOG,IAAA,EAAM;IAChB,OAAOA,IAAA,IAAQY,MAAA,CAAOf,KAAK;EAC5B;EACAL,QAAQK,KAAA,EAAO;IACd,OAAON,OAAA,CAAQC,OAAA,CAAQoB,MAAA,CAAOf,KAAK,CAAC;EACrC;EACAK,IACCL,KAAA,EACAG,IAAA,EACA9B,KAAA,EACC;IACD,MAAMqD,IAAA,GAAOoF,sBAAA,CAAuB/F,MAAA,CAAOf,KAAK,GAAGG,IAAI;IACvD,IAAIuB,IAAA,EAAMrB,GAAA,EAAK;MAGdqB,IAAA,CAAKrB,GAAA,CAAInB,IAAA,CAAKc,KAAA,CAAMgG,MAAA,EAAQ3H,KAAK;MACjC,OAAO;IACR;IACA,IAAI,CAAC2B,KAAA,CAAMwE,SAAA,EAAW;MAGrB,MAAMuC,QAAA,GAAUJ,IAAA,CAAK5F,MAAA,CAAOf,KAAK,GAAGG,IAAI;MAExC,MAAM6G,YAAA,GAAiCD,QAAA,GAAUjK,WAAW;MAC5D,IAAIkK,YAAA,IAAgBA,YAAA,CAAa3H,KAAA,KAAUhB,KAAA,EAAO;QACjD2B,KAAA,CAAMgB,KAAA,CAAOb,IAAI,IAAI9B,KAAA;QACrB2B,KAAA,CAAMyF,SAAA,CAAUtF,IAAI,IAAI;QACxB,OAAO;MACR;MACA,IAAIM,EAAA,CAAGpC,KAAA,EAAO0I,QAAO,MAAM1I,KAAA,KAAU,UAAa6B,GAAA,CAAIF,KAAA,CAAMX,KAAA,EAAOc,IAAI,IACtE,OAAO;MACRyG,WAAA,CAAY5G,KAAK;MACjBiH,WAAA,CAAYjH,KAAK;IAClB;IAEA,IACEA,KAAA,CAAMgB,KAAA,CAAOb,IAAI,MAAM9B,KAAA;IAAA;IAEtBA,KAAA,KAAU,UAAa8B,IAAA,IAAQH,KAAA,CAAMgB,KAAA;IAAA;IAEtCkG,MAAA,CAAOC,KAAA,CAAM9I,KAAK,KAAK6I,MAAA,CAAOC,KAAA,CAAMnH,KAAA,CAAMgB,KAAA,CAAOb,IAAI,CAAC,GAEvD,OAAO;IAGRH,KAAA,CAAMgB,KAAA,CAAOb,IAAI,IAAI9B,KAAA;IACrB2B,KAAA,CAAMyF,SAAA,CAAUtF,IAAI,IAAI;IACxB,OAAO;EACR;EACAiH,eAAepH,KAAA,EAAOG,IAAA,EAAc;IAEnC,IAAIwG,IAAA,CAAK3G,KAAA,CAAMX,KAAA,EAAOc,IAAI,MAAM,UAAaA,IAAA,IAAQH,KAAA,CAAMX,KAAA,EAAO;MACjEW,KAAA,CAAMyF,SAAA,CAAUtF,IAAI,IAAI;MACxByG,WAAA,CAAY5G,KAAK;MACjBiH,WAAA,CAAYjH,KAAK;IAClB,OAAO;MAEN,OAAOA,KAAA,CAAMyF,SAAA,CAAUtF,IAAI;IAC5B;IACA,IAAIH,KAAA,CAAMgB,KAAA,EAAO;MAChB,OAAOhB,KAAA,CAAMgB,KAAA,CAAMb,IAAI;IACxB;IACA,OAAO;EACR;EAAA;EAAA;EAGAkH,yBAAyBrH,KAAA,EAAOG,IAAA,EAAM;IACrC,MAAMmH,KAAA,GAAQvG,MAAA,CAAOf,KAAK;IAC1B,MAAM0B,IAAA,GAAOhC,OAAA,CAAQ2H,wBAAA,CAAyBC,KAAA,EAAOnH,IAAI;IACzD,IAAI,CAACuB,IAAA,EAAM,OAAOA,IAAA;IAClB,OAAO;MACNC,QAAA,EAAU;MACVC,YAAA,EAAc5B,KAAA,CAAMC,KAAA,sBAA4BE,IAAA,KAAS;MACzD0B,UAAA,EAAYH,IAAA,CAAKG,UAAA;MACjBxD,KAAA,EAAOiJ,KAAA,CAAMnH,IAAI;IAClB;EACD;EACAoH,eAAA,EAAiB;IAChBjK,GAAA,CAAI,EAAE;EACP;EACAY,eAAe8B,KAAA,EAAO;IACrB,OAAO9B,cAAA,CAAe8B,KAAA,CAAMX,KAAK;EAClC;EACAmI,eAAA,EAAiB;IAChBlK,GAAA,CAAI,EAAE;EACP;AACD;AAMA,IAAM8I,UAAA,GAA8C,CAAC;AACrD9G,IAAA,CAAK6G,WAAA,EAAa,CAACtG,GAAA,EAAK4H,EAAA,KAAO;EAE9BrB,UAAA,CAAWvG,GAAG,IAAI,YAAW;IAC5BnC,SAAA,CAAU,CAAC,IAAIA,SAAA,CAAU,CAAC,EAAE,CAAC;IAC7B,OAAO+J,EAAA,CAAGzJ,KAAA,CAAM,MAAMN,SAAS;EAChC;AACD,CAAC;AACD0I,UAAA,CAAWgB,cAAA,GAAiB,UAASpH,KAAA,EAAOG,IAAA,EAAM;EACjD,IAAInD,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBiK,KAAA,CAAMO,QAAA,CAASvH,IAAW,CAAC,GACvE7C,GAAA,CAAI,EAAE;EAEP,OAAO8I,UAAA,CAAW/F,GAAA,CAAKnB,IAAA,CAAK,MAAMc,KAAA,EAAOG,IAAA,EAAM,MAAS;AACzD;AACAiG,UAAA,CAAW/F,GAAA,GAAM,UAASL,KAAA,EAAOG,IAAA,EAAM9B,KAAA,EAAO;EAC7C,IACCrB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBACzBiD,IAAA,KAAS,YACTgH,KAAA,CAAMO,QAAA,CAASvH,IAAW,CAAC,GAE3B7C,GAAA,CAAI,EAAE;EACP,OAAO6I,WAAA,CAAY9F,GAAA,CAAKnB,IAAA,CAAK,MAAMc,KAAA,CAAM,CAAC,GAAGG,IAAA,EAAM9B,KAAA,EAAO2B,KAAA,CAAM,CAAC,CAAC;AACnE;AAGA,SAAS2G,KAAK1C,KAAA,EAAgB9D,IAAA,EAAmB;EAChD,MAAMH,KAAA,GAAQiE,KAAA,CAAMnH,WAAW;EAC/B,MAAM2J,MAAA,GAASzG,KAAA,GAAQe,MAAA,CAAOf,KAAK,IAAIiE,KAAA;EACvC,OAAOwC,MAAA,CAAOtG,IAAI;AACnB;AAEA,SAASuG,kBAAkB1G,KAAA,EAAmByG,MAAA,EAAatG,IAAA,EAAmB;EAC7E,MAAMuB,IAAA,GAAOoF,sBAAA,CAAuBL,MAAA,EAAQtG,IAAI;EAChD,OAAOuB,IAAA,GACJ,WAAWA,IAAA,GACVA,IAAA,CAAKrD,KAAA;EAAA;EAAA;EAGLqD,IAAA,CAAKtB,GAAA,EAAKlB,IAAA,CAAKc,KAAA,CAAMgG,MAAM,IAC5B;AACJ;AAEA,SAASc,uBACRL,MAAA,EACAtG,IAAA,EACiC;EAEjC,IAAI,EAAEA,IAAA,IAAQsG,MAAA,GAAS,OAAO;EAC9B,IAAI1H,KAAA,GAAQb,cAAA,CAAeuI,MAAM;EACjC,OAAO1H,KAAA,EAAO;IACb,MAAM2C,IAAA,GAAOvD,MAAA,CAAOkJ,wBAAA,CAAyBtI,KAAA,EAAOoB,IAAI;IACxD,IAAIuB,IAAA,EAAM,OAAOA,IAAA;IACjB3C,KAAA,GAAQb,cAAA,CAAea,KAAK;EAC7B;EACA,OAAO;AACR;AAEO,SAASkI,YAAYjH,KAAA,EAAmB;EAC9C,IAAI,CAACA,KAAA,CAAMwE,SAAA,EAAW;IACrBxE,KAAA,CAAMwE,SAAA,GAAY;IAClB,IAAIxE,KAAA,CAAMiD,OAAA,EAAS;MAClBgE,WAAA,CAAYjH,KAAA,CAAMiD,OAAO;IAC1B;EACD;AACD;AAEO,SAAS2D,YAAY5G,KAAA,EAIzB;EACF,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;IACjBhB,KAAA,CAAMgB,KAAA,GAAQC,WAAA,CACbjB,KAAA,CAAMX,KAAA,EACNW,KAAA,CAAMgF,MAAA,CAAO9B,MAAA,CAAOyE,qBACrB;EACD;AACD;;;AChQO,IAAMC,MAAA,GAAN,MAAoC;EAI1CnJ,YAAYoJ,MAAA,EAGT;IAAA,IAAAC,KAAA;IANH,KAAAlC,WAAA,GAAuB;IACvB,KAAA+B,qBAAA,GAAoC;IA+BpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA,KAAAI,OAAA,GAAoB,CAAC7G,IAAA,EAAW8G,MAAA,EAAcxE,aAAA,KAAwB;MAErE,IAAI,OAAOtC,IAAA,KAAS,cAAc,OAAO8G,MAAA,KAAW,YAAY;QAC/D,MAAMC,WAAA,GAAcD,MAAA;QACpBA,MAAA,GAAS9G,IAAA;QAET,MAAMgH,IAAA,GAAO;QACb,OAAO,SAASC,eAAA,EAId;UAAA,IAFDC,KAAA,GAAA1K,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAwE,SAAA,GAAAxE,SAAA,MAAOuK,WAAA;UAAA,SAAAI,KAAA,GAAA3K,SAAA,CAAAC,MAAA,EACJC,IAAA,OAAAC,KAAA,CAAAwK,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;YAAA1K,IAAA,CAAA0K,KAAA,QAAA5K,SAAA,CAAA4K,KAAA;UAAA;UAEH,OAAOJ,IAAA,CAAKH,OAAA,CAAQK,KAAA,EAAOnE,KAAA,IAAmB+D,MAAA,CAAO9I,IAAA,CAAK,MAAM+E,KAAA,EAAO,GAAGrG,IAAI,CAAC;QAChF;MACD;MAEA,IAAI,OAAOoK,MAAA,KAAW,YAAY1K,GAAA,CAAI,CAAC;MACvC,IAAIkG,aAAA,KAAkB,UAAa,OAAOA,aAAA,KAAkB,YAC3DlG,GAAA,CAAI,CAAC;MAEN,IAAI+G,MAAA;MAGJ,IAAI/F,WAAA,CAAY4C,IAAI,GAAG;QACtB,MAAMqC,KAAA,GAAQQ,UAAA,CAAW,IAAI;QAC7B,MAAMuC,KAAA,GAAQO,WAAA,CAAY3F,IAAA,EAAM,MAAS;QACzC,IAAIqH,QAAA,GAAW;QACf,IAAI;UACHlE,MAAA,GAAS2D,MAAA,CAAO1B,KAAK;UACrBiC,QAAA,GAAW;QACZ,UAAE;UAED,IAAIA,QAAA,EAAU3E,WAAA,CAAYL,KAAK,OAC1BM,UAAA,CAAWN,KAAK;QACtB;QACAD,iBAAA,CAAkBC,KAAA,EAAOC,aAAa;QACtC,OAAOY,aAAA,CAAcC,MAAA,EAAQd,KAAK;MACnC,WAAW,CAACrC,IAAA,IAAQ,OAAOA,IAAA,KAAS,UAAU;QAC7CmD,MAAA,GAAS2D,MAAA,CAAO9G,IAAI;QACpB,IAAImD,MAAA,KAAW,QAAWA,MAAA,GAASnD,IAAA;QACnC,IAAImD,MAAA,KAAW3H,OAAA,EAAS2H,MAAA,GAAS;QACjC,IAAI,KAAKuB,WAAA,EAAa5D,MAAA,CAAOqC,MAAA,EAAQ,IAAI;QACzC,IAAIb,aAAA,EAAe;UAClB,MAAMgF,CAAA,GAAa,EAAC;UACpB,MAAMC,EAAA,GAAc,EAAC;UACrB/F,SAAA,CAAU,SAAS,EAAEiC,2BAAA,CAA4BzD,IAAA,EAAMmD,MAAA,EAAQmE,CAAA,EAAGC,EAAE;UACpEjF,aAAA,CAAcgF,CAAA,EAAGC,EAAE;QACpB;QACA,OAAOpE,MAAA;MACR,OAAO/G,GAAA,CAAI,GAAG4D,IAAI;IACnB;IAEA,KAAAwH,kBAAA,GAA0C,CAACxH,IAAA,EAAW8G,MAAA,KAAsB;MAE3E,IAAI,OAAO9G,IAAA,KAAS,YAAY;QAC/B,OAAO,UAAClB,KAAA;UAAA,SAAA2I,KAAA,GAAAjL,SAAA,CAAAC,MAAA,EAAeC,IAAA,OAAAC,KAAA,CAAA8K,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;YAAAhL,IAAA,CAAAgL,KAAA,QAAAlL,SAAA,CAAAkL,KAAA;UAAA;UAAA,OACtBd,KAAA,CAAKY,kBAAA,CAAmB1I,KAAA,EAAQiE,KAAA,IAAe/C,IAAA,CAAK+C,KAAA,EAAO,GAAGrG,IAAI,CAAC;QAAA;MACrE;MAEA,IAAIiL,OAAA,EAAkBC,cAAA;MACtB,MAAMzE,MAAA,GAAS,KAAK0D,OAAA,CAAQ7G,IAAA,EAAM8G,MAAA,EAAQ,CAACQ,CAAA,EAAYC,EAAA,KAAgB;QACtEI,OAAA,GAAUL,CAAA;QACVM,cAAA,GAAiBL,EAAA;MAClB,CAAC;MACD,OAAO,CAACpE,MAAA,EAAQwE,OAAA,EAAUC,cAAe;IAC1C;IA1FC,IAAI,OAAOjB,MAAA,EAAQkB,UAAA,KAAe,WACjC,KAAKC,aAAA,CAAcnB,MAAA,CAAQkB,UAAU;IACtC,IAAI,OAAOlB,MAAA,EAAQoB,oBAAA,KAAyB,WAC3C,KAAKC,uBAAA,CAAwBrB,MAAA,CAAQoB,oBAAoB;EAC3D;EAwFAE,YAAiCjI,IAAA,EAAmB;IACnD,IAAI,CAAC5C,WAAA,CAAY4C,IAAI,GAAG5D,GAAA,CAAI,CAAC;IAC7B,IAAIc,OAAA,CAAQ8C,IAAI,GAAGA,IAAA,GAAOkI,OAAA,CAAQlI,IAAI;IACtC,MAAMqC,KAAA,GAAQQ,UAAA,CAAW,IAAI;IAC7B,MAAMuC,KAAA,GAAQO,WAAA,CAAY3F,IAAA,EAAM,MAAS;IACzCoF,KAAA,CAAMxJ,WAAW,EAAEmJ,SAAA,GAAY;IAC/BpC,UAAA,CAAWN,KAAK;IAChB,OAAO+C,KAAA;EACR;EAEA+C,YACCpF,KAAA,EACAT,aAAA,EACuC;IACvC,MAAMxD,KAAA,GAAoBiE,KAAA,IAAUA,KAAA,CAAcnH,WAAW;IAC7D,IAAI,CAACkD,KAAA,IAAS,CAACA,KAAA,CAAMiG,SAAA,EAAW3I,GAAA,CAAI,CAAC;IACrC,MAAM;MAAC0H,MAAA,EAAQzB;IAAK,IAAIvD,KAAA;IACxBsD,iBAAA,CAAkBC,KAAA,EAAOC,aAAa;IACtC,OAAOY,aAAA,CAAc,QAAWb,KAAK;EACtC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAyF,cAAc3K,KAAA,EAAgB;IAC7B,KAAKuH,WAAA,GAAcvH,KAAA;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA6K,wBAAwB7K,KAAA,EAAmB;IAC1C,KAAKsJ,qBAAA,GAAwBtJ,KAAA;EAC9B;EAEAiL,aAAkCpI,IAAA,EAAS2H,OAAA,EAA8B;IAGxE,IAAIpH,CAAA;IACJ,KAAKA,CAAA,GAAIoH,OAAA,CAAQlL,MAAA,GAAS,GAAG8D,CAAA,IAAK,GAAGA,CAAA,IAAK;MACzC,MAAM8H,KAAA,GAAQV,OAAA,CAAQpH,CAAC;MACvB,IAAI8H,KAAA,CAAM1E,IAAA,CAAKlH,MAAA,KAAW,KAAK4L,KAAA,CAAMC,EAAA,KAAO,WAAW;QACtDtI,IAAA,GAAOqI,KAAA,CAAMlL,KAAA;QACb;MACD;IACD;IAGA,IAAIoD,CAAA,GAAI,IAAI;MACXoH,OAAA,GAAUA,OAAA,CAAQzH,KAAA,CAAMK,CAAA,GAAI,CAAC;IAC9B;IAEA,MAAMgI,gBAAA,GAAmB/G,SAAA,CAAU,SAAS,EAAEgH,aAAA;IAC9C,IAAItL,OAAA,CAAQ8C,IAAI,GAAG;MAElB,OAAOuI,gBAAA,CAAiBvI,IAAA,EAAM2H,OAAO;IACtC;IAEA,OAAO,KAAKd,OAAA,CAAQ7G,IAAA,EAAO+C,KAAA,IAC1BwF,gBAAA,CAAiBxF,KAAA,EAAO4E,OAAO,CAChC;EACD;AACD;AAEO,SAAShC,YACfxI,KAAA,EACA0H,MAAA,EACyB;EAEzB,MAAM9B,KAAA,GAAiBvF,KAAA,CAAML,KAAK,IAC/BqE,SAAA,CAAU,QAAQ,EAAEiH,SAAA,CAAUtL,KAAA,EAAO0H,MAAM,IAC3CpH,KAAA,CAAMN,KAAK,IACXqE,SAAA,CAAU,QAAQ,EAAEkH,SAAA,CAAUvL,KAAA,EAAO0H,MAAM,IAC3CD,gBAAA,CAAiBzH,KAAA,EAAO0H,MAAM;EAEjC,MAAMxC,KAAA,GAAQwC,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;EACvDQ,KAAA,CAAMJ,OAAA,CAAQ0G,IAAA,CAAK5F,KAAK;EACxB,OAAOA,KAAA;AACR;;;AC3MO,SAASmF,QAAQ/K,KAAA,EAAiB;EACxC,IAAI,CAACD,OAAA,CAAQC,KAAK,GAAGf,GAAA,CAAI,IAAIe,KAAK;EAClC,OAAOyL,WAAA,CAAYzL,KAAK;AACzB;AAEA,SAASyL,YAAYzL,KAAA,EAAiB;EACrC,IAAI,CAACC,WAAA,CAAYD,KAAK,KAAK8D,QAAA,CAAS9D,KAAK,GAAG,OAAOA,KAAA;EACnD,MAAM2B,KAAA,GAAgC3B,KAAA,CAAMvB,WAAW;EACvD,IAAIiN,IAAA;EACJ,IAAI/J,KAAA,EAAO;IACV,IAAI,CAACA,KAAA,CAAMwE,SAAA,EAAW,OAAOxE,KAAA,CAAMX,KAAA;IAEnCW,KAAA,CAAMiF,UAAA,GAAa;IACnB8E,IAAA,GAAO9I,WAAA,CAAY5C,KAAA,EAAO2B,KAAA,CAAMgF,MAAA,CAAO9B,MAAA,CAAOyE,qBAAqB;EACpE,OAAO;IACNoC,IAAA,GAAO9I,WAAA,CAAY5C,KAAA,EAAO,IAAI;EAC/B;EAEAiB,IAAA,CAAKyK,IAAA,EAAM,CAAClK,GAAA,EAAKiF,UAAA,KAAe;IAC/BzE,GAAA,CAAI0J,IAAA,EAAMlK,GAAA,EAAKiK,WAAA,CAAYhF,UAAU,CAAC;EACvC,CAAC;EACD,IAAI9E,KAAA,EAAO;IACVA,KAAA,CAAMiF,UAAA,GAAa;EACpB;EACA,OAAO8E,IAAA;AACR;;;ACdO,SAASC,cAAA,EAAgB;EAC/B,MAAMC,WAAA,GAAc;EACpB,IAAIjN,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IAC1CH,MAAA,CAAO8M,IAAA,CACN,uCACA,UAASL,EAAA,EAAY;MACpB,OAAO,kCAAkCA,EAAA;IAC1C,GACA,UAAS3E,IAAA,EAAc;MACtB,OAAO,+CAA+CA,IAAA;IACvD,GACA,uFACD;EACD;EAEA,MAAMqF,OAAA,GAAU;EAChB,MAAMC,GAAA,GAAM;EACZ,MAAMC,MAAA,GAAS;EAEf,SAAShF,iBACRpF,KAAA,EACAqK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACO;IACP,QAAQ9I,KAAA,CAAMC,KAAA;MACb;MACA;QACC,OAAOqK,2BAAA,CACNtK,KAAA,EACAqK,QAAA,EACAxB,OAAA,EACAC,cACD;MACD;QACC,OAAOyB,oBAAA,CAAqBvK,KAAA,EAAOqK,QAAA,EAAUxB,OAAA,EAASC,cAAc;MACrE;QACC,OAAO0B,kBAAA,CACLxK,KAAA,EACDqK,QAAA,EACAxB,OAAA,EACAC,cACD;IACF;EACD;EAEA,SAASyB,qBACRvK,KAAA,EACAqK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACC;IACD,IAAI;MAACzJ,KAAA;MAAOoG;IAAS,IAAIzF,KAAA;IACzB,IAAIgB,KAAA,GAAQhB,KAAA,CAAMgB,KAAA;IAGlB,IAAIA,KAAA,CAAMrD,MAAA,GAAS0B,KAAA,CAAM1B,MAAA,EAAQ;MAEhC;MAAC,CAAC0B,KAAA,EAAO2B,KAAK,IAAI,CAACA,KAAA,EAAO3B,KAAK;MAC9B,CAACwJ,OAAA,EAASC,cAAc,IAAI,CAACA,cAAA,EAAgBD,OAAO;IACtD;IAGA,SAASpH,CAAA,GAAI,GAAGA,CAAA,GAAIpC,KAAA,CAAM1B,MAAA,EAAQ8D,CAAA,IAAK;MACtC,IAAIgE,SAAA,CAAUhE,CAAC,KAAKT,KAAA,CAAMS,CAAC,MAAMpC,KAAA,CAAMoC,CAAC,GAAG;QAC1C,MAAMoD,IAAA,GAAOwF,QAAA,CAAS3E,MAAA,CAAO,CAACjE,CAAC,CAAC;QAChCoH,OAAA,CAAQgB,IAAA,CAAK;UACZL,EAAA,EAAIU,OAAA;UACJrF,IAAA;UAAA;UAAA;UAGAxG,KAAA,EAAOoM,uBAAA,CAAwBzJ,KAAA,CAAMS,CAAC,CAAC;QACxC,CAAC;QACDqH,cAAA,CAAee,IAAA,CAAK;UACnBL,EAAA,EAAIU,OAAA;UACJrF,IAAA;UACAxG,KAAA,EAAOoM,uBAAA,CAAwBpL,KAAA,CAAMoC,CAAC,CAAC;QACxC,CAAC;MACF;IACD;IAGA,SAASA,CAAA,GAAIpC,KAAA,CAAM1B,MAAA,EAAQ8D,CAAA,GAAIT,KAAA,CAAMrD,MAAA,EAAQ8D,CAAA,IAAK;MACjD,MAAMoD,IAAA,GAAOwF,QAAA,CAAS3E,MAAA,CAAO,CAACjE,CAAC,CAAC;MAChCoH,OAAA,CAAQgB,IAAA,CAAK;QACZL,EAAA,EAAIW,GAAA;QACJtF,IAAA;QAAA;QAAA;QAGAxG,KAAA,EAAOoM,uBAAA,CAAwBzJ,KAAA,CAAMS,CAAC,CAAC;MACxC,CAAC;IACF;IACA,SAASA,CAAA,GAAIT,KAAA,CAAMrD,MAAA,GAAS,GAAG0B,KAAA,CAAM1B,MAAA,IAAU8D,CAAA,EAAG,EAAEA,CAAA,EAAG;MACtD,MAAMoD,IAAA,GAAOwF,QAAA,CAAS3E,MAAA,CAAO,CAACjE,CAAC,CAAC;MAChCqH,cAAA,CAAee,IAAA,CAAK;QACnBL,EAAA,EAAIY,MAAA;QACJvF;MACD,CAAC;IACF;EACD;EAGA,SAASyF,4BACRtK,KAAA,EACAqK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACC;IACD,MAAM;MAACzJ,KAAA;MAAO2B;IAAK,IAAIhB,KAAA;IACvBV,IAAA,CAAKU,KAAA,CAAMyF,SAAA,EAAY,CAAC5F,GAAA,EAAK6K,aAAA,KAAkB;MAC9C,MAAMC,SAAA,GAAYvK,GAAA,CAAIf,KAAA,EAAOQ,GAAG;MAChC,MAAMxB,KAAA,GAAQ+B,GAAA,CAAIY,KAAA,EAAQnB,GAAG;MAC7B,MAAM2J,EAAA,GAAK,CAACkB,aAAA,GAAgBN,MAAA,GAASlK,GAAA,CAAIb,KAAA,EAAOQ,GAAG,IAAIqK,OAAA,GAAUC,GAAA;MACjE,IAAIQ,SAAA,KAActM,KAAA,IAASmL,EAAA,KAAOU,OAAA,EAAS;MAC3C,MAAMrF,IAAA,GAAOwF,QAAA,CAAS3E,MAAA,CAAO7F,GAAU;MACvCgJ,OAAA,CAAQgB,IAAA,CAAKL,EAAA,KAAOY,MAAA,GAAS;QAACZ,EAAA;QAAI3E;MAAI,IAAI;QAAC2E,EAAA;QAAI3E,IAAA;QAAMxG;MAAK,CAAC;MAC3DyK,cAAA,CAAee,IAAA,CACdL,EAAA,KAAOW,GAAA,GACJ;QAACX,EAAA,EAAIY,MAAA;QAAQvF;MAAI,IACjB2E,EAAA,KAAOY,MAAA,GACP;QAACZ,EAAA,EAAIW,GAAA;QAAKtF,IAAA;QAAMxG,KAAA,EAAOoM,uBAAA,CAAwBE,SAAS;MAAC,IACzD;QAACnB,EAAA,EAAIU,OAAA;QAASrF,IAAA;QAAMxG,KAAA,EAAOoM,uBAAA,CAAwBE,SAAS;MAAC,CACjE;IACD,CAAC;EACF;EAEA,SAASH,mBACRxK,KAAA,EACAqK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACC;IACD,IAAI;MAACzJ,KAAA;MAAO2B;IAAK,IAAIhB,KAAA;IAErB,IAAIyB,CAAA,GAAI;IACRpC,KAAA,CAAMO,OAAA,CAASvB,KAAA,IAAe;MAC7B,IAAI,CAAC2C,KAAA,CAAOd,GAAA,CAAI7B,KAAK,GAAG;QACvB,MAAMwG,IAAA,GAAOwF,QAAA,CAAS3E,MAAA,CAAO,CAACjE,CAAC,CAAC;QAChCoH,OAAA,CAAQgB,IAAA,CAAK;UACZL,EAAA,EAAIY,MAAA;UACJvF,IAAA;UACAxG;QACD,CAAC;QACDyK,cAAA,CAAe8B,OAAA,CAAQ;UACtBpB,EAAA,EAAIW,GAAA;UACJtF,IAAA;UACAxG;QACD,CAAC;MACF;MACAoD,CAAA;IACD,CAAC;IACDA,CAAA,GAAI;IACJT,KAAA,CAAOpB,OAAA,CAASvB,KAAA,IAAe;MAC9B,IAAI,CAACgB,KAAA,CAAMa,GAAA,CAAI7B,KAAK,GAAG;QACtB,MAAMwG,IAAA,GAAOwF,QAAA,CAAS3E,MAAA,CAAO,CAACjE,CAAC,CAAC;QAChCoH,OAAA,CAAQgB,IAAA,CAAK;UACZL,EAAA,EAAIW,GAAA;UACJtF,IAAA;UACAxG;QACD,CAAC;QACDyK,cAAA,CAAe8B,OAAA,CAAQ;UACtBpB,EAAA,EAAIY,MAAA;UACJvF,IAAA;UACAxG;QACD,CAAC;MACF;MACAoD,CAAA;IACD,CAAC;EACF;EAEA,SAASkD,4BACRkG,SAAA,EACAC,WAAA,EACAjC,OAAA,EACAC,cAAA,EACO;IACPD,OAAA,CAAQgB,IAAA,CAAK;MACZL,EAAA,EAAIU,OAAA;MACJrF,IAAA,EAAM,EAAC;MACPxG,KAAA,EAAOyM,WAAA,KAAgBpO,OAAA,GAAU,SAAYoO;IAC9C,CAAC;IACDhC,cAAA,CAAee,IAAA,CAAK;MACnBL,EAAA,EAAIU,OAAA;MACJrF,IAAA,EAAM,EAAC;MACPxG,KAAA,EAAOwM;IACR,CAAC;EACF;EAEA,SAASnB,cAAiBzF,KAAA,EAAU4E,OAAA,EAA8B;IACjEA,OAAA,CAAQjJ,OAAA,CAAQ2J,KAAA,IAAS;MACxB,MAAM;QAAC1E,IAAA;QAAM2E;MAAE,IAAID,KAAA;MAEnB,IAAIrI,IAAA,GAAY+C,KAAA;MAChB,SAASxC,CAAA,GAAI,GAAGA,CAAA,GAAIoD,IAAA,CAAKlH,MAAA,GAAS,GAAG8D,CAAA,IAAK;QACzC,MAAMsJ,UAAA,GAAatL,WAAA,CAAYyB,IAAI;QACnC,IAAIsH,CAAA,GAAI3D,IAAA,CAAKpD,CAAC;QACd,IAAI,OAAO+G,CAAA,KAAM,YAAY,OAAOA,CAAA,KAAM,UAAU;UACnDA,CAAA,GAAI,KAAKA,CAAA;QACV;QAGA,KACEuC,UAAA,uBAAkCA,UAAA,wBAClCvC,CAAA,KAAM,eAAeA,CAAA,KAAM,gBAE5BlL,GAAA,CAAI2M,WAAA,GAAc,CAAC;QACpB,IAAI,OAAO/I,IAAA,KAAS,cAAcsH,CAAA,KAAM,aACvClL,GAAA,CAAI2M,WAAA,GAAc,CAAC;QACpB/I,IAAA,GAAOd,GAAA,CAAIc,IAAA,EAAMsH,CAAC;QAClB,IAAI,OAAOtH,IAAA,KAAS,UAAU5D,GAAA,CAAI2M,WAAA,GAAc,GAAGpF,IAAA,CAAKmG,IAAA,CAAK,GAAG,CAAC;MAClE;MAEA,MAAMC,IAAA,GAAOxL,WAAA,CAAYyB,IAAI;MAC7B,MAAM7C,KAAA,GAAQ6M,mBAAA,CAAoB3B,KAAA,CAAMlL,KAAK;MAC7C,MAAMwB,GAAA,GAAMgF,IAAA,CAAKA,IAAA,CAAKlH,MAAA,GAAS,CAAC;MAChC,QAAQ6L,EAAA;QACP,KAAKU,OAAA;UACJ,QAAQe,IAAA;YACP;cACC,OAAO/J,IAAA,CAAKb,GAAA,CAAIR,GAAA,EAAKxB,KAAK;YAE3B;cACCf,GAAA,CAAI2M,WAAW;YAChB;cAKC,OAAQ/I,IAAA,CAAKrB,GAAG,IAAIxB,KAAA;UACtB;QACD,KAAK8L,GAAA;UACJ,QAAQc,IAAA;YACP;cACC,OAAOpL,GAAA,KAAQ,MACZqB,IAAA,CAAK2I,IAAA,CAAKxL,KAAK,IACf6C,IAAA,CAAKiK,MAAA,CAAOtL,GAAA,EAAY,GAAGxB,KAAK;YACpC;cACC,OAAO6C,IAAA,CAAKb,GAAA,CAAIR,GAAA,EAAKxB,KAAK;YAC3B;cACC,OAAO6C,IAAA,CAAKV,GAAA,CAAInC,KAAK;YACtB;cACC,OAAQ6C,IAAA,CAAKrB,GAAG,IAAIxB,KAAA;UACtB;QACD,KAAK+L,MAAA;UACJ,QAAQa,IAAA;YACP;cACC,OAAO/J,IAAA,CAAKiK,MAAA,CAAOtL,GAAA,EAAY,CAAC;YACjC;cACC,OAAOqB,IAAA,CAAKmB,MAAA,CAAOxC,GAAG;YACvB;cACC,OAAOqB,IAAA,CAAKmB,MAAA,CAAOkH,KAAA,CAAMlL,KAAK;YAC/B;cACC,OAAO,OAAO6C,IAAA,CAAKrB,GAAG;UACxB;QACD;UACCvC,GAAA,CAAI2M,WAAA,GAAc,GAAGT,EAAE;MACzB;IACD,CAAC;IAED,OAAOvF,KAAA;EACR;EAMA,SAASiH,oBAAoB3L,GAAA,EAAU;IACtC,IAAI,CAACjB,WAAA,CAAYiB,GAAG,GAAG,OAAOA,GAAA;IAC9B,IAAI1B,KAAA,CAAMW,OAAA,CAAQe,GAAG,GAAG,OAAOA,GAAA,CAAI6L,GAAA,CAAIF,mBAAmB;IAC1D,IAAIxM,KAAA,CAAMa,GAAG,GACZ,OAAO,IAAIsB,GAAA,CACVhD,KAAA,CAAMwN,IAAA,CAAK9L,GAAA,CAAIgD,OAAA,CAAQ,CAAC,EAAE6I,GAAA,CAAIE,KAAA;MAAA,IAAC,CAACC,CAAA,EAAGC,CAAC,IAAAF,KAAA;MAAA,OAAM,CAACC,CAAA,EAAGL,mBAAA,CAAoBM,CAAC,CAAC,CAAC;IAAA,EACtE;IACD,IAAI7M,KAAA,CAAMY,GAAG,GAAG,OAAO,IAAIuB,GAAA,CAAIjD,KAAA,CAAMwN,IAAA,CAAK9L,GAAG,EAAE6L,GAAA,CAAIF,mBAAmB,CAAC;IACvE,MAAMO,MAAA,GAAStN,MAAA,CAAO2D,MAAA,CAAO5D,cAAA,CAAeqB,GAAG,CAAC;IAChD,WAAWM,GAAA,IAAON,GAAA,EAAKkM,MAAA,CAAO5L,GAAG,IAAIqL,mBAAA,CAAoB3L,GAAA,CAAIM,GAAG,CAAC;IACjE,IAAIK,GAAA,CAAIX,GAAA,EAAK1C,SAAS,GAAG4O,MAAA,CAAO5O,SAAS,IAAI0C,GAAA,CAAI1C,SAAS;IAC1D,OAAO4O,MAAA;EACR;EAEA,SAAShB,wBAA2BlL,GAAA,EAAW;IAC9C,IAAInB,OAAA,CAAQmB,GAAG,GAAG;MACjB,OAAO2L,mBAAA,CAAoB3L,GAAG;IAC/B,OAAO,OAAOA,GAAA;EACf;EAEAqD,UAAA,CAAW,WAAW;IACrB8G,aAAA;IACAtE,gBAAA;IACAT;EACD,CAAC;AACF;;;ACzSO,SAAS+G,aAAA,EAAe;EAC9B,MAAMC,QAAA,SAAiB9K,GAAA,CAAI;IAG1BpC,YAAYmC,MAAA,EAAgBmF,MAAA,EAAqB;MAChD,MAAM;MACN,KAAKjJ,WAAW,IAAI;QACnBmD,KAAA;QACAgD,OAAA,EAAS8C,MAAA;QACTf,MAAA,EAAQe,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;QACjDyB,SAAA,EAAW;QACXS,UAAA,EAAY;QACZjE,KAAA,EAAO;QACPyE,SAAA,EAAW;QACXpG,KAAA,EAAOuB,MAAA;QACPoF,MAAA,EAAQ;QACRC,SAAA,EAAW;QACX9B,QAAA,EAAU;MACX;IACD;IAEA,IAAIyH,KAAA,EAAe;MAClB,OAAO7K,MAAA,CAAO,KAAKjE,WAAW,CAAC,EAAE8O,IAAA;IAClC;IAEA1L,IAAIL,GAAA,EAAmB;MACtB,OAAOkB,MAAA,CAAO,KAAKjE,WAAW,CAAC,EAAEoD,GAAA,CAAIL,GAAG;IACzC;IAEAQ,IAAIR,GAAA,EAAUxB,KAAA,EAAY;MACzB,MAAM2B,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrB,IAAI,CAACe,MAAA,CAAOf,KAAK,EAAEE,GAAA,CAAIL,GAAG,KAAKkB,MAAA,CAAOf,KAAK,EAAEI,GAAA,CAAIP,GAAG,MAAMxB,KAAA,EAAO;QAChEyN,cAAA,CAAe9L,KAAK;QACpBiH,WAAA,CAAYjH,KAAK;QACjBA,KAAA,CAAMyF,SAAA,CAAWpF,GAAA,CAAIR,GAAA,EAAK,IAAI;QAC9BG,KAAA,CAAMgB,KAAA,CAAOX,GAAA,CAAIR,GAAA,EAAKxB,KAAK;QAC3B2B,KAAA,CAAMyF,SAAA,CAAWpF,GAAA,CAAIR,GAAA,EAAK,IAAI;MAC/B;MACA,OAAO;IACR;IAEAwC,OAAOxC,GAAA,EAAmB;MACzB,IAAI,CAAC,KAAKK,GAAA,CAAIL,GAAG,GAAG;QACnB,OAAO;MACR;MAEA,MAAMG,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrB8L,cAAA,CAAe9L,KAAK;MACpBiH,WAAA,CAAYjH,KAAK;MACjB,IAAIA,KAAA,CAAMX,KAAA,CAAMa,GAAA,CAAIL,GAAG,GAAG;QACzBG,KAAA,CAAMyF,SAAA,CAAWpF,GAAA,CAAIR,GAAA,EAAK,KAAK;MAChC,OAAO;QACNG,KAAA,CAAMyF,SAAA,CAAWpD,MAAA,CAAOxC,GAAG;MAC5B;MACAG,KAAA,CAAMgB,KAAA,CAAOqB,MAAA,CAAOxC,GAAG;MACvB,OAAO;IACR;IAEAuC,MAAA,EAAQ;MACP,MAAMpC,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrB,IAAIe,MAAA,CAAOf,KAAK,EAAE4L,IAAA,EAAM;QACvBE,cAAA,CAAe9L,KAAK;QACpBiH,WAAA,CAAYjH,KAAK;QACjBA,KAAA,CAAMyF,SAAA,GAAY,mBAAI5E,GAAA,CAAI;QAC1BvB,IAAA,CAAKU,KAAA,CAAMX,KAAA,EAAOQ,GAAA,IAAO;UACxBG,KAAA,CAAMyF,SAAA,CAAWpF,GAAA,CAAIR,GAAA,EAAK,KAAK;QAChC,CAAC;QACDG,KAAA,CAAMgB,KAAA,CAAOoB,KAAA,CAAM;MACpB;IACD;IAEAxC,QAAQmM,EAAA,EAA+CC,OAAA,EAAe;MACrE,MAAMhM,KAAA,GAAkB,KAAKlD,WAAW;MACxCiE,MAAA,CAAOf,KAAK,EAAEJ,OAAA,CAAQ,CAACqM,MAAA,EAAapM,GAAA,EAAUqM,IAAA,KAAc;QAC3DH,EAAA,CAAG7M,IAAA,CAAK8M,OAAA,EAAS,KAAK5L,GAAA,CAAIP,GAAG,GAAGA,GAAA,EAAK,IAAI;MAC1C,CAAC;IACF;IAEAO,IAAIP,GAAA,EAAe;MAClB,MAAMG,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrB,MAAM3B,KAAA,GAAQ0C,MAAA,CAAOf,KAAK,EAAEI,GAAA,CAAIP,GAAG;MACnC,IAAIG,KAAA,CAAMiF,UAAA,IAAc,CAAC3G,WAAA,CAAYD,KAAK,GAAG;QAC5C,OAAOA,KAAA;MACR;MACA,IAAIA,KAAA,KAAU2B,KAAA,CAAMX,KAAA,CAAMe,GAAA,CAAIP,GAAG,GAAG;QACnC,OAAOxB,KAAA;MACR;MAEA,MAAM4F,KAAA,GAAQ4C,WAAA,CAAYxI,KAAA,EAAO2B,KAAK;MACtC8L,cAAA,CAAe9L,KAAK;MACpBA,KAAA,CAAMgB,KAAA,CAAOX,GAAA,CAAIR,GAAA,EAAKoE,KAAK;MAC3B,OAAOA,KAAA;IACR;IAEAzC,KAAA,EAA8B;MAC7B,OAAOT,MAAA,CAAO,KAAKjE,WAAW,CAAC,EAAE0E,IAAA,CAAK;IACvC;IAEA2K,OAAA,EAAgC;MAC/B,MAAMC,QAAA,GAAW,KAAK5K,IAAA,CAAK;MAC3B,OAAO;QACN,CAAC7E,MAAA,CAAOyP,QAAQ,GAAG,MAAM,KAAKD,MAAA,CAAO;QACrCE,IAAA,EAAMA,CAAA,KAAM;UACX,MAAMC,CAAA,GAAIF,QAAA,CAASC,IAAA,CAAK;UAExB,IAAIC,CAAA,CAAEC,IAAA,EAAM,OAAOD,CAAA;UACnB,MAAMjO,KAAA,GAAQ,KAAK+B,GAAA,CAAIkM,CAAA,CAAEjO,KAAK;UAC9B,OAAO;YACNkO,IAAA,EAAM;YACNlO;UACD;QACD;MACD;IACD;IAEAkE,QAAA,EAAwC;MACvC,MAAM6J,QAAA,GAAW,KAAK5K,IAAA,CAAK;MAC3B,OAAO;QACN,CAAC7E,MAAA,CAAOyP,QAAQ,GAAG,MAAM,KAAK7J,OAAA,CAAQ;QACtC8J,IAAA,EAAMA,CAAA,KAAM;UACX,MAAMC,CAAA,GAAIF,QAAA,CAASC,IAAA,CAAK;UAExB,IAAIC,CAAA,CAAEC,IAAA,EAAM,OAAOD,CAAA;UACnB,MAAMjO,KAAA,GAAQ,KAAK+B,GAAA,CAAIkM,CAAA,CAAEjO,KAAK;UAC9B,OAAO;YACNkO,IAAA,EAAM;YACNlO,KAAA,EAAO,CAACiO,CAAA,CAAEjO,KAAA,EAAOA,KAAK;UACvB;QACD;MACD;IACD;IAEA,EAtICvB,WAAA,EAsIAH,MAAA,CAAOyP,QAAA,KAAY;MACnB,OAAO,KAAK7J,OAAA,CAAQ;IACrB;EACD;EAEA,SAASoH,UAA4B/I,MAAA,EAAWmF,MAAA,EAAwB;IAEvE,OAAO,IAAI4F,QAAA,CAAS/K,MAAA,EAAQmF,MAAM;EACnC;EAEA,SAAS+F,eAAe9L,KAAA,EAAiB;IACxC,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;MACjBhB,KAAA,CAAMyF,SAAA,GAAY,mBAAI5E,GAAA,CAAI;MAC1Bb,KAAA,CAAMgB,KAAA,GAAQ,IAAIH,GAAA,CAAIb,KAAA,CAAMX,KAAK;IAClC;EACD;EAEA,MAAMmN,QAAA,SAAiB1L,GAAA,CAAI;IAE1BrC,YAAYmC,MAAA,EAAgBmF,MAAA,EAAqB;MAChD,MAAM;MACN,KAAKjJ,WAAW,IAAI;QACnBmD,KAAA;QACAgD,OAAA,EAAS8C,MAAA;QACTf,MAAA,EAAQe,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;QACjDyB,SAAA,EAAW;QACXS,UAAA,EAAY;QACZjE,KAAA,EAAO;QACP3B,KAAA,EAAOuB,MAAA;QACPoF,MAAA,EAAQ;QACR7C,OAAA,EAAS,mBAAItC,GAAA,CAAI;QACjBsD,QAAA,EAAU;QACV8B,SAAA,EAAW;MACZ;IACD;IAEA,IAAI2F,KAAA,EAAe;MAClB,OAAO7K,MAAA,CAAO,KAAKjE,WAAW,CAAC,EAAE8O,IAAA;IAClC;IAEA1L,IAAI7B,KAAA,EAAqB;MACxB,MAAM2B,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MAErB,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;QACjB,OAAOhB,KAAA,CAAMX,KAAA,CAAMa,GAAA,CAAI7B,KAAK;MAC7B;MACA,IAAI2B,KAAA,CAAMgB,KAAA,CAAMd,GAAA,CAAI7B,KAAK,GAAG,OAAO;MACnC,IAAI2B,KAAA,CAAMmD,OAAA,CAAQjD,GAAA,CAAI7B,KAAK,KAAK2B,KAAA,CAAMgB,KAAA,CAAMd,GAAA,CAAIF,KAAA,CAAMmD,OAAA,CAAQ/C,GAAA,CAAI/B,KAAK,CAAC,GACvE,OAAO;MACR,OAAO;IACR;IAEAmC,IAAInC,KAAA,EAAiB;MACpB,MAAM2B,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrB,IAAI,CAAC,KAAKE,GAAA,CAAI7B,KAAK,GAAG;QACrBoO,cAAA,CAAezM,KAAK;QACpBiH,WAAA,CAAYjH,KAAK;QACjBA,KAAA,CAAMgB,KAAA,CAAOR,GAAA,CAAInC,KAAK;MACvB;MACA,OAAO;IACR;IAEAgE,OAAOhE,KAAA,EAAiB;MACvB,IAAI,CAAC,KAAK6B,GAAA,CAAI7B,KAAK,GAAG;QACrB,OAAO;MACR;MAEA,MAAM2B,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrByM,cAAA,CAAezM,KAAK;MACpBiH,WAAA,CAAYjH,KAAK;MACjB,OACCA,KAAA,CAAMgB,KAAA,CAAOqB,MAAA,CAAOhE,KAAK,MACxB2B,KAAA,CAAMmD,OAAA,CAAQjD,GAAA,CAAI7B,KAAK,IACrB2B,KAAA,CAAMgB,KAAA,CAAOqB,MAAA,CAAOrC,KAAA,CAAMmD,OAAA,CAAQ/C,GAAA,CAAI/B,KAAK,CAAC;MACjB;IAEhC;IAEA+D,MAAA,EAAQ;MACP,MAAMpC,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrB,IAAIe,MAAA,CAAOf,KAAK,EAAE4L,IAAA,EAAM;QACvBa,cAAA,CAAezM,KAAK;QACpBiH,WAAA,CAAYjH,KAAK;QACjBA,KAAA,CAAMgB,KAAA,CAAOoB,KAAA,CAAM;MACpB;IACD;IAEA+J,OAAA,EAAgC;MAC/B,MAAMnM,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrByM,cAAA,CAAezM,KAAK;MACpB,OAAOA,KAAA,CAAMgB,KAAA,CAAOmL,MAAA,CAAO;IAC5B;IAEA5J,QAAA,EAAwC;MACvC,MAAMvC,KAAA,GAAkB,KAAKlD,WAAW;MACxC+O,eAAA,CAAgB7L,KAAK;MACrByM,cAAA,CAAezM,KAAK;MACpB,OAAOA,KAAA,CAAMgB,KAAA,CAAOuB,OAAA,CAAQ;IAC7B;IAEAf,KAAA,EAA8B;MAC7B,OAAO,KAAK2K,MAAA,CAAO;IACpB;IAEA,EA3FCrP,WAAA,EA2FAH,MAAA,CAAOyP,QAAA,KAAY;MACnB,OAAO,KAAKD,MAAA,CAAO;IACpB;IAEAvM,QAAQmM,EAAA,EAASC,OAAA,EAAe;MAC/B,MAAMI,QAAA,GAAW,KAAKD,MAAA,CAAO;MAC7B,IAAI9H,MAAA,GAAS+H,QAAA,CAASC,IAAA,CAAK;MAC3B,OAAO,CAAChI,MAAA,CAAOkI,IAAA,EAAM;QACpBR,EAAA,CAAG7M,IAAA,CAAK8M,OAAA,EAAS3H,MAAA,CAAOhG,KAAA,EAAOgG,MAAA,CAAOhG,KAAA,EAAO,IAAI;QACjDgG,MAAA,GAAS+H,QAAA,CAASC,IAAA,CAAK;MACxB;IACD;EACD;EACA,SAASzC,UAA4BhJ,MAAA,EAAWmF,MAAA,EAAwB;IAEvE,OAAO,IAAIyG,QAAA,CAAS5L,MAAA,EAAQmF,MAAM;EACnC;EAEA,SAAS0G,eAAezM,KAAA,EAAiB;IACxC,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;MAEjBhB,KAAA,CAAMgB,KAAA,GAAQ,mBAAIF,GAAA,CAAI;MACtBd,KAAA,CAAMX,KAAA,CAAMO,OAAA,CAAQvB,KAAA,IAAS;QAC5B,IAAIC,WAAA,CAAYD,KAAK,GAAG;UACvB,MAAM4F,KAAA,GAAQ4C,WAAA,CAAYxI,KAAA,EAAO2B,KAAK;UACtCA,KAAA,CAAMmD,OAAA,CAAQ9C,GAAA,CAAIhC,KAAA,EAAO4F,KAAK;UAC9BjE,KAAA,CAAMgB,KAAA,CAAOR,GAAA,CAAIyD,KAAK;QACvB,OAAO;UACNjE,KAAA,CAAMgB,KAAA,CAAOR,GAAA,CAAInC,KAAK;QACvB;MACD,CAAC;IACF;EACD;EAEA,SAASwN,gBAAgB7L,KAAA,EAA+C;IACvE,IAAIA,KAAA,CAAMmE,QAAA,EAAU7G,GAAA,CAAI,GAAGoP,IAAA,CAAKC,SAAA,CAAU5L,MAAA,CAAOf,KAAK,CAAC,CAAC;EACzD;EAEA4C,UAAA,CAAW,UAAU;IAAC+G,SAAA;IAAWC;EAAS,CAAC;AAC5C;;;ACrRA,IAAMgD,KAAA,GAAQ,IAAIhF,MAAA,CAAM;AAqBjB,IAAMG,OAAA,GAAoB6E,KAAA,CAAM7E,OAAA;AAMhC,IAAMW,kBAAA,GAA0CkE,KAAA,CAAMlE,kBAAA,CAAmBmE,IAAA,CAC/ED,KACD;AAOO,IAAM5D,aAAA,GAAgB4D,KAAA,CAAM5D,aAAA,CAAc6D,IAAA,CAAKD,KAAK;AAOpD,IAAM1D,uBAAA,GAA0B0D,KAAA,CAAM1D,uBAAA,CAAwB2D,IAAA,CAAKD,KAAK;AAOxE,IAAMtD,YAAA,GAAesD,KAAA,CAAMtD,YAAA,CAAauD,IAAA,CAAKD,KAAK;AAMlD,IAAMzD,WAAA,GAAcyD,KAAA,CAAMzD,WAAA,CAAY0D,IAAA,CAAKD,KAAK;AAUhD,IAAMvD,WAAA,GAAcuD,KAAA,CAAMvD,WAAA,CAAYwD,IAAA,CAAKD,KAAK;AAQhD,SAASE,UAAazO,KAAA,EAAoB;EAChD,OAAOA,KAAA;AACR;AAOO,SAAS0O,cAAiB1O,KAAA,EAAwB;EACxD,OAAOA,KAAA;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}