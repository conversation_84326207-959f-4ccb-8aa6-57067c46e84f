{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\Test.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Test() {\n  _s();\n  const [isActive, setIsActive] = React.useState(false);\n  const handleToggle = () => {\n    setIsActive(!isActive);\n  };\n  useEffect(() => {}, [isActive]);\n  return /*#__PURE__*/_jsxDEV(\"label\", {\n    class: \"inline-flex items-center me-5 cursor-pointer\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"checkbox\",\n      value: \"\",\n      class: \"sr-only peer\",\n      onChange: handleToggle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-white-900 peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500 dark:peer-checked:bg-orange-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 9\n  }, this);\n}\n_s(Test, \"Got/rvXObncgVYAtKDAIOlzhTAc=\");\n_c = Test;\nexport default Test;\nvar _c;\n$RefreshReg$(_c, \"Test\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "Test", "_s", "isActive", "setIsActive", "useState", "handleToggle", "class", "children", "type", "value", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/Test.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\n\r\n\r\nfunction Test() {\r\n\r\n    const [isActive, setIsActive] = React.useState(false);\r\n\r\n    const handleToggle = () => {\r\n        setIsActive(!isActive);\r\n    }\r\n\r\n    useEffect(() => {\r\n\r\n    }, [isActive])\r\n\r\n    return (\r\n        <label class=\"inline-flex items-center me-5 cursor-pointer\">\r\n            <input type=\"checkbox\" value=\"\" class=\"sr-only peer\" onChange={handleToggle} />\r\n            <div class=\"relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-white-900 peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500 dark:peer-checked:bg-orange-500\"></div>\r\n        </label>\r\n    );\r\n\r\n\r\n}\r\nexport default Test;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzC,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAEZ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,KAAK,CAACQ,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvBF,WAAW,CAAC,CAACD,QAAQ,CAAC;EAC1B,CAAC;EAEDL,SAAS,CAAC,MAAM,CAEhB,CAAC,EAAE,CAACK,QAAQ,CAAC,CAAC;EAEd,oBACIH,OAAA;IAAOO,KAAK,EAAC,8CAA8C;IAAAC,QAAA,gBACvDR,OAAA;MAAOS,IAAI,EAAC,UAAU;MAACC,KAAK,EAAC,EAAE;MAACH,KAAK,EAAC,cAAc;MAACI,QAAQ,EAAEL;IAAa;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/Ef,OAAA;MAAKO,KAAK,EAAC;IAA6f;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5gB,CAAC;AAIhB;AAACb,EAAA,CApBQD,IAAI;AAAAe,EAAA,GAAJf,IAAI;AAqBb,eAAeA,IAAI;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}