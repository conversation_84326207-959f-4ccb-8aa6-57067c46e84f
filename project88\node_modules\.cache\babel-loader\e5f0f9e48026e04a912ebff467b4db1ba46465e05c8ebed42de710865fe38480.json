{"ast": null, "code": "import { z } from 'zod';\nimport UserApi from '../api/UserApi';\nexport const EditUserValidation = (originalEmail, originalPhone) => {\n  return z.object({\n    email: z.string().nonempty(\"Email không được để trống\").email(\"Email không hợp lệ\").refine(async value => {\n      if (value === originalEmail) return true;\n      const rs = await UserApi.isExistEmail(value);\n      return !rs.data;\n    }, {\n      message: \"Email đã tòn tại\"\n    }),\n    phone: z.string().nonempty(\"Số điện thoại không được để trống\").regex(/^\\d{10}$/, \"Số điện thoại phải gồm đúng 10 chữ số\").refine(async value => {\n      if (value === originalPhone) return true;\n      const rs = await UserApi.isExistPhone(value);\n      return !rs.data;\n    }, {\n      message: \"SDT đã tồn tại\"\n    })\n  });\n};\n_c = EditUserValidation;\nvar _c;\n$RefreshReg$(_c, \"EditUserValidation\");", "map": {"version": 3, "names": ["z", "UserApi", "EditUserValidation", "originalEmail", "originalPhone", "object", "email", "string", "nonempty", "refine", "value", "rs", "isExistEmail", "data", "message", "phone", "regex", "isExistPhone", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/validation/EditUserValidatiion.js"], "sourcesContent": ["import { z } from 'zod';\r\nimport UserApi from '../api/UserApi';\r\n\r\nexport const EditUserValidation = (originalEmail, originalPhone) => {\r\n\r\n    return z.object({\r\n        email: z.string().nonempty(\"Email không được để trống\").email(\"Email không hợp lệ\")\r\n            .refine(async (value) => {\r\n                if (value === originalEmail) return true;\r\n                const rs = await UserApi.isExistEmail(value);\r\n                return !rs.data;\r\n            }, {\r\n                message: \"Email đã tòn tại\"\r\n            }),\r\n        phone: z.string()\r\n            .nonempty(\"Số điện thoại không được để trống\")\r\n            .regex(/^\\d{10}$/, \"Số điện thoại phải gồm đúng 10 chữ số\")\r\n            .refine(async (value) => {\r\n                if (value === originalPhone) return true;\r\n                const rs = await UserApi.isExistPhone(value);\r\n                return !rs.data;\r\n            }, {\r\n                message: \"SDT đã tồn tại\"\r\n            }),\r\n    });\r\n}"], "mappings": "AAAA,SAASA,CAAC,QAAQ,KAAK;AACvB,OAAOC,OAAO,MAAM,gBAAgB;AAEpC,OAAO,MAAMC,kBAAkB,GAAGA,CAACC,aAAa,EAAEC,aAAa,KAAK;EAEhE,OAAOJ,CAAC,CAACK,MAAM,CAAC;IACZC,KAAK,EAAEN,CAAC,CAACO,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,2BAA2B,CAAC,CAACF,KAAK,CAAC,oBAAoB,CAAC,CAC9EG,MAAM,CAAC,MAAOC,KAAK,IAAK;MACrB,IAAIA,KAAK,KAAKP,aAAa,EAAE,OAAO,IAAI;MACxC,MAAMQ,EAAE,GAAG,MAAMV,OAAO,CAACW,YAAY,CAACF,KAAK,CAAC;MAC5C,OAAO,CAACC,EAAE,CAACE,IAAI;IACnB,CAAC,EAAE;MACCC,OAAO,EAAE;IACb,CAAC,CAAC;IACNC,KAAK,EAAEf,CAAC,CAACO,MAAM,CAAC,CAAC,CACZC,QAAQ,CAAC,mCAAmC,CAAC,CAC7CQ,KAAK,CAAC,UAAU,EAAE,uCAAuC,CAAC,CAC1DP,MAAM,CAAC,MAAOC,KAAK,IAAK;MACrB,IAAIA,KAAK,KAAKN,aAAa,EAAE,OAAO,IAAI;MACxC,MAAMO,EAAE,GAAG,MAAMV,OAAO,CAACgB,YAAY,CAACP,KAAK,CAAC;MAC5C,OAAO,CAACC,EAAE,CAACE,IAAI;IACnB,CAAC,EAAE;MACCC,OAAO,EAAE;IACb,CAAC;EACT,CAAC,CAAC;AACN,CAAC;AAAAI,EAAA,GAtBYhB,kBAAkB;AAAA,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}