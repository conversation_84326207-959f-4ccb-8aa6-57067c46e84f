{"ast": null, "code": "export function buildMatchPatternFn(args) {\n  return function (string) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n\n    // [TODO] I challenge you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return {\n      value,\n      rest\n    };\n  };\n}", "map": {"version": 3, "names": ["buildMatchPatternFn", "args", "string", "options", "arguments", "length", "undefined", "matchResult", "match", "matchPattern", "matchedString", "parseResult", "parsePattern", "value", "valueCallback", "rest", "slice"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js"], "sourcesContent": ["export function buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback\n      ? args.valueCallback(parseResult[0])\n      : parseResult[0];\n\n    // [TODO] I challenge you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n"], "mappings": "AAAA,OAAO,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAO,UAACC,MAAM,EAAmB;IAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC1B,MAAMG,WAAW,GAAGL,MAAM,CAACM,KAAK,CAACP,IAAI,CAACQ,YAAY,CAAC;IACnD,IAAI,CAACF,WAAW,EAAE,OAAO,IAAI;IAC7B,MAAMG,aAAa,GAAGH,WAAW,CAAC,CAAC,CAAC;IAEpC,MAAMI,WAAW,GAAGT,MAAM,CAACM,KAAK,CAACP,IAAI,CAACW,YAAY,CAAC;IACnD,IAAI,CAACD,WAAW,EAAE,OAAO,IAAI;IAC7B,IAAIE,KAAK,GAAGZ,IAAI,CAACa,aAAa,GAC1Bb,IAAI,CAACa,aAAa,CAACH,WAAW,CAAC,CAAC,CAAC,CAAC,GAClCA,WAAW,CAAC,CAAC,CAAC;;IAElB;IACAE,KAAK,GAAGV,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACW,aAAa,CAACD,KAAK,CAAC,GAAGA,KAAK;IAEpE,MAAME,IAAI,GAAGb,MAAM,CAACc,KAAK,CAACN,aAAa,CAACL,MAAM,CAAC;IAE/C,OAAO;MAAEQ,KAAK;MAAEE;IAAK,CAAC;EACxB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}