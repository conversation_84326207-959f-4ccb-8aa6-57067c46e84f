{"ast": null, "code": "import { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseAnyDigitsSigned } from \"../utils.js\";\nexport class TimestampSecondsParser extends Parser {\n  priority = 40;\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n  set(date, _flags, value) {\n    return [constructFrom(date, value * 1000), {\n      timestampIsSet: true\n    }];\n  }\n  incompatibleTokens = \"*\";\n}", "map": {"version": 3, "names": ["constructFrom", "<PERSON><PERSON><PERSON>", "parseAnyDigitsSigned", "TimestampSecondsParser", "priority", "parse", "dateString", "set", "date", "_flags", "value", "timestampIsSet", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/parse/_lib/parsers/TimestampSecondsParser.js"], "sourcesContent": ["import { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseAnyDigitsSigned } from \"../utils.js\";\n\nexport class TimestampSecondsParser extends Parser {\n  priority = 40;\n\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n\n  set(date, _flags, value) {\n    return [constructFrom(date, value * 1000), { timestampIsSet: true }];\n  }\n\n  incompatibleTokens = \"*\";\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,oBAAoB,QAAQ,aAAa;AAElD,OAAO,MAAMC,sBAAsB,SAASF,MAAM,CAAC;EACjDG,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAE;IAChB,OAAOJ,oBAAoB,CAACI,UAAU,CAAC;EACzC;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACvB,OAAO,CAACV,aAAa,CAACQ,IAAI,EAAEE,KAAK,GAAG,IAAI,CAAC,EAAE;MAAEC,cAAc,EAAE;IAAK,CAAC,CAAC;EACtE;EAEAC,kBAAkB,GAAG,GAAG;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}