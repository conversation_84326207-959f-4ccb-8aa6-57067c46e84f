{"ast": null, "code": "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };", "map": {"version": 3, "names": ["rectToClientRect", "arrow", "arrow$1", "autoPlacement", "autoPlacement$1", "detectOverflow", "detectOverflow$1", "flip", "flip$1", "hide", "hide$1", "inline", "inline$1", "limitShift", "limitShift$1", "offset", "offset$1", "shift", "shift$1", "size", "size$1", "computePosition", "computePosition$1", "round", "createCoords", "max", "min", "floor", "getComputedStyle", "isHTMLElement", "isElement", "getWindow", "isWebKit", "getFrameElement", "getNodeScroll", "getDocumentElement", "isTop<PERSON><PERSON>er", "getNodeName", "isOverflowElement", "getOverflowAncestors", "getParentNode", "isLastTraversableNode", "isContainingBlock", "isTableElement", "getContainingBlock", "getCssDimensions", "element", "css", "width", "parseFloat", "height", "hasOffset", "offsetWidth", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "$", "unwrapElement", "contextElement", "getScale", "dom<PERSON>lement", "rect", "getBoundingClientRect", "x", "y", "Number", "isFinite", "noOffsets", "getVisualOffsets", "win", "visualViewport", "offsetLeft", "offsetTop", "shouldAddVisualOffsets", "isFixed", "floatingOffsetParent", "includeScale", "isFixedStrategy", "offsetParent", "clientRect", "scale", "visualOffsets", "left", "top", "offsetWin", "currentWin", "currentIFrame", "iframeScale", "iframeRect", "clientLeft", "paddingLeft", "clientTop", "paddingTop", "getWindowScrollBarX", "leftScroll", "scrollLeft", "getHTMLOffset", "documentElement", "scroll", "ignoreScrollbarX", "htmlRect", "scrollTop", "convertOffsetParentRelativeRectToViewportRelativeRect", "_ref", "elements", "strategy", "topLayer", "floating", "offsets", "isOffsetParentAnElement", "offsetRect", "htmlOffset", "getClientRects", "Array", "from", "getDocumentRect", "html", "body", "ownerDocument", "scrollWidth", "clientWidth", "scrollHeight", "clientHeight", "direction", "getViewportRect", "visualViewportBased", "getInnerBoundingClientRect", "getClientRectFromClippingAncestor", "clippingAncestor", "hasFixedPositionAncestor", "stopNode", "parentNode", "position", "getClippingElementAncestors", "cache", "cachedResult", "get", "result", "filter", "el", "currentContainingBlockComputedStyle", "elementIsFixed", "currentNode", "computedStyle", "currentNodeIsContaining", "shouldDropCurrentNode", "includes", "ancestor", "set", "getClippingRect", "boundary", "rootBoundary", "elementClippingAncestors", "_c", "concat", "clippingAncestors", "firstClippingAncestor", "clippingRect", "reduce", "accRect", "right", "bottom", "getDimensions", "getRectRelativeToOffsetParent", "setLeftRTLScrollbarOffset", "isStaticPositioned", "getTrueOffsetParent", "polyfill", "rawOffsetParent", "getOffsetParent", "svgOffsetParent", "getElementRects", "data", "getOffsetParentFn", "getDimensionsFn", "floatingDimensions", "reference", "isRTL", "platform", "rectsAreEqual", "a", "b", "<PERSON><PERSON><PERSON>", "onMove", "io", "timeoutId", "root", "cleanup", "_io", "clearTimeout", "disconnect", "refresh", "skip", "threshold", "elementRectForRootMargin", "insetTop", "insetRight", "insetBottom", "insetLeft", "rootMargin", "options", "isFirstUpdate", "handleObserve", "entries", "ratio", "intersectionRatio", "setTimeout", "IntersectionObserver", "_e", "observe", "autoUpdate", "update", "ancestorScroll", "ancestorResize", "elementResize", "ResizeObserver", "layoutShift", "animationFrame", "referenceEl", "ancestors", "for<PERSON>ach", "addEventListener", "passive", "cleanupIo", "reobserveFrame", "resizeObserver", "firstEntry", "target", "unobserve", "cancelAnimationFrame", "requestAnimationFrame", "_resizeObserver", "frameId", "prevRefRect", "frameLoop", "nextRefRect", "_resizeObserver2", "removeEventListener", "Map", "mergedOptions", "platformWithCache"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs"], "sourcesContent": ["import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,KAAK,IAAIC,OAAO,EAAEC,aAAa,IAAIC,eAAe,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,IAAI,IAAIC,MAAM,EAAEC,IAAI,IAAIC,MAAM,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,UAAU,IAAIC,YAAY,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,KAAK,IAAIC,OAAO,EAAEC,IAAI,IAAIC,MAAM,EAAEC,eAAe,IAAIC,iBAAiB,QAAQ,mBAAmB;AACxT,SAASC,KAAK,EAAEC,YAAY,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAAQ,oBAAoB;AACzE,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC3T,SAASL,oBAAoB,QAAQ,wBAAwB;AAE7D,SAASM,gBAAgBA,CAACC,OAAO,EAAE;EACjC,MAAMC,GAAG,GAAGnB,gBAAgB,CAACkB,OAAO,CAAC;EACrC;EACA;EACA,IAAIE,KAAK,GAAGC,UAAU,CAACF,GAAG,CAACC,KAAK,CAAC,IAAI,CAAC;EACtC,IAAIE,MAAM,GAAGD,UAAU,CAACF,GAAG,CAACG,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,SAAS,GAAGtB,aAAa,CAACiB,OAAO,CAAC;EACxC,MAAMM,WAAW,GAAGD,SAAS,GAAGL,OAAO,CAACM,WAAW,GAAGJ,KAAK;EAC3D,MAAMK,YAAY,GAAGF,SAAS,GAAGL,OAAO,CAACO,YAAY,GAAGH,MAAM;EAC9D,MAAMI,cAAc,GAAG/B,KAAK,CAACyB,KAAK,CAAC,KAAKI,WAAW,IAAI7B,KAAK,CAAC2B,MAAM,CAAC,KAAKG,YAAY;EACrF,IAAIC,cAAc,EAAE;IAClBN,KAAK,GAAGI,WAAW;IACnBF,MAAM,GAAGG,YAAY;EACvB;EACA,OAAO;IACLL,KAAK;IACLE,MAAM;IACNK,CAAC,EAAED;EACL,CAAC;AACH;AAEA,SAASE,aAAaA,CAACV,OAAO,EAAE;EAC9B,OAAO,CAAChB,SAAS,CAACgB,OAAO,CAAC,GAAGA,OAAO,CAACW,cAAc,GAAGX,OAAO;AAC/D;AAEA,SAASY,QAAQA,CAACZ,OAAO,EAAE;EACzB,MAAMa,UAAU,GAAGH,aAAa,CAACV,OAAO,CAAC;EACzC,IAAI,CAACjB,aAAa,CAAC8B,UAAU,CAAC,EAAE;IAC9B,OAAOnC,YAAY,CAAC,CAAC,CAAC;EACxB;EACA,MAAMoC,IAAI,GAAGD,UAAU,CAACE,qBAAqB,CAAC,CAAC;EAC/C,MAAM;IACJb,KAAK;IACLE,MAAM;IACNK;EACF,CAAC,GAAGV,gBAAgB,CAACc,UAAU,CAAC;EAChC,IAAIG,CAAC,GAAG,CAACP,CAAC,GAAGhC,KAAK,CAACqC,IAAI,CAACZ,KAAK,CAAC,GAAGY,IAAI,CAACZ,KAAK,IAAIA,KAAK;EACpD,IAAIe,CAAC,GAAG,CAACR,CAAC,GAAGhC,KAAK,CAACqC,IAAI,CAACV,MAAM,CAAC,GAAGU,IAAI,CAACV,MAAM,IAAIA,MAAM;;EAEvD;;EAEA,IAAI,CAACY,CAAC,IAAI,CAACE,MAAM,CAACC,QAAQ,CAACH,CAAC,CAAC,EAAE;IAC7BA,CAAC,GAAG,CAAC;EACP;EACA,IAAI,CAACC,CAAC,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACF,CAAC,CAAC,EAAE;IAC7BA,CAAC,GAAG,CAAC;EACP;EACA,OAAO;IACLD,CAAC;IACDC;EACF,CAAC;AACH;AAEA,MAAMG,SAAS,GAAG,aAAa1C,YAAY,CAAC,CAAC,CAAC;AAC9C,SAAS2C,gBAAgBA,CAACrB,OAAO,EAAE;EACjC,MAAMsB,GAAG,GAAGrC,SAAS,CAACe,OAAO,CAAC;EAC9B,IAAI,CAACd,QAAQ,CAAC,CAAC,IAAI,CAACoC,GAAG,CAACC,cAAc,EAAE;IACtC,OAAOH,SAAS;EAClB;EACA,OAAO;IACLJ,CAAC,EAAEM,GAAG,CAACC,cAAc,CAACC,UAAU;IAChCP,CAAC,EAAEK,GAAG,CAACC,cAAc,CAACE;EACxB,CAAC;AACH;AACA,SAASC,sBAAsBA,CAAC1B,OAAO,EAAE2B,OAAO,EAAEC,oBAAoB,EAAE;EACtE,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,KAAK;EACjB;EACA,IAAI,CAACC,oBAAoB,IAAID,OAAO,IAAIC,oBAAoB,KAAK3C,SAAS,CAACe,OAAO,CAAC,EAAE;IACnF,OAAO,KAAK;EACd;EACA,OAAO2B,OAAO;AAChB;AAEA,SAASZ,qBAAqBA,CAACf,OAAO,EAAE6B,YAAY,EAAEC,eAAe,EAAEC,YAAY,EAAE;EACnF,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,KAAK;EACtB;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,KAAK;EACzB;EACA,MAAME,UAAU,GAAGhC,OAAO,CAACe,qBAAqB,CAAC,CAAC;EAClD,MAAMF,UAAU,GAAGH,aAAa,CAACV,OAAO,CAAC;EACzC,IAAIiC,KAAK,GAAGvD,YAAY,CAAC,CAAC,CAAC;EAC3B,IAAImD,YAAY,EAAE;IAChB,IAAIE,YAAY,EAAE;MAChB,IAAI/C,SAAS,CAAC+C,YAAY,CAAC,EAAE;QAC3BE,KAAK,GAAGrB,QAAQ,CAACmB,YAAY,CAAC;MAChC;IACF,CAAC,MAAM;MACLE,KAAK,GAAGrB,QAAQ,CAACZ,OAAO,CAAC;IAC3B;EACF;EACA,MAAMkC,aAAa,GAAGR,sBAAsB,CAACb,UAAU,EAAEiB,eAAe,EAAEC,YAAY,CAAC,GAAGV,gBAAgB,CAACR,UAAU,CAAC,GAAGnC,YAAY,CAAC,CAAC,CAAC;EACxI,IAAIsC,CAAC,GAAG,CAACgB,UAAU,CAACG,IAAI,GAAGD,aAAa,CAAClB,CAAC,IAAIiB,KAAK,CAACjB,CAAC;EACrD,IAAIC,CAAC,GAAG,CAACe,UAAU,CAACI,GAAG,GAAGF,aAAa,CAACjB,CAAC,IAAIgB,KAAK,CAAChB,CAAC;EACpD,IAAIf,KAAK,GAAG8B,UAAU,CAAC9B,KAAK,GAAG+B,KAAK,CAACjB,CAAC;EACtC,IAAIZ,MAAM,GAAG4B,UAAU,CAAC5B,MAAM,GAAG6B,KAAK,CAAChB,CAAC;EACxC,IAAIJ,UAAU,EAAE;IACd,MAAMS,GAAG,GAAGrC,SAAS,CAAC4B,UAAU,CAAC;IACjC,MAAMwB,SAAS,GAAGN,YAAY,IAAI/C,SAAS,CAAC+C,YAAY,CAAC,GAAG9C,SAAS,CAAC8C,YAAY,CAAC,GAAGA,YAAY;IAClG,IAAIO,UAAU,GAAGhB,GAAG;IACpB,IAAIiB,aAAa,GAAGpD,eAAe,CAACmD,UAAU,CAAC;IAC/C,OAAOC,aAAa,IAAIR,YAAY,IAAIM,SAAS,KAAKC,UAAU,EAAE;MAChE,MAAME,WAAW,GAAG5B,QAAQ,CAAC2B,aAAa,CAAC;MAC3C,MAAME,UAAU,GAAGF,aAAa,CAACxB,qBAAqB,CAAC,CAAC;MACxD,MAAMd,GAAG,GAAGnB,gBAAgB,CAACyD,aAAa,CAAC;MAC3C,MAAMJ,IAAI,GAAGM,UAAU,CAACN,IAAI,GAAG,CAACI,aAAa,CAACG,UAAU,GAAGvC,UAAU,CAACF,GAAG,CAAC0C,WAAW,CAAC,IAAIH,WAAW,CAACxB,CAAC;MACvG,MAAMoB,GAAG,GAAGK,UAAU,CAACL,GAAG,GAAG,CAACG,aAAa,CAACK,SAAS,GAAGzC,UAAU,CAACF,GAAG,CAAC4C,UAAU,CAAC,IAAIL,WAAW,CAACvB,CAAC;MACnGD,CAAC,IAAIwB,WAAW,CAACxB,CAAC;MAClBC,CAAC,IAAIuB,WAAW,CAACvB,CAAC;MAClBf,KAAK,IAAIsC,WAAW,CAACxB,CAAC;MACtBZ,MAAM,IAAIoC,WAAW,CAACvB,CAAC;MACvBD,CAAC,IAAImB,IAAI;MACTlB,CAAC,IAAImB,GAAG;MACRE,UAAU,GAAGrD,SAAS,CAACsD,aAAa,CAAC;MACrCA,aAAa,GAAGpD,eAAe,CAACmD,UAAU,CAAC;IAC7C;EACF;EACA,OAAOpF,gBAAgB,CAAC;IACtBgD,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA,SAAS6B,mBAAmBA,CAAC9C,OAAO,EAAEc,IAAI,EAAE;EAC1C,MAAMiC,UAAU,GAAG3D,aAAa,CAACY,OAAO,CAAC,CAACgD,UAAU;EACpD,IAAI,CAAClC,IAAI,EAAE;IACT,OAAOC,qBAAqB,CAAC1B,kBAAkB,CAACW,OAAO,CAAC,CAAC,CAACmC,IAAI,GAAGY,UAAU;EAC7E;EACA,OAAOjC,IAAI,CAACqB,IAAI,GAAGY,UAAU;AAC/B;AAEA,SAASE,aAAaA,CAACC,eAAe,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;EAChE,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAC/BA,gBAAgB,GAAG,KAAK;EAC1B;EACA,MAAMC,QAAQ,GAAGH,eAAe,CAACnC,qBAAqB,CAAC,CAAC;EACxD,MAAMC,CAAC,GAAGqC,QAAQ,CAAClB,IAAI,GAAGgB,MAAM,CAACH,UAAU,IAAII,gBAAgB,GAAG,CAAC;EACnE;EACAN,mBAAmB,CAACI,eAAe,EAAEG,QAAQ,CAAC,CAAC;EAC/C,MAAMpC,CAAC,GAAGoC,QAAQ,CAACjB,GAAG,GAAGe,MAAM,CAACG,SAAS;EACzC,OAAO;IACLtC,CAAC;IACDC;EACF,CAAC;AACH;AAEA,SAASsC,qDAAqDA,CAACC,IAAI,EAAE;EACnE,IAAI;IACFC,QAAQ;IACR3C,IAAI;IACJiB,YAAY;IACZ2B;EACF,CAAC,GAAGF,IAAI;EACR,MAAM7B,OAAO,GAAG+B,QAAQ,KAAK,OAAO;EACpC,MAAMR,eAAe,GAAG7D,kBAAkB,CAAC0C,YAAY,CAAC;EACxD,MAAM4B,QAAQ,GAAGF,QAAQ,GAAGnE,UAAU,CAACmE,QAAQ,CAACG,QAAQ,CAAC,GAAG,KAAK;EACjE,IAAI7B,YAAY,KAAKmB,eAAe,IAAIS,QAAQ,IAAIhC,OAAO,EAAE;IAC3D,OAAOb,IAAI;EACb;EACA,IAAIqC,MAAM,GAAG;IACXH,UAAU,EAAE,CAAC;IACbM,SAAS,EAAE;EACb,CAAC;EACD,IAAIrB,KAAK,GAAGvD,YAAY,CAAC,CAAC,CAAC;EAC3B,MAAMmF,OAAO,GAAGnF,YAAY,CAAC,CAAC,CAAC;EAC/B,MAAMoF,uBAAuB,GAAG/E,aAAa,CAACgD,YAAY,CAAC;EAC3D,IAAI+B,uBAAuB,IAAI,CAACA,uBAAuB,IAAI,CAACnC,OAAO,EAAE;IACnE,IAAIpC,WAAW,CAACwC,YAAY,CAAC,KAAK,MAAM,IAAIvC,iBAAiB,CAAC0D,eAAe,CAAC,EAAE;MAC9EC,MAAM,GAAG/D,aAAa,CAAC2C,YAAY,CAAC;IACtC;IACA,IAAIhD,aAAa,CAACgD,YAAY,CAAC,EAAE;MAC/B,MAAMgC,UAAU,GAAGhD,qBAAqB,CAACgB,YAAY,CAAC;MACtDE,KAAK,GAAGrB,QAAQ,CAACmB,YAAY,CAAC;MAC9B8B,OAAO,CAAC7C,CAAC,GAAG+C,UAAU,CAAC/C,CAAC,GAAGe,YAAY,CAACW,UAAU;MAClDmB,OAAO,CAAC5C,CAAC,GAAG8C,UAAU,CAAC9C,CAAC,GAAGc,YAAY,CAACa,SAAS;IACnD;EACF;EACA,MAAMoB,UAAU,GAAGd,eAAe,IAAI,CAACY,uBAAuB,IAAI,CAACnC,OAAO,GAAGsB,aAAa,CAACC,eAAe,EAAEC,MAAM,EAAE,IAAI,CAAC,GAAGzE,YAAY,CAAC,CAAC,CAAC;EAC3I,OAAO;IACLwB,KAAK,EAAEY,IAAI,CAACZ,KAAK,GAAG+B,KAAK,CAACjB,CAAC;IAC3BZ,MAAM,EAAEU,IAAI,CAACV,MAAM,GAAG6B,KAAK,CAAChB,CAAC;IAC7BD,CAAC,EAAEF,IAAI,CAACE,CAAC,GAAGiB,KAAK,CAACjB,CAAC,GAAGmC,MAAM,CAACH,UAAU,GAAGf,KAAK,CAACjB,CAAC,GAAG6C,OAAO,CAAC7C,CAAC,GAAGgD,UAAU,CAAChD,CAAC;IAC5EC,CAAC,EAAEH,IAAI,CAACG,CAAC,GAAGgB,KAAK,CAAChB,CAAC,GAAGkC,MAAM,CAACG,SAAS,GAAGrB,KAAK,CAAChB,CAAC,GAAG4C,OAAO,CAAC5C,CAAC,GAAG+C,UAAU,CAAC/C;EAC5E,CAAC;AACH;AAEA,SAASgD,cAAcA,CAACjE,OAAO,EAAE;EAC/B,OAAOkE,KAAK,CAACC,IAAI,CAACnE,OAAO,CAACiE,cAAc,CAAC,CAAC,CAAC;AAC7C;;AAEA;AACA;AACA,SAASG,eAAeA,CAACpE,OAAO,EAAE;EAChC,MAAMqE,IAAI,GAAGhF,kBAAkB,CAACW,OAAO,CAAC;EACxC,MAAMmD,MAAM,GAAG/D,aAAa,CAACY,OAAO,CAAC;EACrC,MAAMsE,IAAI,GAAGtE,OAAO,CAACuE,aAAa,CAACD,IAAI;EACvC,MAAMpE,KAAK,GAAGvB,GAAG,CAAC0F,IAAI,CAACG,WAAW,EAAEH,IAAI,CAACI,WAAW,EAAEH,IAAI,CAACE,WAAW,EAAEF,IAAI,CAACG,WAAW,CAAC;EACzF,MAAMrE,MAAM,GAAGzB,GAAG,CAAC0F,IAAI,CAACK,YAAY,EAAEL,IAAI,CAACM,YAAY,EAAEL,IAAI,CAACI,YAAY,EAAEJ,IAAI,CAACK,YAAY,CAAC;EAC9F,IAAI3D,CAAC,GAAG,CAACmC,MAAM,CAACH,UAAU,GAAGF,mBAAmB,CAAC9C,OAAO,CAAC;EACzD,MAAMiB,CAAC,GAAG,CAACkC,MAAM,CAACG,SAAS;EAC3B,IAAIxE,gBAAgB,CAACwF,IAAI,CAAC,CAACM,SAAS,KAAK,KAAK,EAAE;IAC9C5D,CAAC,IAAIrC,GAAG,CAAC0F,IAAI,CAACI,WAAW,EAAEH,IAAI,CAACG,WAAW,CAAC,GAAGvE,KAAK;EACtD;EACA,OAAO;IACLA,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC;AACH;AAEA,SAAS4D,eAAeA,CAAC7E,OAAO,EAAE0D,QAAQ,EAAE;EAC1C,MAAMpC,GAAG,GAAGrC,SAAS,CAACe,OAAO,CAAC;EAC9B,MAAMqE,IAAI,GAAGhF,kBAAkB,CAACW,OAAO,CAAC;EACxC,MAAMuB,cAAc,GAAGD,GAAG,CAACC,cAAc;EACzC,IAAIrB,KAAK,GAAGmE,IAAI,CAACI,WAAW;EAC5B,IAAIrE,MAAM,GAAGiE,IAAI,CAACM,YAAY;EAC9B,IAAI3D,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIM,cAAc,EAAE;IAClBrB,KAAK,GAAGqB,cAAc,CAACrB,KAAK;IAC5BE,MAAM,GAAGmB,cAAc,CAACnB,MAAM;IAC9B,MAAM0E,mBAAmB,GAAG5F,QAAQ,CAAC,CAAC;IACtC,IAAI,CAAC4F,mBAAmB,IAAIA,mBAAmB,IAAIpB,QAAQ,KAAK,OAAO,EAAE;MACvE1C,CAAC,GAAGO,cAAc,CAACC,UAAU;MAC7BP,CAAC,GAAGM,cAAc,CAACE,SAAS;IAC9B;EACF;EACA,OAAO;IACLvB,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC;AACH;;AAEA;AACA,SAAS8D,0BAA0BA,CAAC/E,OAAO,EAAE0D,QAAQ,EAAE;EACrD,MAAM1B,UAAU,GAAGjB,qBAAqB,CAACf,OAAO,EAAE,IAAI,EAAE0D,QAAQ,KAAK,OAAO,CAAC;EAC7E,MAAMtB,GAAG,GAAGJ,UAAU,CAACI,GAAG,GAAGpC,OAAO,CAAC4C,SAAS;EAC9C,MAAMT,IAAI,GAAGH,UAAU,CAACG,IAAI,GAAGnC,OAAO,CAAC0C,UAAU;EACjD,MAAMT,KAAK,GAAGlD,aAAa,CAACiB,OAAO,CAAC,GAAGY,QAAQ,CAACZ,OAAO,CAAC,GAAGtB,YAAY,CAAC,CAAC,CAAC;EAC1E,MAAMwB,KAAK,GAAGF,OAAO,CAACyE,WAAW,GAAGxC,KAAK,CAACjB,CAAC;EAC3C,MAAMZ,MAAM,GAAGJ,OAAO,CAAC2E,YAAY,GAAG1C,KAAK,CAAChB,CAAC;EAC7C,MAAMD,CAAC,GAAGmB,IAAI,GAAGF,KAAK,CAACjB,CAAC;EACxB,MAAMC,CAAC,GAAGmB,GAAG,GAAGH,KAAK,CAAChB,CAAC;EACvB,OAAO;IACLf,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC;AACH;AACA,SAAS+D,iCAAiCA,CAAChF,OAAO,EAAEiF,gBAAgB,EAAEvB,QAAQ,EAAE;EAC9E,IAAI5C,IAAI;EACR,IAAImE,gBAAgB,KAAK,UAAU,EAAE;IACnCnE,IAAI,GAAG+D,eAAe,CAAC7E,OAAO,EAAE0D,QAAQ,CAAC;EAC3C,CAAC,MAAM,IAAIuB,gBAAgB,KAAK,UAAU,EAAE;IAC1CnE,IAAI,GAAGsD,eAAe,CAAC/E,kBAAkB,CAACW,OAAO,CAAC,CAAC;EACrD,CAAC,MAAM,IAAIhB,SAAS,CAACiG,gBAAgB,CAAC,EAAE;IACtCnE,IAAI,GAAGiE,0BAA0B,CAACE,gBAAgB,EAAEvB,QAAQ,CAAC;EAC/D,CAAC,MAAM;IACL,MAAMxB,aAAa,GAAGb,gBAAgB,CAACrB,OAAO,CAAC;IAC/Cc,IAAI,GAAG;MACLE,CAAC,EAAEiE,gBAAgB,CAACjE,CAAC,GAAGkB,aAAa,CAAClB,CAAC;MACvCC,CAAC,EAAEgE,gBAAgB,CAAChE,CAAC,GAAGiB,aAAa,CAACjB,CAAC;MACvCf,KAAK,EAAE+E,gBAAgB,CAAC/E,KAAK;MAC7BE,MAAM,EAAE6E,gBAAgB,CAAC7E;IAC3B,CAAC;EACH;EACA,OAAOlD,gBAAgB,CAAC4D,IAAI,CAAC;AAC/B;AACA,SAASoE,wBAAwBA,CAAClF,OAAO,EAAEmF,QAAQ,EAAE;EACnD,MAAMC,UAAU,GAAG1F,aAAa,CAACM,OAAO,CAAC;EACzC,IAAIoF,UAAU,KAAKD,QAAQ,IAAI,CAACnG,SAAS,CAACoG,UAAU,CAAC,IAAIzF,qBAAqB,CAACyF,UAAU,CAAC,EAAE;IAC1F,OAAO,KAAK;EACd;EACA,OAAOtG,gBAAgB,CAACsG,UAAU,CAAC,CAACC,QAAQ,KAAK,OAAO,IAAIH,wBAAwB,CAACE,UAAU,EAAED,QAAQ,CAAC;AAC5G;;AAEA;AACA;AACA;AACA,SAASG,2BAA2BA,CAACtF,OAAO,EAAEuF,KAAK,EAAE;EACnD,MAAMC,YAAY,GAAGD,KAAK,CAACE,GAAG,CAACzF,OAAO,CAAC;EACvC,IAAIwF,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EACA,IAAIE,MAAM,GAAGjG,oBAAoB,CAACO,OAAO,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC2F,MAAM,CAACC,EAAE,IAAI5G,SAAS,CAAC4G,EAAE,CAAC,IAAIrG,WAAW,CAACqG,EAAE,CAAC,KAAK,MAAM,CAAC;EAC/G,IAAIC,mCAAmC,GAAG,IAAI;EAC9C,MAAMC,cAAc,GAAGhH,gBAAgB,CAACkB,OAAO,CAAC,CAACqF,QAAQ,KAAK,OAAO;EACrE,IAAIU,WAAW,GAAGD,cAAc,GAAGpG,aAAa,CAACM,OAAO,CAAC,GAAGA,OAAO;;EAEnE;EACA,OAAOhB,SAAS,CAAC+G,WAAW,CAAC,IAAI,CAACpG,qBAAqB,CAACoG,WAAW,CAAC,EAAE;IACpE,MAAMC,aAAa,GAAGlH,gBAAgB,CAACiH,WAAW,CAAC;IACnD,MAAME,uBAAuB,GAAGrG,iBAAiB,CAACmG,WAAW,CAAC;IAC9D,IAAI,CAACE,uBAAuB,IAAID,aAAa,CAACX,QAAQ,KAAK,OAAO,EAAE;MAClEQ,mCAAmC,GAAG,IAAI;IAC5C;IACA,MAAMK,qBAAqB,GAAGJ,cAAc,GAAG,CAACG,uBAAuB,IAAI,CAACJ,mCAAmC,GAAG,CAACI,uBAAuB,IAAID,aAAa,CAACX,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACQ,mCAAmC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAACM,QAAQ,CAACN,mCAAmC,CAACR,QAAQ,CAAC,IAAI7F,iBAAiB,CAACuG,WAAW,CAAC,IAAI,CAACE,uBAAuB,IAAIf,wBAAwB,CAAClF,OAAO,EAAE+F,WAAW,CAAC;IAC1Z,IAAIG,qBAAqB,EAAE;MACzB;MACAR,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACS,QAAQ,IAAIA,QAAQ,KAAKL,WAAW,CAAC;IAC9D,CAAC,MAAM;MACL;MACAF,mCAAmC,GAAGG,aAAa;IACrD;IACAD,WAAW,GAAGrG,aAAa,CAACqG,WAAW,CAAC;EAC1C;EACAR,KAAK,CAACc,GAAG,CAACrG,OAAO,EAAE0F,MAAM,CAAC;EAC1B,OAAOA,MAAM;AACf;;AAEA;AACA;AACA,SAASY,eAAeA,CAAC9C,IAAI,EAAE;EAC7B,IAAI;IACFxD,OAAO;IACPuG,QAAQ;IACRC,YAAY;IACZ9C;EACF,CAAC,GAAGF,IAAI;EACR,MAAMiD,wBAAwB,GAAGF,QAAQ,KAAK,mBAAmB,GAAGjH,UAAU,CAACU,OAAO,CAAC,GAAG,EAAE,GAAGsF,2BAA2B,CAACtF,OAAO,EAAE,IAAI,CAAC0G,EAAE,CAAC,GAAG,EAAE,CAACC,MAAM,CAACJ,QAAQ,CAAC;EAClK,MAAMK,iBAAiB,GAAG,CAAC,GAAGH,wBAAwB,EAAED,YAAY,CAAC;EACrE,MAAMK,qBAAqB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;EAClD,MAAME,YAAY,GAAGF,iBAAiB,CAACG,MAAM,CAAC,CAACC,OAAO,EAAE/B,gBAAgB,KAAK;IAC3E,MAAMnE,IAAI,GAAGkE,iCAAiC,CAAChF,OAAO,EAAEiF,gBAAgB,EAAEvB,QAAQ,CAAC;IACnFsD,OAAO,CAAC5E,GAAG,GAAGzD,GAAG,CAACmC,IAAI,CAACsB,GAAG,EAAE4E,OAAO,CAAC5E,GAAG,CAAC;IACxC4E,OAAO,CAACC,KAAK,GAAGrI,GAAG,CAACkC,IAAI,CAACmG,KAAK,EAAED,OAAO,CAACC,KAAK,CAAC;IAC9CD,OAAO,CAACE,MAAM,GAAGtI,GAAG,CAACkC,IAAI,CAACoG,MAAM,EAAEF,OAAO,CAACE,MAAM,CAAC;IACjDF,OAAO,CAAC7E,IAAI,GAAGxD,GAAG,CAACmC,IAAI,CAACqB,IAAI,EAAE6E,OAAO,CAAC7E,IAAI,CAAC;IAC3C,OAAO6E,OAAO;EAChB,CAAC,EAAEhC,iCAAiC,CAAChF,OAAO,EAAE6G,qBAAqB,EAAEnD,QAAQ,CAAC,CAAC;EAC/E,OAAO;IACLxD,KAAK,EAAE4G,YAAY,CAACG,KAAK,GAAGH,YAAY,CAAC3E,IAAI;IAC7C/B,MAAM,EAAE0G,YAAY,CAACI,MAAM,GAAGJ,YAAY,CAAC1E,GAAG;IAC9CpB,CAAC,EAAE8F,YAAY,CAAC3E,IAAI;IACpBlB,CAAC,EAAE6F,YAAY,CAAC1E;EAClB,CAAC;AACH;AAEA,SAAS+E,aAAaA,CAACnH,OAAO,EAAE;EAC9B,MAAM;IACJE,KAAK;IACLE;EACF,CAAC,GAAGL,gBAAgB,CAACC,OAAO,CAAC;EAC7B,OAAO;IACLE,KAAK;IACLE;EACF,CAAC;AACH;AAEA,SAASgH,6BAA6BA,CAACpH,OAAO,EAAE+B,YAAY,EAAE2B,QAAQ,EAAE;EACtE,MAAMI,uBAAuB,GAAG/E,aAAa,CAACgD,YAAY,CAAC;EAC3D,MAAMmB,eAAe,GAAG7D,kBAAkB,CAAC0C,YAAY,CAAC;EACxD,MAAMJ,OAAO,GAAG+B,QAAQ,KAAK,OAAO;EACpC,MAAM5C,IAAI,GAAGC,qBAAqB,CAACf,OAAO,EAAE,IAAI,EAAE2B,OAAO,EAAEI,YAAY,CAAC;EACxE,IAAIoB,MAAM,GAAG;IACXH,UAAU,EAAE,CAAC;IACbM,SAAS,EAAE;EACb,CAAC;EACD,MAAMO,OAAO,GAAGnF,YAAY,CAAC,CAAC,CAAC;;EAE/B;EACA;EACA,SAAS2I,yBAAyBA,CAAA,EAAG;IACnCxD,OAAO,CAAC7C,CAAC,GAAG8B,mBAAmB,CAACI,eAAe,CAAC;EAClD;EACA,IAAIY,uBAAuB,IAAI,CAACA,uBAAuB,IAAI,CAACnC,OAAO,EAAE;IACnE,IAAIpC,WAAW,CAACwC,YAAY,CAAC,KAAK,MAAM,IAAIvC,iBAAiB,CAAC0D,eAAe,CAAC,EAAE;MAC9EC,MAAM,GAAG/D,aAAa,CAAC2C,YAAY,CAAC;IACtC;IACA,IAAI+B,uBAAuB,EAAE;MAC3B,MAAMC,UAAU,GAAGhD,qBAAqB,CAACgB,YAAY,EAAE,IAAI,EAAEJ,OAAO,EAAEI,YAAY,CAAC;MACnF8B,OAAO,CAAC7C,CAAC,GAAG+C,UAAU,CAAC/C,CAAC,GAAGe,YAAY,CAACW,UAAU;MAClDmB,OAAO,CAAC5C,CAAC,GAAG8C,UAAU,CAAC9C,CAAC,GAAGc,YAAY,CAACa,SAAS;IACnD,CAAC,MAAM,IAAIM,eAAe,EAAE;MAC1BmE,yBAAyB,CAAC,CAAC;IAC7B;EACF;EACA,IAAI1F,OAAO,IAAI,CAACmC,uBAAuB,IAAIZ,eAAe,EAAE;IAC1DmE,yBAAyB,CAAC,CAAC;EAC7B;EACA,MAAMrD,UAAU,GAAGd,eAAe,IAAI,CAACY,uBAAuB,IAAI,CAACnC,OAAO,GAAGsB,aAAa,CAACC,eAAe,EAAEC,MAAM,CAAC,GAAGzE,YAAY,CAAC,CAAC,CAAC;EACrI,MAAMsC,CAAC,GAAGF,IAAI,CAACqB,IAAI,GAAGgB,MAAM,CAACH,UAAU,GAAGa,OAAO,CAAC7C,CAAC,GAAGgD,UAAU,CAAChD,CAAC;EAClE,MAAMC,CAAC,GAAGH,IAAI,CAACsB,GAAG,GAAGe,MAAM,CAACG,SAAS,GAAGO,OAAO,CAAC5C,CAAC,GAAG+C,UAAU,CAAC/C,CAAC;EAChE,OAAO;IACLD,CAAC;IACDC,CAAC;IACDf,KAAK,EAAEY,IAAI,CAACZ,KAAK;IACjBE,MAAM,EAAEU,IAAI,CAACV;EACf,CAAC;AACH;AAEA,SAASkH,kBAAkBA,CAACtH,OAAO,EAAE;EACnC,OAAOlB,gBAAgB,CAACkB,OAAO,CAAC,CAACqF,QAAQ,KAAK,QAAQ;AACxD;AAEA,SAASkC,mBAAmBA,CAACvH,OAAO,EAAEwH,QAAQ,EAAE;EAC9C,IAAI,CAACzI,aAAa,CAACiB,OAAO,CAAC,IAAIlB,gBAAgB,CAACkB,OAAO,CAAC,CAACqF,QAAQ,KAAK,OAAO,EAAE;IAC7E,OAAO,IAAI;EACb;EACA,IAAImC,QAAQ,EAAE;IACZ,OAAOA,QAAQ,CAACxH,OAAO,CAAC;EAC1B;EACA,IAAIyH,eAAe,GAAGzH,OAAO,CAAC+B,YAAY;;EAE1C;EACA;EACA;EACA;EACA,IAAI1C,kBAAkB,CAACW,OAAO,CAAC,KAAKyH,eAAe,EAAE;IACnDA,eAAe,GAAGA,eAAe,CAAClD,aAAa,CAACD,IAAI;EACtD;EACA,OAAOmD,eAAe;AACxB;;AAEA;AACA;AACA,SAASC,eAAeA,CAAC1H,OAAO,EAAEwH,QAAQ,EAAE;EAC1C,MAAMlG,GAAG,GAAGrC,SAAS,CAACe,OAAO,CAAC;EAC9B,IAAIV,UAAU,CAACU,OAAO,CAAC,EAAE;IACvB,OAAOsB,GAAG;EACZ;EACA,IAAI,CAACvC,aAAa,CAACiB,OAAO,CAAC,EAAE;IAC3B,IAAI2H,eAAe,GAAGjI,aAAa,CAACM,OAAO,CAAC;IAC5C,OAAO2H,eAAe,IAAI,CAAChI,qBAAqB,CAACgI,eAAe,CAAC,EAAE;MACjE,IAAI3I,SAAS,CAAC2I,eAAe,CAAC,IAAI,CAACL,kBAAkB,CAACK,eAAe,CAAC,EAAE;QACtE,OAAOA,eAAe;MACxB;MACAA,eAAe,GAAGjI,aAAa,CAACiI,eAAe,CAAC;IAClD;IACA,OAAOrG,GAAG;EACZ;EACA,IAAIS,YAAY,GAAGwF,mBAAmB,CAACvH,OAAO,EAAEwH,QAAQ,CAAC;EACzD,OAAOzF,YAAY,IAAIlC,cAAc,CAACkC,YAAY,CAAC,IAAIuF,kBAAkB,CAACvF,YAAY,CAAC,EAAE;IACvFA,YAAY,GAAGwF,mBAAmB,CAACxF,YAAY,EAAEyF,QAAQ,CAAC;EAC5D;EACA,IAAIzF,YAAY,IAAIpC,qBAAqB,CAACoC,YAAY,CAAC,IAAIuF,kBAAkB,CAACvF,YAAY,CAAC,IAAI,CAACnC,iBAAiB,CAACmC,YAAY,CAAC,EAAE;IAC/H,OAAOT,GAAG;EACZ;EACA,OAAOS,YAAY,IAAIjC,kBAAkB,CAACE,OAAO,CAAC,IAAIsB,GAAG;AAC3D;AAEA,MAAMsG,eAAe,GAAG,eAAAA,CAAgBC,IAAI,EAAE;EAC5C,MAAMC,iBAAiB,GAAG,IAAI,CAACJ,eAAe,IAAIA,eAAe;EACjE,MAAMK,eAAe,GAAG,IAAI,CAACZ,aAAa;EAC1C,MAAMa,kBAAkB,GAAG,MAAMD,eAAe,CAACF,IAAI,CAACjE,QAAQ,CAAC;EAC/D,OAAO;IACLqE,SAAS,EAAEb,6BAA6B,CAACS,IAAI,CAACI,SAAS,EAAE,MAAMH,iBAAiB,CAACD,IAAI,CAACjE,QAAQ,CAAC,EAAEiE,IAAI,CAACnE,QAAQ,CAAC;IAC/GE,QAAQ,EAAE;MACR5C,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJf,KAAK,EAAE8H,kBAAkB,CAAC9H,KAAK;MAC/BE,MAAM,EAAE4H,kBAAkB,CAAC5H;IAC7B;EACF,CAAC;AACH,CAAC;AAED,SAAS8H,KAAKA,CAAClI,OAAO,EAAE;EACtB,OAAOlB,gBAAgB,CAACkB,OAAO,CAAC,CAAC4E,SAAS,KAAK,KAAK;AACtD;AAEA,MAAMuD,QAAQ,GAAG;EACf5E,qDAAqD;EACrDlE,kBAAkB;EAClBiH,eAAe;EACfoB,eAAe;EACfE,eAAe;EACf3D,cAAc;EACdkD,aAAa;EACbvG,QAAQ;EACR5B,SAAS;EACTkJ;AACF,CAAC;AAED,SAASE,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAOD,CAAC,CAACrH,CAAC,KAAKsH,CAAC,CAACtH,CAAC,IAAIqH,CAAC,CAACpH,CAAC,KAAKqH,CAAC,CAACrH,CAAC,IAAIoH,CAAC,CAACnI,KAAK,KAAKoI,CAAC,CAACpI,KAAK,IAAImI,CAAC,CAACjI,MAAM,KAAKkI,CAAC,CAAClI,MAAM;AACnF;;AAEA;AACA,SAASmI,WAAWA,CAACvI,OAAO,EAAEwI,MAAM,EAAE;EACpC,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,SAAS;EACb,MAAMC,IAAI,GAAGtJ,kBAAkB,CAACW,OAAO,CAAC;EACxC,SAAS4I,OAAOA,CAAA,EAAG;IACjB,IAAIC,GAAG;IACPC,YAAY,CAACJ,SAAS,CAAC;IACvB,CAACG,GAAG,GAAGJ,EAAE,KAAK,IAAI,IAAII,GAAG,CAACE,UAAU,CAAC,CAAC;IACtCN,EAAE,GAAG,IAAI;EACX;EACA,SAASO,OAAOA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAChC,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,KAAK;IACd;IACA,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBA,SAAS,GAAG,CAAC;IACf;IACAN,OAAO,CAAC,CAAC;IACT,MAAMO,wBAAwB,GAAGnJ,OAAO,CAACe,qBAAqB,CAAC,CAAC;IAChE,MAAM;MACJoB,IAAI;MACJC,GAAG;MACHlC,KAAK;MACLE;IACF,CAAC,GAAG+I,wBAAwB;IAC5B,IAAI,CAACF,IAAI,EAAE;MACTT,MAAM,CAAC,CAAC;IACV;IACA,IAAI,CAACtI,KAAK,IAAI,CAACE,MAAM,EAAE;MACrB;IACF;IACA,MAAMgJ,QAAQ,GAAGvK,KAAK,CAACuD,GAAG,CAAC;IAC3B,MAAMiH,UAAU,GAAGxK,KAAK,CAAC8J,IAAI,CAAClE,WAAW,IAAItC,IAAI,GAAGjC,KAAK,CAAC,CAAC;IAC3D,MAAMoJ,WAAW,GAAGzK,KAAK,CAAC8J,IAAI,CAAChE,YAAY,IAAIvC,GAAG,GAAGhC,MAAM,CAAC,CAAC;IAC7D,MAAMmJ,SAAS,GAAG1K,KAAK,CAACsD,IAAI,CAAC;IAC7B,MAAMqH,UAAU,GAAG,CAACJ,QAAQ,GAAG,KAAK,GAAG,CAACC,UAAU,GAAG,KAAK,GAAG,CAACC,WAAW,GAAG,KAAK,GAAG,CAACC,SAAS,GAAG,IAAI;IACrG,MAAME,OAAO,GAAG;MACdD,UAAU;MACVN,SAAS,EAAEvK,GAAG,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,EAAEsK,SAAS,CAAC,CAAC,IAAI;IAC1C,CAAC;IACD,IAAIQ,aAAa,GAAG,IAAI;IACxB,SAASC,aAAaA,CAACC,OAAO,EAAE;MAC9B,MAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACE,iBAAiB;MAC1C,IAAID,KAAK,KAAKX,SAAS,EAAE;QACvB,IAAI,CAACQ,aAAa,EAAE;UAClB,OAAOV,OAAO,CAAC,CAAC;QAClB;QACA,IAAI,CAACa,KAAK,EAAE;UACV;UACA;UACAnB,SAAS,GAAGqB,UAAU,CAAC,MAAM;YAC3Bf,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;UACtB,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLA,OAAO,CAAC,KAAK,EAAEa,KAAK,CAAC;QACvB;MACF;MACA,IAAIA,KAAK,KAAK,CAAC,IAAI,CAACzB,aAAa,CAACe,wBAAwB,EAAEnJ,OAAO,CAACe,qBAAqB,CAAC,CAAC,CAAC,EAAE;QAC5F;QACA;QACA;QACA;QACA;QACA;QACA;QACAiI,OAAO,CAAC,CAAC;MACX;MACAU,aAAa,GAAG,KAAK;IACvB;;IAEA;IACA;IACA,IAAI;MACFjB,EAAE,GAAG,IAAIuB,oBAAoB,CAACL,aAAa,EAAE;QAC3C,GAAGF,OAAO;QACV;QACAd,IAAI,EAAEA,IAAI,CAACpE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO0F,EAAE,EAAE;MACXxB,EAAE,GAAG,IAAIuB,oBAAoB,CAACL,aAAa,EAAEF,OAAO,CAAC;IACvD;IACAhB,EAAE,CAACyB,OAAO,CAAClK,OAAO,CAAC;EACrB;EACAgJ,OAAO,CAAC,IAAI,CAAC;EACb,OAAOJ,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,UAAUA,CAAClC,SAAS,EAAErE,QAAQ,EAAEwG,MAAM,EAAEX,OAAO,EAAE;EACxD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,MAAM;IACJY,cAAc,GAAG,IAAI;IACrBC,cAAc,GAAG,IAAI;IACrBC,aAAa,GAAG,OAAOC,cAAc,KAAK,UAAU;IACpDC,WAAW,GAAG,OAAOT,oBAAoB,KAAK,UAAU;IACxDU,cAAc,GAAG;EACnB,CAAC,GAAGjB,OAAO;EACX,MAAMkB,WAAW,GAAGjK,aAAa,CAACuH,SAAS,CAAC;EAC5C,MAAM2C,SAAS,GAAGP,cAAc,IAAIC,cAAc,GAAG,CAAC,IAAIK,WAAW,GAAGlL,oBAAoB,CAACkL,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,GAAGlL,oBAAoB,CAACmE,QAAQ,CAAC,CAAC,GAAG,EAAE;EACxJgH,SAAS,CAACC,OAAO,CAACzE,QAAQ,IAAI;IAC5BiE,cAAc,IAAIjE,QAAQ,CAAC0E,gBAAgB,CAAC,QAAQ,EAAEV,MAAM,EAAE;MAC5DW,OAAO,EAAE;IACX,CAAC,CAAC;IACFT,cAAc,IAAIlE,QAAQ,CAAC0E,gBAAgB,CAAC,QAAQ,EAAEV,MAAM,CAAC;EAC/D,CAAC,CAAC;EACF,MAAMY,SAAS,GAAGL,WAAW,IAAIF,WAAW,GAAGlC,WAAW,CAACoC,WAAW,EAAEP,MAAM,CAAC,GAAG,IAAI;EACtF,IAAIa,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIX,aAAa,EAAE;IACjBW,cAAc,GAAG,IAAIV,cAAc,CAAChH,IAAI,IAAI;MAC1C,IAAI,CAAC2H,UAAU,CAAC,GAAG3H,IAAI;MACvB,IAAI2H,UAAU,IAAIA,UAAU,CAACC,MAAM,KAAKT,WAAW,IAAIO,cAAc,EAAE;QACrE;QACA;QACAA,cAAc,CAACG,SAAS,CAACzH,QAAQ,CAAC;QAClC0H,oBAAoB,CAACL,cAAc,CAAC;QACpCA,cAAc,GAAGM,qBAAqB,CAAC,MAAM;UAC3C,IAAIC,eAAe;UACnB,CAACA,eAAe,GAAGN,cAAc,KAAK,IAAI,IAAIM,eAAe,CAACtB,OAAO,CAACtG,QAAQ,CAAC;QACjF,CAAC,CAAC;MACJ;MACAwG,MAAM,CAAC,CAAC;IACV,CAAC,CAAC;IACF,IAAIO,WAAW,IAAI,CAACD,cAAc,EAAE;MAClCQ,cAAc,CAAChB,OAAO,CAACS,WAAW,CAAC;IACrC;IACAO,cAAc,CAAChB,OAAO,CAACtG,QAAQ,CAAC;EAClC;EACA,IAAI6H,OAAO;EACX,IAAIC,WAAW,GAAGhB,cAAc,GAAG3J,qBAAqB,CAACkH,SAAS,CAAC,GAAG,IAAI;EAC1E,IAAIyC,cAAc,EAAE;IAClBiB,SAAS,CAAC,CAAC;EACb;EACA,SAASA,SAASA,CAAA,EAAG;IACnB,MAAMC,WAAW,GAAG7K,qBAAqB,CAACkH,SAAS,CAAC;IACpD,IAAIyD,WAAW,IAAI,CAACtD,aAAa,CAACsD,WAAW,EAAEE,WAAW,CAAC,EAAE;MAC3DxB,MAAM,CAAC,CAAC;IACV;IACAsB,WAAW,GAAGE,WAAW;IACzBH,OAAO,GAAGF,qBAAqB,CAACI,SAAS,CAAC;EAC5C;EACAvB,MAAM,CAAC,CAAC;EACR,OAAO,MAAM;IACX,IAAIyB,gBAAgB;IACpBjB,SAAS,CAACC,OAAO,CAACzE,QAAQ,IAAI;MAC5BiE,cAAc,IAAIjE,QAAQ,CAAC0F,mBAAmB,CAAC,QAAQ,EAAE1B,MAAM,CAAC;MAChEE,cAAc,IAAIlE,QAAQ,CAAC0F,mBAAmB,CAAC,QAAQ,EAAE1B,MAAM,CAAC;IAClE,CAAC,CAAC;IACFY,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC,CAAC;IAChC,CAACa,gBAAgB,GAAGX,cAAc,KAAK,IAAI,IAAIW,gBAAgB,CAAC9C,UAAU,CAAC,CAAC;IAC5EmC,cAAc,GAAG,IAAI;IACrB,IAAIR,cAAc,EAAE;MAClBY,oBAAoB,CAACG,OAAO,CAAC;IAC/B;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMlO,cAAc,GAAGC,gBAAgB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,MAAM,GAAGC,QAAQ;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMb,aAAa,GAAGC,eAAe;;AAErC;AACA;AACA;AACA;AACA;AACA,MAAMa,KAAK,GAAGC,OAAO;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMX,IAAI,GAAGC,MAAM;;AAEnB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,IAAI,GAAGC,MAAM;;AAEnB;AACA;AACA;AACA;AACA;AACA,MAAMX,IAAI,GAAGC,MAAM;;AAEnB;AACA;AACA;AACA;AACA;AACA,MAAMT,KAAK,GAAGC,OAAO;;AAErB;AACA;AACA;AACA;AACA;AACA,MAAMS,MAAM,GAAGC,QAAQ;;AAEvB;AACA;AACA;AACA,MAAMC,UAAU,GAAGC,YAAY;;AAE/B;AACA;AACA;AACA;AACA,MAAMO,eAAe,GAAGA,CAAC0J,SAAS,EAAErE,QAAQ,EAAE6F,OAAO,KAAK;EACxD;EACA;EACA;EACA,MAAMlE,KAAK,GAAG,IAAIwG,GAAG,CAAC,CAAC;EACvB,MAAMC,aAAa,GAAG;IACpB7D,QAAQ;IACR,GAAGsB;EACL,CAAC;EACD,MAAMwC,iBAAiB,GAAG;IACxB,GAAGD,aAAa,CAAC7D,QAAQ;IACzBzB,EAAE,EAAEnB;EACN,CAAC;EACD,OAAO/G,iBAAiB,CAACyJ,SAAS,EAAErE,QAAQ,EAAE;IAC5C,GAAGoI,aAAa;IAChB7D,QAAQ,EAAE8D;EACZ,CAAC,CAAC;AACJ,CAAC;AAED,SAAS9O,KAAK,EAAEE,aAAa,EAAE8M,UAAU,EAAE5L,eAAe,EAAEhB,cAAc,EAAEE,IAAI,EAAEE,IAAI,EAAEE,MAAM,EAAEE,UAAU,EAAEE,MAAM,EAAEkK,QAAQ,EAAEhK,KAAK,EAAEE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}