import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import Table from "../../components/Table";
import Pagination from "../../components/Pagination";
import Search from "../../components/Search";
import EmployeeService from "./EmployeeService";

const DeletedEmployees = () => {
    const [deletedEmployees, setDeletedEmployees] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [totalElements, setTotalElements] = useState(0);
    const [searchFilter, setSearchFilter] = useState({ name: "" });
    const pageSize = 10;

    const columns = [
        { key: "id", label: "ID" },
        { key: "firstName", label: "Tên" },
        { key: "lastName", label: "H<PERSON>" },
        { key: "username", label: "Tên đăng nhập" },
        { key: "email", label: "Email" },
        { key: "phone", label: "Số điện thoại" },
        { key: "cccd", label: "CCCD" },
        { key: "deletedAt", label: "Ngày xóa" }
    ];

    const fetchDeletedEmployees = async (page = 0, filter = {}) => {
        try {
            setLoading(true);
            const params = { 
                name: filter.name || null,
                deleted: true // Filter for deleted employees
            };
            const response = await EmployeeService.getAllEmployees(page, pageSize, params);
            
            setDeletedEmployees(response.data.content || []);
            setTotalPages(response.data.totalPages || 0);
            setTotalElements(response.data.totalElements || 0);
            setCurrentPage(page);
        } catch (error) {
            toast.error("Không thể tải danh sách nhân viên đã xóa!");
            setDeletedEmployees([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchDeletedEmployees(0, searchFilter);
    }, []);

    const handleSearch = (filter) => {
        setSearchFilter(filter);
        setCurrentPage(0);
        fetchDeletedEmployees(0, filter);
    };

    const handlePageChange = (page) => {
        fetchDeletedEmployees(page, searchFilter);
    };

    const handleRestore = async (employeeId) => {
        if (window.confirm("Bạn có chắc chắn muốn khôi phục nhân viên này?")) {
            try {
                await EmployeeService.restoreEmployee(employeeId);
                toast.success("Khôi phục nhân viên thành công!");
                fetchDeletedEmployees(currentPage, searchFilter);
            } catch (error) {
                toast.error("Khôi phục thất bại!");
            }
        }
    };

    const handlePermanentDelete = async (employeeId) => {
        if (window.confirm("Bạn có chắc chắn muốn xóa vĩnh viễn nhân viên này? Hành động này không thể hoàn tác!")) {
            try {
                await EmployeeService.permanentDeleteEmployee(employeeId);
                toast.success("Xóa vĩnh viễn thành công!");
                fetchDeletedEmployees(currentPage, searchFilter);
            } catch (error) {
                toast.error("Xóa vĩnh viễn thất bại!");
            }
        }
    };

    const actions = [
        {
            label: "Khôi phục",
            onClick: handleRestore,
            className: "bg-green-500 hover:bg-green-600 text-white"
        },
        {
            label: "Xóa vĩnh viễn",
            onClick: handlePermanentDelete,
            className: "bg-red-500 hover:bg-red-600 text-white"
        }
    ];

    return (
        <div className="p-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold mb-4">Danh sách nhân viên đã xóa</h1>
                
                <Search 
                    onSearch={handleSearch}
                    placeholder="Tìm kiếm theo tên..."
                    fields={[
                        { key: "name", label: "Tên", type: "text" }
                    ]}
                />
            </div>

            {loading ? (
                <div className="flex justify-center items-center py-8">
                    <div className="text-lg">Đang tải...</div>
                </div>
            ) : (
                <>
                    <Table 
                        data={deletedEmployees}
                        columns={columns}
                        actions={actions}
                        emptyMessage="Không có nhân viên nào đã bị xóa"
                    />
                    
                    {totalPages > 1 && (
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            totalElements={totalElements}
                            onPageChange={handlePageChange}
                        />
                    )}
                </>
            )}
        </div>
    );
};

export default DeletedEmployees;
