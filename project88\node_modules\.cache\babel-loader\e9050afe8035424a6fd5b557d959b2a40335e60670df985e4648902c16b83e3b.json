{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport AuthAPI from \"../api/AuthAPI\";\nimport { getUserRole } from \"../utils/auth\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  toggleTheme,\n  currentTheme\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [menuOpen, setMenuOpen] = useState(false);\n  const role = getUserRole();\n\n  // Hàm xử lý logout\n  const handleLogout = async () => {\n    try {\n      await AuthAPI.logout();\n    } catch (error) {\n      // <PERSON><PERSON> thể log lỗi nếu cần\n    }\n    localStorage.removeItem('token');\n    // localStorage.removeItem('userId');\n    // localStorage.removeItem('role');\n    setMenuOpen(false);\n    navigate('/login');\n  };\n  const handleNavigate = path => {\n    navigate(path);\n    setMenuOpen(false); // đóng sidebar sau khi chọn\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"flex justify-between items-center p-4 bg-white shadow fixed top-0 left-0 right-0 z-40\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl font-bold text-red-600 cursor-pointer\",\n        onClick: () => navigate(\"/\"),\n        children: \"LOGO\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleTheme,\n          className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n          children: currentTheme === \"light\" ? \"Light/Dark\" : \"Dark/Light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setMenuOpen(true),\n          className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-6 h-6\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this), menuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 z-30\",\n      onClick: () => setMenuOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 z-50 ${menuOpen ? \"translate-x-0\" : \"translate-x-full\"}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: \"Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setMenuOpen(false),\n          className: \"text-gray-600 hover:text-black\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex flex-col p-4 space-y-2\",\n        children: [role === 'ROLE_ADMIN' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-left px-4 py-2 hover:bg-gray-100 rounded\",\n          onClick: () => handleNavigate(\"/admin\"),\n          children: \"Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 29\n        }, this), (role === 'ROLE_EMPLOYEE' || role === 'ROLE_ADMIN') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-left px-4 py-2 hover:bg-gray-100 rounded\",\n          onClick: () => handleNavigate(\"/employees\"),\n          children: \"Employee\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 29\n        }, this), (role === 'ROLE_USER' || role === 'ROLE_EMPLOYEE' || role === 'ROLE_ADMIN') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-left px-4 py-2 hover:bg-gray-100 rounded\",\n          onClick: () => handleNavigate(\"/user\"),\n          children: \"User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-left px-4 py-2 hover:bg-gray-100 rounded\",\n          onClick: handleLogout,\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(Header, \"LOkzV6QWmuhZGiaOKOGTReFPfb0=\", false, function () {\n  return [useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "AuthAPI", "getUserRole", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "toggleTheme", "currentTheme", "_s", "navigate", "menuOpen", "setMenuOpen", "role", "handleLogout", "logout", "error", "localStorage", "removeItem", "handleNavigate", "path", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/components/Header.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AuthAPI from \"../api/AuthAPI\";\r\nimport { getUserRole } from \"../utils/auth\";\r\n\r\nconst Header = ({ toggleTheme, currentTheme }) => {\r\n    const navigate = useNavigate();\r\n    const [menuOpen, setMenuOpen] = useState(false);\r\n    const role = getUserRole();\r\n\r\n    // Hàm xử lý logout\r\n    const handleLogout = async () => {\r\n        try {\r\n            await AuthAPI.logout();\r\n        } catch (error) {\r\n            // Có thể log lỗi nếu cần\r\n        }\r\n        localStorage.removeItem('token');\r\n        // localStorage.removeItem('userId');\r\n        // localStorage.removeItem('role');\r\n        setMenuOpen(false);\r\n        navigate('/login');\r\n    };\r\n\r\n    const handleNavigate = (path) => {\r\n        navigate(path);\r\n        setMenuOpen(false); // đóng sidebar sau khi chọn\r\n    };\r\n\r\n    return (\r\n        <>\r\n            {/* <PERSON><PERSON> chính */}\r\n            <header className=\"flex justify-between items-center p-4 bg-white shadow fixed top-0 left-0 right-0 z-40\">\r\n                <div\r\n                    className=\"text-2xl font-bold text-red-600 cursor-pointer\"\r\n                    onClick={() => navigate(\"/\")}\r\n                >\r\n                    LOGO\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                    <button\r\n                        onClick={toggleTheme}\r\n                        className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\"\r\n                    >\r\n                        {currentTheme === \"light\" ? \"Light/Dark\" : \"Dark/Light\"}\r\n                    </button>\r\n                    <button\r\n                        onClick={() => setMenuOpen(true)}\r\n                        className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\"\r\n                    >\r\n                        <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            strokeWidth=\"1.5\"\r\n                            stroke=\"currentColor\"\r\n                            className=\"w-6 h-6\"\r\n                        >\r\n                            <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                d=\"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5\"\r\n                            />\r\n                        </svg>\r\n                    </button>\r\n                </div>\r\n            </header>\r\n\r\n            {/* Overlay mờ nền */}\r\n            {menuOpen && (\r\n                <div\r\n                    className=\"fixed inset-0 bg-black bg-opacity-50 z-30\"\r\n                    onClick={() => setMenuOpen(false)}\r\n                ></div>\r\n            )}\r\n\r\n            {/* Sidebar trượt ngang */}\r\n            <div\r\n                className={`fixed top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 z-50 ${menuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n                    }`}\r\n            >\r\n                <div className=\"p-4 border-b flex justify-between items-center\">\r\n                    <h2 className=\"text-lg font-semibold\">Menu</h2>\r\n                    <button onClick={() => setMenuOpen(false)} className=\"text-gray-600 hover:text-black\">\r\n                        ✕\r\n                    </button>\r\n                </div>\r\n\r\n                \r\n                    <nav className=\"flex flex-col p-4 space-y-2\">\r\n                        {role === 'ROLE_ADMIN' && (\r\n                            <button\r\n                                className=\"text-left px-4 py-2 hover:bg-gray-100 rounded\"\r\n                                onClick={() => handleNavigate(\"/admin\")}\r\n                            >\r\n                                Admin\r\n                            </button>\r\n                        )}\r\n\r\n                        {(role === 'ROLE_EMPLOYEE' || role === 'ROLE_ADMIN') && (\r\n                            <button\r\n                                className=\"text-left px-4 py-2 hover:bg-gray-100 rounded\"\r\n                                onClick={() => handleNavigate(\"/employees\")}\r\n                            >\r\n                                Employee\r\n                            </button>\r\n                        )}\r\n\r\n                        {(role === 'ROLE_USER' || role === 'ROLE_EMPLOYEE' || role === 'ROLE_ADMIN') && (\r\n                            <button\r\n                                className=\"text-left px-4 py-2 hover:bg-gray-100 rounded\"\r\n                                onClick={() => handleNavigate(\"/user\")}\r\n                            >\r\n                                User\r\n                            </button>\r\n                        )}\r\n\r\n                        <button\r\n                            className=\"text-left px-4 py-2 hover:bg-gray-100 rounded\"\r\n                            onClick={handleLogout}\r\n                        >\r\n                            Logout\r\n                        </button>\r\n                    </nav>\r\n\r\n\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Header;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,MAAM,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMe,IAAI,GAAGZ,WAAW,CAAC,CAAC;;EAE1B;EACA,MAAMa,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMd,OAAO,CAACe,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZ;IAAA;IAEJC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC;IACA;IACAN,WAAW,CAAC,KAAK,CAAC;IAClBF,QAAQ,CAAC,QAAQ,CAAC;EACtB,CAAC;EAED,MAAMS,cAAc,GAAIC,IAAI,IAAK;IAC7BV,QAAQ,CAACU,IAAI,CAAC;IACdR,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;EACxB,CAAC;EAED,oBACIT,OAAA,CAAAE,SAAA;IAAAgB,QAAA,gBAEIlB,OAAA;MAAQmB,SAAS,EAAC,uFAAuF;MAAAD,QAAA,gBACrGlB,OAAA;QACImB,SAAS,EAAC,gDAAgD;QAC1DC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,GAAG,CAAE;QAAAW,QAAA,EAChC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNxB,OAAA;QAAKmB,SAAS,EAAC,6BAA6B;QAAAD,QAAA,gBACxClB,OAAA;UACIoB,OAAO,EAAEhB,WAAY;UACrBe,SAAS,EAAC,+DAA+D;UAAAD,QAAA,EAExEb,YAAY,KAAK,OAAO,GAAG,YAAY,GAAG;QAAY;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACTxB,OAAA;UACIoB,OAAO,EAAEA,CAAA,KAAMX,WAAW,CAAC,IAAI,CAAE;UACjCU,SAAS,EAAC,+DAA+D;UAAAD,QAAA,eAEzElB,OAAA;YACIyB,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,WAAW,EAAC,KAAK;YACjBC,MAAM,EAAC,cAAc;YACrBV,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBlB,OAAA;cACI8B,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA8C;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGRhB,QAAQ,iBACLR,OAAA;MACImB,SAAS,EAAC,2CAA2C;MACrDC,OAAO,EAAEA,CAAA,KAAMX,WAAW,CAAC,KAAK;IAAE;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACT,eAGDxB,OAAA;MACImB,SAAS,EAAE,uGAAuGX,QAAQ,GAAG,eAAe,GAAG,kBAAkB,EAC1J;MAAAU,QAAA,gBAEPlB,OAAA;QAAKmB,SAAS,EAAC,gDAAgD;QAAAD,QAAA,gBAC3DlB,OAAA;UAAImB,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CxB,OAAA;UAAQoB,OAAO,EAAEA,CAAA,KAAMX,WAAW,CAAC,KAAK,CAAE;UAACU,SAAS,EAAC,gCAAgC;UAAAD,QAAA,EAAC;QAEtF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGFxB,OAAA;QAAKmB,SAAS,EAAC,6BAA6B;QAAAD,QAAA,GACvCR,IAAI,KAAK,YAAY,iBAClBV,OAAA;UACImB,SAAS,EAAC,+CAA+C;UACzDC,OAAO,EAAEA,CAAA,KAAMJ,cAAc,CAAC,QAAQ,CAAE;UAAAE,QAAA,EAC3C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX,EAEA,CAACd,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,YAAY,kBAC/CV,OAAA;UACImB,SAAS,EAAC,+CAA+C;UACzDC,OAAO,EAAEA,CAAA,KAAMJ,cAAc,CAAC,YAAY,CAAE;UAAAE,QAAA,EAC/C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX,EAEA,CAACd,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,YAAY,kBACvEV,OAAA;UACImB,SAAS,EAAC,+CAA+C;UACzDC,OAAO,EAAEA,CAAA,KAAMJ,cAAc,CAAC,OAAO,CAAE;UAAAE,QAAA,EAC1C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX,eAEDxB,OAAA;UACImB,SAAS,EAAC,+CAA+C;UACzDC,OAAO,EAAET,YAAa;UAAAO,QAAA,EACzB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGT,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAClB,EAAA,CA5HIH,MAAM;EAAA,QACSP,WAAW;AAAA;AAAAqC,EAAA,GAD1B9B,MAAM;AA8HZ,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}