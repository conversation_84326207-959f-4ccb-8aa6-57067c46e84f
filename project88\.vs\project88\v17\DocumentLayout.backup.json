{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\setupTests.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\setupTests.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\reportWebVitals.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\reportWebVitals.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\index.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\index.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.test.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\App.test.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\App.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\Test.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\features\\Test.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\RegisterService.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\features\\register\\RegisterService.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\Register.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\features\\register\\Register.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\UserApi.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\api\\UserApi.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\TransactionAPI.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\api\\TransactionAPI.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\Api.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\api\\Api.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "setupTests.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\setupTests.js", "RelativeDocumentMoniker": "src\\setupTests.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\setupTests.js", "RelativeToolTip": "src\\setupTests.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:39.81Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "reportWebVitals.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\reportWebVitals.js", "RelativeDocumentMoniker": "src\\reportWebVitals.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\reportWebVitals.js", "RelativeToolTip": "src\\reportWebVitals.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:37.73Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "index.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\index.js", "RelativeDocumentMoniker": "src\\index.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\index.js", "RelativeToolTip": "src\\index.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:35.496Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.test.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.test.js", "RelativeDocumentMoniker": "src\\App.test.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.test.js", "RelativeToolTip": "src\\App.test.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:33.072Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "App.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.js", "RelativeDocumentMoniker": "src\\App.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.js", "RelativeToolTip": "src\\App.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:27.522Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "RegisterService.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\RegisterService.js", "RelativeDocumentMoniker": "src\\features\\register\\RegisterService.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\RegisterService.js", "RelativeToolTip": "src\\features\\register\\RegisterService.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:23.145Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "UserApi.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\UserApi.js", "RelativeDocumentMoniker": "src\\api\\UserApi.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\UserApi.js", "RelativeToolTip": "src\\api\\UserApi.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:14.301Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "TransactionAPI.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\TransactionAPI.js", "RelativeDocumentMoniker": "src\\api\\TransactionAPI.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\TransactionAPI.js", "RelativeToolTip": "src\\api\\TransactionAPI.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:12.651Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "Api.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\Api.js", "RelativeDocumentMoniker": "src\\api\\Api.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\api\\Api.js", "RelativeToolTip": "src\\api\\Api.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:03:10.682Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Test.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\Test.js", "RelativeDocumentMoniker": "src\\features\\Test.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\Test.js", "RelativeToolTip": "src\\features\\Test.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-22T15:04:32.156Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Register.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\Register.js", "RelativeDocumentMoniker": "src\\features\\register\\Register.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\Register.js", "RelativeToolTip": "src\\features\\register\\Register.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-22T15:04:27.722Z", "EditorCaption": ""}]}]}]}