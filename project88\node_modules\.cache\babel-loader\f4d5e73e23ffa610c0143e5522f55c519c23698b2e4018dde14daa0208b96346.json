{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfYear} function options.\n */\n\n/**\n * @name endOfYear\n * @category Year Helpers\n * @summary Return the end of a year for the given date.\n *\n * @description\n * Return the end of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The end of a year\n *\n * @example\n * // The end of a year for 2 September 2014 11:55:00:\n * const result = endOfYear(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Wed Dec 31 2014 23:59:59.999\n */\nexport function endOfYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  _date.setFullYear(year + 1, 0, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfYear;", "map": {"version": 3, "names": ["toDate", "endOfYear", "date", "options", "_date", "in", "year", "getFullYear", "setFullYear", "setHours"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/endOfYear.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfYear} function options.\n */\n\n/**\n * @name endOfYear\n * @category Year Helpers\n * @summary Return the end of a year for the given date.\n *\n * @description\n * Return the end of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The end of a year\n *\n * @example\n * // The end of a year for 2 September 2014 11:55:00:\n * const result = endOfYear(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Wed Dec 31 2014 23:59:59.999\n */\nexport function endOfYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  _date.setFullYear(year + 1, 0, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfYear;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChCH,KAAK,CAACI,WAAW,CAACF,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjCF,KAAK,CAACK,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC/B,OAAOL,KAAK;AACd;;AAEA;AACA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}