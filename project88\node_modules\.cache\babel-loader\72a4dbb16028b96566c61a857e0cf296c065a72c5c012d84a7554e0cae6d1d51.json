{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\home\\\\Transfer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"../../styles/TransferForm.scss\";\nimport TransferForm from \"../../components/TransferForm\";\nimport UserAPIv2 from \"../../api/UserAPIv2\";\nimport TransferUserBalance from \"../../components/TranferUserBalance\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Transfer = ({\n  setShowTransfer,\n  onAfterTransfer\n}) => {\n  _s();\n  // const userID = getUserId();\n  const userID = localStorage.getItem(\"userId\");\n  const [transferDTO, settransferDTO] = useState({\n    senderID: userID,\n    cardNumber: null,\n    money: null,\n    content: \"\"\n  });\n  const [userBalance, setUserBalance] = useState({\n    firstName: \"\",\n    lastName: \"\",\n    fullName: \"\",\n    balance: 0\n  });\n  const [receiverName, setReceiverName] = useState(\"\");\n  const fetchUserBalance = async () => {\n    try {\n      const response = await UserAPIv2.FindUserById(userID);\n      if (response && response.data) {\n        setUserBalance({\n          firstName: response.data.firstName,\n          lastName: response.data.lastName,\n          fullName: `${response.data.lastName} ${response.data.firstName}`,\n          balance: response.data.balance\n        });\n      } else {\n        console.error(\"No user data found.\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching user balance:\", error);\n    }\n  };\n  const fetchTransferData = async () => {\n    try {\n      const response = await UserAPIv2.Transfer(transferDTO);\n      alert(\"Chuyển khoản thành công!\");\n      if (onAfterTransfer) onAfterTransfer();\n    } catch (error) {\n      alert(\"Chuyển khoản thất bại! Vui lòng kiểm tra lại thông tin.\");\n      console.error(\"Transfer failed:\", error);\n    }\n  };\n  const fetchUserByCardNumber = async cardNumber => {\n    try {\n      const response = await UserAPIv2.FindUserByCardNumber(cardNumber);\n      if (response && response.data) {\n        setReceiverName(response.data);\n      } else {\n        setReceiverName(\"Không tìm thấy người dùng với số tài khoản này.\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching user by card number:\", error);\n      setReceiverName(\"STK không tồn tại!\");\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    settransferDTO(preTransferDTO => ({\n      ...preTransferDTO,\n      [name]: value\n    }));\n  };\n  const handleTransfer = () => {\n    // Kiểm tra dữ liệu đầu vào\n    if (!transferDTO.cardNumber || !transferDTO.money || !transferDTO.content) {\n      alert(\"Vui lòng nhập đầy đủ Số tài khoản, Số tiền và Nội dung chuyển khoản.\");\n      return;\n    }\n\n    // Gọi API để thực hiện chuyển khoản\n    fetchTransferData();\n    // Reset form or close modal after transfer\n    setShowTransfer(false);\n  };\n  useEffect(() => {\n    if (transferDTO.cardNumber) {\n      const timer = setTimeout(() => {\n        fetchUserByCardNumber(transferDTO.cardNumber);\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      setReceiverName(\"\");\n    }\n  }, [transferDTO.cardNumber]);\n  useEffect(() => {\n    fetchUserBalance();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transfer-form\",\n    children: [/*#__PURE__*/_jsxDEV(TransferUserBalance, {\n      userBalance: userBalance\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TransferForm, {\n      transferDTO: transferDTO,\n      handleInputChange: handleInputChange,\n      setShowTransfer: setShowTransfer,\n      handleTransfer: handleTransfer,\n      receiverName: receiverName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(Transfer, \"VPIXhiMRsKQk/y0cg96ISR9Xd4U=\");\n_c = Transfer;\nexport default Transfer;\nvar _c;\n$RefreshReg$(_c, \"Transfer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "TransferForm", "UserAPIv2", "TransferUserBalance", "jsxDEV", "_jsxDEV", "Transfer", "setShowTransfer", "onAfterTransfer", "_s", "userID", "localStorage", "getItem", "transferDTO", "settransferDTO", "senderID", "cardNumber", "money", "content", "userBalance", "setUserBalance", "firstName", "lastName", "fullName", "balance", "<PERSON><PERSON><PERSON>", "setReceiverName", "fetchUserBalance", "response", "FindUserById", "data", "console", "error", "fetchTransferData", "alert", "fetchUserByCardNumber", "FindUserByCardNumber", "handleInputChange", "e", "name", "value", "target", "preTransferDTO", "handleTransfer", "timer", "setTimeout", "clearTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"../../styles/TransferForm.scss\";\r\nimport TransferForm from \"../../components/TransferForm\";\r\nimport UserAPIv2 from \"../../api/UserAPIv2\";\r\nimport TransferUserBalance from \"../../components/TranferUserBalance\";\r\n\r\nconst Transfer = ({ setShowTransfer, onAfterTransfer }) => {\r\n\r\n  // const userID = getUserId();\r\n  const userID = localStorage.getItem(\"userId\");\r\n\r\n  const [transferDTO, settransferDTO] = useState({\r\n    senderID: userID,\r\n    cardNumber: null,\r\n    money: null,\r\n    content: \"\"\r\n  }\r\n  );\r\n\r\n  const [userBalance, setUserBalance] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    fullName: \"\",\r\n    balance: 0\r\n  });\r\n\r\n  const [receiverName, setReceiverName] = useState(\"\");\r\n\r\n  const fetchUserBalance = async () => {\r\n    try {\r\n      const response = await UserAPIv2.FindUserById(userID);\r\n      if (response && response.data) {\r\n        setUserBalance({\r\n          firstName: response.data.firstName,\r\n          lastName: response.data.lastName,\r\n          fullName: `${response.data.lastName} ${response.data.firstName}`,\r\n          balance: response.data.balance\r\n        });\r\n      } else {\r\n        console.error(\"No user data found.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching user balance:\", error);\r\n    }\r\n  }\r\n  const fetchTransferData = async () => {\r\n    try {\r\n      const response = await UserAPIv2.Transfer(transferDTO);\r\n      alert(\"Chuyển khoản thành công!\");\r\n      if (onAfterTransfer) onAfterTransfer();\r\n    } catch (error) {\r\n      alert(\"Chuyển khoản thất bại! Vui lòng kiểm tra lại thông tin.\");\r\n      console.error(\"Transfer failed:\", error);\r\n    }\r\n  }\r\n\r\n  const fetchUserByCardNumber = async (cardNumber) => {\r\n    try {\r\n      const response = await UserAPIv2.FindUserByCardNumber(cardNumber);\r\n      if (response && response.data) {\r\n        setReceiverName(response.data);\r\n      } else {\r\n        setReceiverName(\"Không tìm thấy người dùng với số tài khoản này.\");\r\n      }\r\n    }\r\n    catch (error) {\r\n      console.error(\"Error fetching user by card number:\", error);\r\n      setReceiverName(\"STK không tồn tại!\");\r\n    }\r\n  }\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    settransferDTO(preTransferDTO => ({\r\n      ...preTransferDTO,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleTransfer = () => {\r\n    // Kiểm tra dữ liệu đầu vào\r\n    if (\r\n      !transferDTO.cardNumber ||\r\n      !transferDTO.money ||\r\n      !transferDTO.content\r\n    ) {\r\n      alert(\"Vui lòng nhập đầy đủ Số tài khoản, Số tiền và Nội dung chuyển khoản.\");\r\n      return;\r\n    }\r\n\r\n    // Gọi API để thực hiện chuyển khoản\r\n    fetchTransferData();\r\n    // Reset form or close modal after transfer\r\n    setShowTransfer(false);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (transferDTO.cardNumber) {\r\n      const timer = setTimeout(() => {\r\n        fetchUserByCardNumber(transferDTO.cardNumber);\r\n      }, 1000);\r\n      return () => clearTimeout(timer);\r\n    } else {\r\n      setReceiverName(\"\");\r\n    }\r\n  }, [transferDTO.cardNumber]);\r\n\r\n  useEffect(() => {\r\n    fetchUserBalance();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"transfer-form\">\r\n\r\n      <TransferUserBalance userBalance={userBalance} />\r\n\r\n      <TransferForm\r\n        transferDTO={transferDTO}\r\n        handleInputChange={handleInputChange}\r\n        setShowTransfer={setShowTransfer}\r\n        handleTransfer={handleTransfer}\r\n        receiverName={receiverName}\r\n      />\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Transfer;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,gCAAgC;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,mBAAmB,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAEzD;EACA,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAE7C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC;IAC7CgB,QAAQ,EAAEL,MAAM;IAChBM,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE;EACX,CACA,CAAC;EAED,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC;IAC7CsB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM4B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM1B,SAAS,CAAC2B,YAAY,CAACnB,MAAM,CAAC;MACrD,IAAIkB,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;QAC7BV,cAAc,CAAC;UACbC,SAAS,EAAEO,QAAQ,CAACE,IAAI,CAACT,SAAS;UAClCC,QAAQ,EAAEM,QAAQ,CAACE,IAAI,CAACR,QAAQ;UAChCC,QAAQ,EAAE,GAAGK,QAAQ,CAACE,IAAI,CAACR,QAAQ,IAAIM,QAAQ,CAACE,IAAI,CAACT,SAAS,EAAE;UAChEG,OAAO,EAAEI,QAAQ,CAACE,IAAI,CAACN;QACzB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLO,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EACD,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAM1B,SAAS,CAACI,QAAQ,CAACO,WAAW,CAAC;MACtDqB,KAAK,CAAC,0BAA0B,CAAC;MACjC,IAAI1B,eAAe,EAAEA,eAAe,CAAC,CAAC;IACxC,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdE,KAAK,CAAC,yDAAyD,CAAC;MAChEH,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C;EACF,CAAC;EAED,MAAMG,qBAAqB,GAAG,MAAOnB,UAAU,IAAK;IAClD,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM1B,SAAS,CAACkC,oBAAoB,CAACpB,UAAU,CAAC;MACjE,IAAIY,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;QAC7BJ,eAAe,CAACE,QAAQ,CAACE,IAAI,CAAC;MAChC,CAAC,MAAM;QACLJ,eAAe,CAAC,iDAAiD,CAAC;MACpE;IACF,CAAC,CACD,OAAOM,KAAK,EAAE;MACZD,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DN,eAAe,CAAC,oBAAoB,CAAC;IACvC;EACF,CAAC;EAED,MAAMW,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,cAAc,CAAC4B,cAAc,KAAK;MAChC,GAAGA,cAAc;MACjB,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,IACE,CAAC9B,WAAW,CAACG,UAAU,IACvB,CAACH,WAAW,CAACI,KAAK,IAClB,CAACJ,WAAW,CAACK,OAAO,EACpB;MACAgB,KAAK,CAAC,sEAAsE,CAAC;MAC7E;IACF;;IAEA;IACAD,iBAAiB,CAAC,CAAC;IACnB;IACA1B,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAEDP,SAAS,CAAC,MAAM;IACd,IAAIa,WAAW,CAACG,UAAU,EAAE;MAC1B,MAAM4B,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BV,qBAAqB,CAACtB,WAAW,CAACG,UAAU,CAAC;MAC/C,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAM8B,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLlB,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC,EAAE,CAACb,WAAW,CAACG,UAAU,CAAC,CAAC;EAE5BhB,SAAS,CAAC,MAAM;IACd2B,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEtB,OAAA;IAAK0C,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5B3C,OAAA,CAACF,mBAAmB;MAACgB,WAAW,EAAEA;IAAY;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjD/C,OAAA,CAACJ,YAAY;MACXY,WAAW,EAAEA,WAAY;MACzBwB,iBAAiB,EAAEA,iBAAkB;MACrC9B,eAAe,EAAEA,eAAgB;MACjCoC,cAAc,EAAEA,cAAe;MAC/BlB,YAAY,EAAEA;IAAa;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEC,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAxHIH,QAAQ;AAAA+C,EAAA,GAAR/C,QAAQ;AA0Hd,eAAeA,QAAQ;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}