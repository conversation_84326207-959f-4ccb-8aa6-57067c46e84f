{"ast": null, "code": "import employeeAPI from \"../../api/EmployeeAPI\";\nimport { getEmployeeByUsername } from \"../../redux/slices/employeeSlice\";\nconst EmployeeService = {\n  getAllEmployees: async (page, size, filter) => {\n    return await employeeAPI.getAllEmployees(page, size, filter);\n  },\n  getEmployeeByUsername: async () => {\n    return await employeeAPI.getEmployeeByUsername();\n  },\n  createEmployee: async body => {\n    return await employeeAPI.createEmployee(body);\n  },\n  getEmployeeById: async id => {\n    return await employeeAPI.getEmployeeById(id);\n  },\n  updateEmployee: async (id, body) => {\n    return await employeeAPI.updateEmployee(id, body);\n  },\n  deleteEmployee: async id => {\n    return await employeeAPI.deleteEmployee(id);\n  },\n  restoreEmployee: async id => {\n    return await employeeAPI.restoreEmployee(id);\n  },\n  permanentDeleteEmployee: async id => {\n    return await employeeAPI.permanentDeleteEmployee(id);\n  }\n};\nexport default EmployeeService;", "map": {"version": 3, "names": ["employeeAPI", "getEmployeeByUsername", "EmployeeService", "getAllEmployees", "page", "size", "filter", "createEmployee", "body", "getEmployeeById", "id", "updateEmployee", "deleteEmployee", "restoreEmployee", "permanentDeleteEmployee"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/Employee/EmployeeService.js"], "sourcesContent": ["import employeeAPI from \"../../api/EmployeeAPI\";\r\nimport { getEmployeeByUsername } from \"../../redux/slices/employeeSlice\";\r\n\r\nconst EmployeeService = {\r\n    getAllEmployees: async (page, size, filter) => {\r\n        return await employeeAPI.getAllEmployees(page, size, filter);\r\n    },\r\n    getEmployeeByUsername: async () => {\r\n        return await employeeAPI.getEmployeeByUsername();\r\n    },\r\n    createEmployee: async (body) => {\r\n        return await employeeAPI.createEmployee(body);\r\n    },\r\n    getEmployeeById: async (id) => {\r\n        return await employeeAPI.getEmployeeById(id);\r\n    },\r\n    updateEmployee: async (id, body) => {\r\n        return await employeeAPI.updateEmployee(id, body);\r\n    },\r\n    deleteEmployee: async (id) => {\r\n        return await employeeAPI.deleteEmployee(id);\r\n    },\r\n    restoreEmployee: async (id) => {\r\n        return await employeeAPI.restoreEmployee(id);\r\n    },\r\n    permanentDeleteEmployee: async (id) => {\r\n        return await employeeAPI.permanentDeleteEmployee(id);\r\n    }\r\n}\r\n\r\nexport default EmployeeService;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,uBAAuB;AAC/C,SAASC,qBAAqB,QAAQ,kCAAkC;AAExE,MAAMC,eAAe,GAAG;EACpBC,eAAe,EAAE,MAAAA,CAAOC,IAAI,EAAEC,IAAI,EAAEC,MAAM,KAAK;IAC3C,OAAO,MAAMN,WAAW,CAACG,eAAe,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,CAAC;EAChE,CAAC;EACDL,qBAAqB,EAAE,MAAAA,CAAA,KAAY;IAC/B,OAAO,MAAMD,WAAW,CAACC,qBAAqB,CAAC,CAAC;EACpD,CAAC;EACDM,cAAc,EAAE,MAAOC,IAAI,IAAK;IAC5B,OAAO,MAAMR,WAAW,CAACO,cAAc,CAACC,IAAI,CAAC;EACjD,CAAC;EACDC,eAAe,EAAE,MAAOC,EAAE,IAAK;IAC3B,OAAO,MAAMV,WAAW,CAACS,eAAe,CAACC,EAAE,CAAC;EAChD,CAAC;EACDC,cAAc,EAAE,MAAAA,CAAOD,EAAE,EAAEF,IAAI,KAAK;IAChC,OAAO,MAAMR,WAAW,CAACW,cAAc,CAACD,EAAE,EAAEF,IAAI,CAAC;EACrD,CAAC;EACDI,cAAc,EAAE,MAAOF,EAAE,IAAK;IAC1B,OAAO,MAAMV,WAAW,CAACY,cAAc,CAACF,EAAE,CAAC;EAC/C,CAAC;EACDG,eAAe,EAAE,MAAOH,EAAE,IAAK;IAC3B,OAAO,MAAMV,WAAW,CAACa,eAAe,CAACH,EAAE,CAAC;EAChD,CAAC;EACDI,uBAAuB,EAAE,MAAOJ,EAAE,IAAK;IACnC,OAAO,MAAMV,WAAW,CAACc,uBAAuB,CAACJ,EAAE,CAAC;EACxD;AACJ,CAAC;AAED,eAAeR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}