{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\nimport LoginPage from './features/auth/LoginPage';\nimport FogotPassword from './features/auth/FogotPassword';\nimport Register from './features/register/Register';\nimport HomePage from './features/home/<USER>';\nimport ChangePassword from './features/auth/ChangePassword';\nimport Profile from './features/user/Profile';\nimport TransferForm from './features/home/<USER>';\nimport Test from './features/Test';\nimport EmployeeList from \"./features/admin/EmployeeList\";\nimport Header from \"./components/Header\";\nimport Footer from \"./components/Footer\";\nimport VerifyPage from \"./features/auth/VerifyPage\";\nimport UserContent from './features/user/UserContent';\nimport EmployeeContent from './features/employee/EmployeeContent';\nimport AdminContent from './features/admin/AdminContent';\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [theme, setTheme] = useState(\"light\");\n  const toggleTheme = () => {\n    setTheme(prev => prev === \"light\" ? \"dark\" : \"light\");\n  };\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 3000\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {\n      toggleTheme: toggleTheme,\n      currentTheme: theme\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-20\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/fogot-password\",\n          element: /*#__PURE__*/_jsxDEV(FogotPassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/profile\",\n          element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/homepage\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/reset-password\",\n          element: /*#__PURE__*/_jsxDEV(ChangePassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/Transfer\",\n          element: /*#__PURE__*/_jsxDEV(TransferForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/test\",\n          element: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/list-employees\",\n          element: /*#__PURE__*/_jsxDEV(EmployeeList, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/verify\",\n          element: /*#__PURE__*/_jsxDEV(VerifyPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/user\",\n          element: /*#__PURE__*/_jsxDEV(UserContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/employees\",\n          element: /*#__PURE__*/_jsxDEV(EmployeeContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {\n      toggleTheme: toggleTheme,\n      currentTheme: theme\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"1NR3O0/bzzhjlLyt740h+JhxH9c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LoginPage", "FogotPassword", "Register", "HomePage", "ChangePassword", "Profile", "TransferForm", "Test", "EmployeeList", "Header", "Footer", "VerifyPage", "UserContent", "EmployeeContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ToastContainer", "jsxDEV", "_jsxDEV", "App", "_s", "theme", "setTheme", "toggleTheme", "prev", "children", "position", "autoClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentTheme", "className", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport './App.css';\r\nimport LoginPage from './features/auth/LoginPage';\r\nimport FogotPassword from './features/auth/FogotPassword';\r\nimport Register from './features/register/Register';\r\nimport HomePage from './features/home/<USER>';\r\nimport ChangePassword from './features/auth/ChangePassword';\r\nimport Profile from './features/user/Profile';\r\nimport TransferForm from './features/home/<USER>';\r\nimport Test from './features/Test';\r\nimport EmployeeList from \"./features/admin/EmployeeList\";\r\nimport Header from \"./components/Header\";\r\nimport Footer from \"./components/Footer\";\r\nimport VerifyPage from \"./features/auth/VerifyPage\";\r\nimport UserContent from './features/user/UserContent';\r\nimport EmployeeContent from './features/employee/EmployeeContent';\r\nimport AdminContent from './features/admin/AdminContent';\r\nimport { ToastContainer } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\n\r\n\r\nfunction App() {\r\n  const [theme, setTheme] = useState(\"light\");\r\n\r\n  const toggleTheme = () => {\r\n    setTheme(prev => (prev === \"light\" ? \"dark\" : \"light\"));\r\n  };\r\n\r\n  return (\r\n\r\n    <Router>\r\n      <ToastContainer position=\"top-right\" autoClose={3000} />\r\n      <Header toggleTheme={toggleTheme} currentTheme={theme} />\r\n      <div className='mt-20'>\r\n        <Routes>\r\n          <Route path=\"/\" element={<LoginPage />} />\r\n          <Route path=\"/login\" element={<LoginPage />} />\r\n          <Route path=\"/fogot-password\" element={<FogotPassword />} />\r\n          <Route path='/register' element={<Register />} />\r\n          <Route path='/profile' element={<Profile />} />\r\n          <Route path='/homepage' element={<HomePage />} />\r\n          <Route path='/reset-password' element={<ChangePassword />} />\r\n          <Route path='/Transfer' element={<TransferForm />} />\r\n          <Route path='/test' element={<Test />} />\r\n          <Route path='/list-employees' element={<EmployeeList />} />\r\n          <Route path=\"/verify\" element={<VerifyPage />} />\r\n          <Route path=\"/user\" element={<UserContent />} />\r\n          <Route path=\"/admin\" element={<AdminContent />} />\r\n          <Route path=\"/employees\" element={<EmployeeContent />} />\r\n        </Routes>\r\n      </div>\r\n\r\n      <Footer toggleTheme={toggleTheme} currentTheme={theme} />\r\n    </Router>\r\n\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAO,WAAW;AAClB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG/C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,OAAO,CAAC;EAE3C,MAAM2B,WAAW,GAAGA,CAAA,KAAM;IACxBD,QAAQ,CAACE,IAAI,IAAKA,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG,OAAQ,CAAC;EACzD,CAAC;EAED,oBAEEN,OAAA,CAACpB,MAAM;IAAA2B,QAAA,gBACLP,OAAA,CAACF,cAAc;MAACU,QAAQ,EAAC,WAAW;MAACC,SAAS,EAAE;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxDb,OAAA,CAACR,MAAM;MAACa,WAAW,EAAEA,WAAY;MAACS,YAAY,EAAEX;IAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzDb,OAAA;MAAKe,SAAS,EAAC,OAAO;MAAAR,QAAA,eACpBP,OAAA,CAACnB,MAAM;QAAA0B,QAAA,gBACLP,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEjB,OAAA,CAACjB,SAAS;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1Cb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEjB,OAAA,CAACjB,SAAS;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/Cb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,iBAAiB;UAACC,OAAO,eAAEjB,OAAA,CAAChB,aAAa;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5Db,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEjB,OAAA,CAACf,QAAQ;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEjB,OAAA,CAACZ,OAAO;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/Cb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEjB,OAAA,CAACd,QAAQ;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,iBAAiB;UAACC,OAAO,eAAEjB,OAAA,CAACb,cAAc;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7Db,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEjB,OAAA,CAACX,YAAY;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEjB,OAAA,CAACV,IAAI;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,iBAAiB;UAACC,OAAO,eAAEjB,OAAA,CAACT,YAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3Db,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,SAAS;UAACC,OAAO,eAAEjB,OAAA,CAACN,UAAU;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEjB,OAAA,CAACL,WAAW;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEjB,OAAA,CAACH,YAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDb,OAAA,CAAClB,KAAK;UAACkC,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEjB,OAAA,CAACJ,eAAe;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENb,OAAA,CAACP,MAAM;MAACY,WAAW,EAAEA,WAAY;MAACS,YAAY,EAAEX;IAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnD,CAAC;AAGb;AAACX,EAAA,CAnCQD,GAAG;AAAAiB,EAAA,GAAHjB,GAAG;AAqCZ,eAAeA,GAAG;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}