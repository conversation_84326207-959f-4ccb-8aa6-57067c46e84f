{"ast": null, "code": "import { getWeek } from \"./getWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setWeek} function options.\n */\n\n/**\n * @name setWeek\n * @category Week Helpers\n * @summary Set the local week to the given date.\n *\n * @description\n * Set the local week to the given date, saving the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param week - The week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the local week set\n *\n * @example\n * // Set the 1st week to 2 January 2005 with default options:\n * const result = setWeek(new Date(2005, 0, 2), 1)\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // Set the 1st week to 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January:\n * const result = setWeek(new Date(2005, 0, 2), 1, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sun Jan 4 2004 00:00:00\n */\nexport function setWeek(date, week, options) {\n  const date_ = toDate(date, options?.in);\n  const diff = getWeek(date_, options) - week;\n  date_.setDate(date_.getDate() - diff * 7);\n  return toDate(date_, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default setWeek;", "map": {"version": 3, "names": ["getWeek", "toDate", "setWeek", "date", "week", "options", "date_", "in", "diff", "setDate", "getDate"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/setWeek.js"], "sourcesContent": ["import { getWeek } from \"./getWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setWeek} function options.\n */\n\n/**\n * @name setWeek\n * @category Week Helpers\n * @summary Set the local week to the given date.\n *\n * @description\n * Set the local week to the given date, saving the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param week - The week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the local week set\n *\n * @example\n * // Set the 1st week to 2 January 2005 with default options:\n * const result = setWeek(new Date(2005, 0, 2), 1)\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // Set the 1st week to 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January:\n * const result = setWeek(new Date(2005, 0, 2), 1, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sun Jan 4 2004 00:00:00\n */\nexport function setWeek(date, week, options) {\n  const date_ = toDate(date, options?.in);\n  const diff = getWeek(date_, options) - week;\n  date_.setDate(date_.getDate() - diff * 7);\n  return toDate(date_, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default setWeek;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGR,OAAO,CAACM,KAAK,EAAED,OAAO,CAAC,GAAGD,IAAI;EAC3CE,KAAK,CAACG,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGF,IAAI,GAAG,CAAC,CAAC;EACzC,OAAOP,MAAM,CAACK,KAAK,EAAED,OAAO,EAAEE,EAAE,CAAC;AACnC;;AAEA;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}