{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\home\\\\Bills.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport PaymentModal from \"../../components/PaymentModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Bills = props => {\n  _s();\n  const [showModal, setShowModal] = useState(false);\n  const [selectedBill, setSelectedBill] = useState([]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full lg:w-1/5 space-y-4 overflow-y-auto h-[920px]\",\n    children: [props.data.map(bill => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 bg-white shadow rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-lg font-bold\",\n        children: bill.billName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500 mt-2\",\n        children: [\"M\\xE3 HD: \", bill.billId, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 25\n        }, this), \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n thanh to\\xE1n: \", Number(bill.billAmount).toLocaleString(\"de-DE\"), \" VND\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"mt-4 w-full px-4 py-2 bg-red-100 text-red-600 rounded hover:bg-red-200\",\n        onClick: () => {\n          setSelectedBill(bill);\n          setShowModal(true);\n        },\n        children: \"Thanh To\\xE1n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 21\n      }, this)]\n    }, bill.billId, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 17\n    }, this)), showModal && /*#__PURE__*/_jsxDEV(PaymentModal, {\n      balance: props.balance,\n      bill: selectedBill,\n      onClose: () => setShowModal(false),\n      fetchPayBill: props.fetchPayBill\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n_s(Bills, \"SGzvEbAb2RyFMfWqRNVtV7IQaY8=\");\n_c = Bills;\nexport default Bills;\nvar _c;\n$RefreshReg$(_c, \"Bills\");", "map": {"version": 3, "names": ["React", "useState", "PaymentModal", "jsxDEV", "_jsxDEV", "Bills", "props", "_s", "showModal", "setShowModal", "selected<PERSON><PERSON>", "setSelectedBill", "className", "children", "data", "map", "bill", "bill<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "billId", "Number", "billAmount", "toLocaleString", "onClick", "balance", "onClose", "fetchPayBill", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/home/<USER>"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport PaymentModal from \"../../components/PaymentModal\";\r\nconst Bills = (props) => {\r\n    const [showModal, setShowModal] = useState(false);\r\n    const [selectedBill, setSelectedBill] = useState([]);\r\n    return (\r\n        <div className=\"w-full lg:w-1/5 space-y-4 overflow-y-auto h-[920px]\">\r\n            {props.data.map((bill) => (\r\n                <div className=\"p-4 bg-white shadow rounded\" key={bill.billId}>\r\n                    <div className=\"text-lg font-bold\">{bill.billName}</div>\r\n                    <div className=\"text-gray-500 mt-2\">\r\n                        Mã HD: {bill.billId}\r\n                        <br />\r\n                        Số tiền cần thanh toán: {Number(bill.billAmount).toLocaleString(\"de-DE\")} VND\r\n                    </div>\r\n                    <button\r\n                        className=\"mt-4 w-full px-4 py-2 bg-red-100 text-red-600 rounded hover:bg-red-200\"\r\n                    onClick={() => {\r\n                        setSelectedBill(bill);\r\n                        setShowModal(true)}}\r\n                    >\r\n                        <PERSON><PERSON>\r\n                    </button>\r\n                </div>\r\n            ))}\r\n            {showModal && (\r\n                <PaymentModal\r\n                    balance={props.balance}\r\n                    bill={selectedBill}\r\n                    onClose={() => setShowModal(false)}\r\n                    fetchPayBill={props.fetchPayBill}\r\n                />\r\n            )}\r\n        </div>\r\n    );\r\n}\r\nexport default Bills;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACzD,MAAMC,KAAK,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpD,oBACIG,OAAA;IAAKQ,SAAS,EAAC,qDAAqD;IAAAC,QAAA,GAC/DP,KAAK,CAACQ,IAAI,CAACC,GAAG,CAAEC,IAAI,iBACjBZ,OAAA;MAAKQ,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBACxCT,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAEG,IAAI,CAACC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxDjB,OAAA;QAAKQ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAAC,YACzB,EAACG,IAAI,CAACM,MAAM,eACnBlB,OAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,8CACkB,EAACE,MAAM,CAACP,IAAI,CAACQ,UAAU,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAC,MAC7E;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjB,OAAA;QACIQ,SAAS,EAAC,wEAAwE;QACtFc,OAAO,EAAEA,CAAA,KAAM;UACXf,eAAe,CAACK,IAAI,CAAC;UACrBP,YAAY,CAAC,IAAI,CAAC;QAAA,CAAE;QAAAI,QAAA,EACvB;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA,GAdqCL,IAAI,CAACM,MAAM;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAexD,CACR,CAAC,EACDb,SAAS,iBACNJ,OAAA,CAACF,YAAY;MACTyB,OAAO,EAAErB,KAAK,CAACqB,OAAQ;MACvBX,IAAI,EAAEN,YAAa;MACnBkB,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAAC,KAAK,CAAE;MACnCoB,YAAY,EAAEvB,KAAK,CAACuB;IAAa;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAd,EAAA,CAjCKF,KAAK;AAAAyB,EAAA,GAALzB,KAAK;AAkCX,eAAeA,KAAK;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}