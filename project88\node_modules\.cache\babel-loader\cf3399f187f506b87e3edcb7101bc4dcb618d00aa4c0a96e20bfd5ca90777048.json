{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\register\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from 'react-redux';\nimport Form from \"../../components/Form\";\nimport { Validation } from \"../../validation/Validation\";\nimport { register } from \"../../redux/slices/registerSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const initialValues = {\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    birth: '',\n    cccd: '',\n    gender: '',\n    phone: '',\n    password: '',\n    confirmPassword: ''\n  };\n  const onSubmit = dataForm => {\n    dispatch(register(dataForm)).then(rs => {\n      if (rs.payload) {\n        alert(\"dk thanh cong\");\n        navigate('/login');\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-8 rounded-lg shadow-md w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-center mb-6\",\n        children: \"\\u0110\\u0103ng K\\xFD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: onSubmit,\n        initialValues: initialValues,\n        btn: \"\\u0110\\u0103ng K\\xFD\",\n        validation: Validation\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"pp\", {\n          className: \"text-sm text-gray-500 hover:underline\",\n          children: \"\\u0110\\xE3 c\\xF3 t\\xE0i kho\\u1EA3n?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/login'),\n          className: \"text-sm text-red-600 font-medium hover:underline\",\n          children: \"\\u0110\\u0103ng nh\\u1EADp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n};\n_s(Register, \"ZaVe+Vo7W9FMoQ/aTgBrV7UvA04=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useNavigate", "useDispatch", "Form", "Validation", "register", "jsxDEV", "_jsxDEV", "Register", "_s", "navigate", "dispatch", "initialValues", "username", "firstName", "lastName", "email", "birth", "cccd", "gender", "phone", "password", "confirmPassword", "onSubmit", "dataForm", "then", "rs", "payload", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "btn", "validation", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/register/Register.js"], "sourcesContent": ["import React from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useDispatch } from 'react-redux';\r\nimport Form from \"../../components/Form\";\r\nimport { Validation } from \"../../validation/Validation\";\r\nimport { register } from \"../../redux/slices/registerSlice\"\r\n\r\nconst Register = () => {\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n\r\n    const initialValues = { username: '', firstName: '', lastName: '', email: '', birth: '', cccd: '', gender: '', phone: '', password: '', confirmPassword: '' };\r\n\r\n\r\n    const onSubmit = (dataForm) => {\r\n        dispatch(register(dataForm)).then((rs) => {\r\n            if (rs.payload) {\r\n                alert(\"dk thanh cong\")\r\n                navigate('/login');\r\n            }\r\n        })\r\n\r\n    }\r\n\r\n\r\n    return (\r\n        <div className=\"flex items-center justify-center min-h-screen bg-gray-100\">\r\n            <div className=\"bg-white p-8 rounded-lg shadow-md w-full max-w-md\">\r\n                <h1 className=\"text-2xl font-bold text-center mb-6\"><PERSON><PERSON><PERSON></h1>\r\n                <Form onSubmit={onSubmit} initialValues={initialValues} btn=\"Đăng Ký\" validation={Validation}></Form>\r\n                <div className=\"mt-4 text-center\">\r\n                    <pp\r\n                        className=\"text-sm text-gray-500 hover:underline\"\r\n                    >\r\n                        Đã có tài khoản?\r\n                    </pp>\r\n                    <button\r\n                        onClick={() => navigate('/login')}\r\n                        className=\"text-sm text-red-600 font-medium hover:underline\"\r\n                    >\r\n                        Đăng nhập\r\n                    </button>\r\n\r\n                </div>\r\n            </div>\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default Register;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,IAAI,MAAM,uBAAuB;AACxC,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,QAAQ,QAAQ,kCAAkC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,aAAa,GAAG;IAAEC,QAAQ,EAAE,EAAE;IAAEC,SAAS,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,eAAe,EAAE;EAAG,CAAC;EAG7J,MAAMC,QAAQ,GAAIC,QAAQ,IAAK;IAC3Bb,QAAQ,CAACN,QAAQ,CAACmB,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAEC,EAAE,IAAK;MACtC,IAAIA,EAAE,CAACC,OAAO,EAAE;QACZC,KAAK,CAAC,eAAe,CAAC;QACtBlB,QAAQ,CAAC,QAAQ,CAAC;MACtB;IACJ,CAAC,CAAC;EAEN,CAAC;EAGD,oBACIH,OAAA;IAAKsB,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACtEvB,OAAA;MAAKsB,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAC9DvB,OAAA;QAAIsB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE3B,OAAA,CAACJ,IAAI;QAACoB,QAAQ,EAAEA,QAAS;QAACX,aAAa,EAAEA,aAAc;QAACuB,GAAG,EAAC,sBAAS;QAACC,UAAU,EAAEhC;MAAW;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrG3B,OAAA;QAAKsB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BvB,OAAA;UACIsB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACpD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UACI8B,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,QAAQ,CAAE;UAClCmB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,EAC/D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEL,CAAC;AAEd,CAAC;AAAAzB,EAAA,CAzCKD,QAAQ;EAAA,QACOP,WAAW,EACXC,WAAW;AAAA;AAAAoC,EAAA,GAF1B9B,QAAQ;AA2Cd,eAAeA,QAAQ;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}