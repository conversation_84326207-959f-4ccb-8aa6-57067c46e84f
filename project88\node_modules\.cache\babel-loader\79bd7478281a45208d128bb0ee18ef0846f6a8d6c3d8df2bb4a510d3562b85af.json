{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\components\\\\Form.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, createElement as _createElement } from \"react\";\nimport { Controller, useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport MyDatePicker from \"./MyDatePicker\";\n// import { Validation } from \"../validation/Validation\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Form = ({\n  initialValues,\n  onSubmit,\n  btn,\n  validation\n}) => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    control,\n    formState: {\n      errors\n    },\n    reset\n  } = useForm({\n    resolver: zodResolver(validation),\n    defaultValues: initialValues\n  });\n  const handleFormSubmit = async data => {\n    const rs = await onSubmit(data);\n    if (rs !== null && rs !== void 0 && rs.success) {\n      reset();\n    }\n  };\n  const today = new Date();\n  const pastDate = new Date(today);\n  pastDate.setFullYear(today.getFullYear() - 18);\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit(handleFormSubmit),\n    initialValues: initialValues,\n    children: [Object.keys(initialValues).map((field, index) => {\n      var _errors$field;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [field === \"gender\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              value: \"Male\",\n              ...register(field),\n              id: `${field}-male`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 33\n            }, this), \" Nam\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              value: \"Female\",\n              ...register(field),\n              id: `${field}-female`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 33\n            }, this), \" N\\u1EEF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              value: \"Other\",\n              ...register(field),\n              id: `${field}-Other`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 33\n            }, this), \" Kh\\xE1c\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 25\n        }, this) : field === \"remember\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            ...register(field),\n            id: field,\n            className: \"h-4 w-4 text-indigo-600 border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: field,\n            className: \"ml-2 block text-sm text-gray-900\",\n            children: \"Remember me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 29\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 25\n        }, this) : field === \"birth\" ? /*#__PURE__*/_jsxDEV(Controller, {\n          name: \"birth\",\n          control: control,\n          render: ({\n            field\n          }) => /*#__PURE__*/_jsxDEV(MyDatePicker, {\n            ...field,\n            typeDate: pastDate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_createElement(\"input\", {\n          className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\",\n          ...register(field),\n          key: index,\n          type: field.includes(\"password\") || field.includes(\"confirmPassword\") ? \"password\" : \"text\",\n          id: field,\n          name: field,\n          placeholder: field.includes(\"password\") || field.includes(\"confirmPass\") ? \"********\" : `Nhập ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 25\n          }\n        }), errors[field] && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500 text-sm mt-1\",\n          children: (_errors$field = errors[field]) === null || _errors$field === void 0 ? void 0 : _errors$field.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 25\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this);\n    }), btn && /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      className: \"w-full bg-red-100 text-red-600 py-2 px-4 rounded-md hover:bg-red-200 transition\",\n      children: btn\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n};\n_s(Form, \"FS/ASgNLb/336lNHqj7jMhdenzI=\", false, function () {\n  return [useForm];\n});\n_c = Form;\nexport default Form;\nvar _c;\n$RefreshReg$(_c, \"Form\");", "map": {"version": 3, "names": ["React", "useEffect", "createElement", "_createElement", "Controller", "useForm", "zodResolver", "MyDatePicker", "jsxDEV", "_jsxDEV", "Form", "initialValues", "onSubmit", "btn", "validation", "_s", "register", "handleSubmit", "control", "formState", "errors", "reset", "resolver", "defaultValues", "handleFormSubmit", "data", "rs", "success", "today", "Date", "pastDate", "setFullYear", "getFullYear", "children", "Object", "keys", "map", "field", "index", "_errors$field", "className", "type", "value", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "name", "render", "typeDate", "key", "includes", "placeholder", "replace", "toLowerCase", "__self", "__source", "message", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/components/Form.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport { Controller, useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\"\r\nimport MyDatePicker from \"./MyDatePicker\";\r\n// import { Validation } from \"../validation/Validation\";\r\n\r\nconst Form = ({ initialValues, onSubmit, btn, validation }) => {\r\n\r\n\r\n    const { register, handleSubmit, control, formState: { errors }, reset } = useForm({\r\n        resolver: zodResolver(validation),\r\n        defaultValues: initialValues\r\n    });\r\n\r\n    const handleFormSubmit = async (data) => {\r\n        const rs = await onSubmit(data);\r\n        if (rs?.success) {\r\n            reset();\r\n        }\r\n    }\r\n\r\n\r\n    const today = new Date();\r\n    const pastDate = new Date(today);\r\n    pastDate.setFullYear(today.getFullYear() - 18);\r\n\r\n    return (\r\n        <form onSubmit={handleSubmit(handleFormSubmit)} initialValues={initialValues}>\r\n            {Object.keys(initialValues).map((field, index) => (\r\n                <div className=\"mb-4\" key={index}>\r\n                    {field === \"gender\" ? (\r\n                        <div className=\"flex space-x-4\">\r\n                            <label>\r\n                                <input\r\n                                    type=\"radio\"\r\n                                    value=\"Male\"\r\n                                    {...register(field)}\r\n                                    id={`${field}-male`}\r\n                                /> Nam\r\n                            </label>\r\n                            <label>\r\n                                <input\r\n                                    type=\"radio\"\r\n                                    value=\"Female\"\r\n                                    {...register(field)}\r\n                                    id={`${field}-female`}\r\n                                /> Nữ\r\n                            </label>\r\n                            <label>\r\n                                <input\r\n                                    type=\"radio\"\r\n                                    value=\"Other\"\r\n                                    {...register(field)}\r\n                                    id={`${field}-Other`}\r\n                                /> Khác\r\n                            </label>\r\n                        </div>\r\n                    ) : field === \"remember\" ? (\r\n                        <div className=\"flex items-center\" key={index}>\r\n                            <input\r\n                                type=\"checkbox\"\r\n                                {...register(field)}\r\n                                id={field}\r\n                                className=\"h-4 w-4 text-indigo-600 border-gray-300 rounded\"\r\n                            />\r\n                            <label htmlFor={field} className=\"ml-2 block text-sm text-gray-900\">\r\n                                Remember me\r\n                            </label>\r\n                        </div>\r\n                    ) : field === \"birth\" ? (\r\n                        <Controller\r\n                            name=\"birth\"\r\n                            control={control}\r\n                            render={({ field }) => (\r\n                                <MyDatePicker {...field} typeDate={pastDate}></MyDatePicker>\r\n                            )}\r\n                        >\r\n                        </Controller>\r\n                    ) : (\r\n                        <input\r\n                            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                            {...register(field)}\r\n                            key={index}\r\n                            type={field.includes(\"password\") || field.includes(\"confirmPassword\") ? \"password\" : \"text\"}\r\n                            id={field}\r\n                            name={field}\r\n                            placeholder={field.includes(\"password\") || field.includes(\"confirmPass\") ? \"********\" : `Nhập ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`}\r\n                        />\r\n                    )}\r\n                    {errors[field] && (\r\n                        <p className=\"text-red-500 text-sm mt-1\">{errors[field]?.message}</p>\r\n                    )}\r\n                </div>\r\n\r\n            ))}\r\n            {btn && (\r\n                <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-red-100 text-red-600 py-2 px-4 rounded-md hover:bg-red-200 transition\"\r\n                >\r\n                    {btn}\r\n                </button>\r\n            )}\r\n\r\n        </form>\r\n    )\r\n}\r\n\r\nexport default Form;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAAC,aAAA,IAAAC,cAAA,QAAQ,OAAO;AACxC,SAASC,UAAU,EAAEC,OAAO,QAAQ,iBAAiB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,YAAY,MAAM,gBAAgB;AACzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,IAAI,GAAGA,CAAC;EAAEC,aAAa;EAAEC,QAAQ;EAAEC,GAAG;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAG3D,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,OAAO;IAAEC,SAAS,EAAE;MAAEC;IAAO,CAAC;IAAEC;EAAM,CAAC,GAAGhB,OAAO,CAAC;IAC9EiB,QAAQ,EAAEhB,WAAW,CAACQ,UAAU,CAAC;IACjCS,aAAa,EAAEZ;EACnB,CAAC,CAAC;EAEF,MAAMa,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACrC,MAAMC,EAAE,GAAG,MAAMd,QAAQ,CAACa,IAAI,CAAC;IAC/B,IAAIC,EAAE,aAAFA,EAAE,eAAFA,EAAE,CAAEC,OAAO,EAAE;MACbN,KAAK,CAAC,CAAC;IACX;EACJ,CAAC;EAGD,MAAMO,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;EACxB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;EAChCE,QAAQ,CAACC,WAAW,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;EAE9C,oBACIvB,OAAA;IAAMG,QAAQ,EAAEK,YAAY,CAACO,gBAAgB,CAAE;IAACb,aAAa,EAAEA,aAAc;IAAAsB,QAAA,GACxEC,MAAM,CAACC,IAAI,CAACxB,aAAa,CAAC,CAACyB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK;MAAA,IAAAC,aAAA;MAAA,oBACzC9B,OAAA;QAAK+B,SAAS,EAAC,MAAM;QAAAP,QAAA,GAChBI,KAAK,KAAK,QAAQ,gBACf5B,OAAA;UAAK+B,SAAS,EAAC,gBAAgB;UAAAP,QAAA,gBAC3BxB,OAAA;YAAAwB,QAAA,gBACIxB,OAAA;cACIgC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,MAAM;cAAA,GACR1B,QAAQ,CAACqB,KAAK,CAAC;cACnBM,EAAE,EAAE,GAAGN,KAAK;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,QACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YAAAwB,QAAA,gBACIxB,OAAA;cACIgC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,QAAQ;cAAA,GACV1B,QAAQ,CAACqB,KAAK,CAAC;cACnBM,EAAE,EAAE,GAAGN,KAAK;YAAU;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,YACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YAAAwB,QAAA,gBACIxB,OAAA;cACIgC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,OAAO;cAAA,GACT1B,QAAQ,CAACqB,KAAK,CAAC;cACnBM,EAAE,EAAE,GAAGN,KAAK;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,YACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,GACNV,KAAK,KAAK,UAAU,gBACpB5B,OAAA;UAAK+B,SAAS,EAAC,mBAAmB;UAAAP,QAAA,gBAC9BxB,OAAA;YACIgC,IAAI,EAAC,UAAU;YAAA,GACXzB,QAAQ,CAACqB,KAAK,CAAC;YACnBM,EAAE,EAAEN,KAAM;YACVG,SAAS,EAAC;UAAiD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACFtC,OAAA;YAAOuC,OAAO,EAAEX,KAAM;YAACG,SAAS,EAAC,kCAAkC;YAAAP,QAAA,EAAC;UAEpE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAT4BT,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUxC,CAAC,GACNV,KAAK,KAAK,OAAO,gBACjB5B,OAAA,CAACL,UAAU;UACP6C,IAAI,EAAC,OAAO;UACZ/B,OAAO,EAAEA,OAAQ;UACjBgC,MAAM,EAAEA,CAAC;YAAEb;UAAM,CAAC,kBACd5B,OAAA,CAACF,YAAY;YAAA,GAAK8B,KAAK;YAAEc,QAAQ,EAAErB;UAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe;QAC7D;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEM,CAAC,gBAEb5C,cAAA;UACIqC,SAAS,EAAC,0IAA0I;UAAA,GAChJxB,QAAQ,CAACqB,KAAK,CAAC;UACnBe,GAAG,EAAEd,KAAM;UACXG,IAAI,EAAEJ,KAAK,CAACgB,QAAQ,CAAC,UAAU,CAAC,IAAIhB,KAAK,CAACgB,QAAQ,CAAC,iBAAiB,CAAC,GAAG,UAAU,GAAG,MAAO;UAC5FV,EAAE,EAAEN,KAAM;UACVY,IAAI,EAAEZ,KAAM;UACZiB,WAAW,EAAEjB,KAAK,CAACgB,QAAQ,CAAC,UAAU,CAAC,IAAIhB,KAAK,CAACgB,QAAQ,CAAC,aAAa,CAAC,GAAG,UAAU,GAAG,QAAQhB,KAAK,CAACkB,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC,EAAG;UAAAC,MAAA;UAAAC,QAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CACpJ,CACJ,EACA3B,MAAM,CAACiB,KAAK,CAAC,iBACV5B,OAAA;UAAG+B,SAAS,EAAC,2BAA2B;UAAAP,QAAA,GAAAM,aAAA,GAAEnB,MAAM,CAACiB,KAAK,CAAC,cAAAE,aAAA,uBAAbA,aAAA,CAAeoB;QAAO;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACvE;MAAA,GA9DsBT,KAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+D3B,CAAC;IAAA,CAET,CAAC,EACDlC,GAAG,iBACAJ,OAAA;MACIgC,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAC,iFAAiF;MAAAP,QAAA,EAE1FpB;IAAG;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEC,CAAC;AAEf,CAAC;AAAAhC,EAAA,CApGKL,IAAI;EAAA,QAGoEL,OAAO;AAAA;AAAAuD,EAAA,GAH/ElD,IAAI;AAsGV,eAAeA,IAAI;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}