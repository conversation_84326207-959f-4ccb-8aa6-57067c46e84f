{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\auth\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Form from '../../components/Form';\nimport { ValidationLogin } from '../../validation/ValidationLogin';\nimport AuthAPI from '../../api/AuthAPI';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  localStorage.clear();\n  const navigate = useNavigate();\n  const initialValues = {\n    username: '',\n    password: '',\n    remember: false\n  };\n  const onSubmit = async data => {\n    try {\n      const response = await AuthAPI.login({\n        username: data.username,\n        password: data.password\n      });\n      const token = response.data.token;\n      const userId = response.data.userId;\n      localStorage.setItem('token', token);\n      // localStorage.setItem('userId', userId);\n      // localStorage.setItem('role', response.data.role);\n      // localStorage.setItem('username', response.data.username);\n      alert('Đăng nhập thành công!');\n      navigate('/homepage'); // chuyển hướng về homepage\n    } catch (error) {\n      alert('Đăng nhập thất bại!');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-8 rounded-lg shadow-md w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-center mb-6\",\n        children: \"\\u0110\\u0103ng Nh\\u1EADp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: onSubmit,\n        initialValues: initialValues,\n        btn: \"\\u0110\\u0103ng Nh\\u1EADp\",\n        validation: ValidationLogin\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/fogot-password'),\n          className: \"text-sm text-gray-500 hover:underline\",\n          children: \"Qu\\xEAn M\\u1EADt Kh\\u1EA9u?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/register'),\n          className: \"text-sm text-red-600 font-medium hover:underline\",\n          children: \"T\\u1EA1o T\\xE0i Kho\\u1EA3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useNavigate", "Form", "ValidationLogin", "AuthAPI", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "localStorage", "clear", "navigate", "initialValues", "username", "password", "remember", "onSubmit", "data", "response", "login", "token", "userId", "setItem", "alert", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "btn", "validation", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/auth/LoginPage.js"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport Form from '../../components/Form';\r\nimport { ValidationLogin } from '../../validation/ValidationLogin';\r\nimport AuthAPI from '../../api/AuthAPI';\r\n\r\nconst LoginPage = () => {\r\n  localStorage.clear();\r\n  const navigate = useNavigate();\r\n\r\n  const initialValues = { username: '', password: '', remember: false };\r\n\r\n  const onSubmit = async (data) => {\r\n    try {\r\n      const response = await AuthAPI.login({\r\n        username: data.username,\r\n        password: data.password,\r\n      });\r\n      const token = response.data.token;\r\n      const userId = response.data.userId;\r\n      localStorage.setItem('token', token);\r\n      // localStorage.setItem('userId', userId);\r\n      // localStorage.setItem('role', response.data.role);\r\n      // localStorage.setItem('username', response.data.username);\r\n      alert('<PERSON><PERSON>ng nhập thành công!');\r\n      navigate('/homepage'); // chuyển hướng về homepage\r\n    } catch (error) {\r\n      alert('Đăng nhập thất bại!');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-100\">\r\n      <div className=\"bg-white p-8 rounded-lg shadow-md w-full max-w-md\">\r\n        <h1 className=\"text-2xl font-bold text-center mb-6\">Đăng Nhập</h1>\r\n        <Form onSubmit={onSubmit} initialValues={initialValues} btn=\"Đăng Nhập\" validation={ValidationLogin} />\r\n        <div className=\"mt-4 text-center\">\r\n          <button\r\n            onClick={() => navigate('/fogot-password')}\r\n            className=\"text-sm text-gray-500 hover:underline\"\r\n          >\r\n            Quên Mật Khẩu?\r\n          </button>\r\n          <button\r\n            onClick={() => navigate('/register')}\r\n            className=\"text-sm text-red-600 font-medium hover:underline\"\r\n          >\r\n            Tạo Tài Khoản\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,IAAI,MAAM,uBAAuB;AACxC,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,OAAO,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtBC,YAAY,CAACC,KAAK,CAAC,CAAC;EACpB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,aAAa,GAAG;IAAEC,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAErE,MAAMC,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMd,OAAO,CAACe,KAAK,CAAC;QACnCN,QAAQ,EAAEI,IAAI,CAACJ,QAAQ;QACvBC,QAAQ,EAAEG,IAAI,CAACH;MACjB,CAAC,CAAC;MACF,MAAMM,KAAK,GAAGF,QAAQ,CAACD,IAAI,CAACG,KAAK;MACjC,MAAMC,MAAM,GAAGH,QAAQ,CAACD,IAAI,CAACI,MAAM;MACnCZ,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEF,KAAK,CAAC;MACpC;MACA;MACA;MACAG,KAAK,CAAC,uBAAuB,CAAC;MAC9BZ,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdD,KAAK,CAAC,qBAAqB,CAAC;IAC9B;EACF,CAAC;EAED,oBACEjB,OAAA;IAAKmB,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxEpB,OAAA;MAAKmB,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChEpB,OAAA;QAAImB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClExB,OAAA,CAACJ,IAAI;QAACc,QAAQ,EAAEA,QAAS;QAACJ,aAAa,EAAEA,aAAc;QAACmB,GAAG,EAAC,0BAAW;QAACC,UAAU,EAAE7B;MAAgB;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvGxB,OAAA;QAAKmB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BpB,OAAA;UACE2B,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,iBAAiB,CAAE;UAC3Cc,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAClD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA;UACE2B,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,WAAW,CAAE;UACrCc,SAAS,EAAC,kDAAkD;UAAAC,QAAA,EAC7D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA/CID,SAAS;EAAA,QAEIN,WAAW;AAAA;AAAAiC,EAAA,GAFxB3B,SAAS;AAiDf,eAAeA,SAAS;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}