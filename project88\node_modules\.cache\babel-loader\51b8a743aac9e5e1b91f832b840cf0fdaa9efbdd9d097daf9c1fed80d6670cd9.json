{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class Hour0to23Parser extends Parser {\n  priority = 70;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"H\":\n        return parseNumericPattern(numericPatterns.hour23h, dateString);\n      case \"Ho\":\n        return match.ordinalNumber(dateString, {\n          unit: \"hour\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 23;\n  }\n  set(date, _flags, value) {\n    date.setHours(value, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"a\", \"b\", \"h\", \"K\", \"k\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["numericPatterns", "<PERSON><PERSON><PERSON>", "parseNDigits", "parseNumericPattern", "Hour0to23Parser", "priority", "parse", "dateString", "token", "match", "hour23h", "ordinalNumber", "unit", "length", "validate", "_date", "value", "set", "date", "_flags", "setHours", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/parse/_lib/parsers/Hour0to23Parser.js"], "sourcesContent": ["import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour0to23Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"H\":\n        return parseNumericPattern(numericPatterns.hour23h, dateString);\n      case \"Ho\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 23;\n  }\n\n  set(date, _flags, value) {\n    date.setHours(value, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"b\", \"h\", \"K\", \"k\", \"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;AACjD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,aAAa;AAE/D,OAAO,MAAMC,eAAe,SAASH,MAAM,CAAC;EAC1CI,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;QACN,OAAOL,mBAAmB,CAACH,eAAe,CAACU,OAAO,EAAEH,UAAU,CAAC;MACjE,KAAK,IAAI;QACP,OAAOE,KAAK,CAACE,aAAa,CAACJ,UAAU,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAC,CAAC;MAC1D;QACE,OAAOV,YAAY,CAACM,KAAK,CAACK,MAAM,EAAEN,UAAU,CAAC;IACjD;EACF;EAEAO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE;EAClC;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEH,KAAK,EAAE;IACvBE,IAAI,CAACE,QAAQ,CAACJ,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7B,OAAOE,IAAI;EACb;EAEAG,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}