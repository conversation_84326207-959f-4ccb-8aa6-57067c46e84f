{"ast": null, "code": "import { getDefaultOptions, setDefaultOptions as setInternalDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * @name setDefaultOptions\n * @category Common Helpers\n * @summary Set default options including locale.\n * @pure false\n *\n * @description\n * Sets the defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * @param options - An object with options\n *\n * @example\n * // Set global locale:\n * import { es } from 'date-fns/locale'\n * setDefaultOptions({ locale: es })\n * const result = format(new Date(2014, 8, 2), 'PPPP')\n * //=> 'martes, 2 de septiembre de 2014'\n *\n * @example\n * // Start of the week for 2 September 2014:\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Start of the week for 2 September 2014,\n * // when we set that week starts on Monday by default:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Mon Sep 01 2014 00:00:00\n *\n * @example\n * // Manually set options take priority over default options:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2), { weekStartsOn: 0 })\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Remove the option by setting it to `undefined`:\n * setDefaultOptions({ weekStartsOn: 1 })\n * setDefaultOptions({ weekStartsOn: undefined })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n */\nexport function setDefaultOptions(options) {\n  const result = {};\n  const defaultOptions = getDefaultOptions();\n  for (const property in defaultOptions) {\n    if (Object.prototype.hasOwnProperty.call(defaultOptions, property)) {\n      // [TODO] I challenge you to fix the type\n      result[property] = defaultOptions[property];\n    }\n  }\n  for (const property in options) {\n    if (Object.prototype.hasOwnProperty.call(options, property)) {\n      if (options[property] === undefined) {\n        // [TODO] I challenge you to fix the type\n        delete result[property];\n      } else {\n        // [TODO] I challenge you to fix the type\n        result[property] = options[property];\n      }\n    }\n  }\n  setInternalDefaultOptions(result);\n}\n\n// Fallback for modularized imports:\nexport default setDefaultOptions;", "map": {"version": 3, "names": ["getDefaultOptions", "setDefaultOptions", "setInternalDefaultOptions", "options", "result", "defaultOptions", "property", "Object", "prototype", "hasOwnProperty", "call", "undefined"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/setDefaultOptions.js"], "sourcesContent": ["import {\n  getDefaultOptions,\n  setDefaultOptions as setInternalDefaultOptions,\n} from \"./_lib/defaultOptions.js\";\n\n/**\n * @name setDefaultOptions\n * @category Common Helpers\n * @summary Set default options including locale.\n * @pure false\n *\n * @description\n * Sets the defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * @param options - An object with options\n *\n * @example\n * // Set global locale:\n * import { es } from 'date-fns/locale'\n * setDefaultOptions({ locale: es })\n * const result = format(new Date(2014, 8, 2), 'PPPP')\n * //=> 'martes, 2 de septiembre de 2014'\n *\n * @example\n * // Start of the week for 2 September 2014:\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Start of the week for 2 September 2014,\n * // when we set that week starts on Monday by default:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Mon Sep 01 2014 00:00:00\n *\n * @example\n * // Manually set options take priority over default options:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2), { weekStartsOn: 0 })\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Remove the option by setting it to `undefined`:\n * setDefaultOptions({ weekStartsOn: 1 })\n * setDefaultOptions({ weekStartsOn: undefined })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n */\nexport function setDefaultOptions(options) {\n  const result = {};\n  const defaultOptions = getDefaultOptions();\n\n  for (const property in defaultOptions) {\n    if (Object.prototype.hasOwnProperty.call(defaultOptions, property)) {\n      // [TODO] I challenge you to fix the type\n      result[property] = defaultOptions[property];\n    }\n  }\n\n  for (const property in options) {\n    if (Object.prototype.hasOwnProperty.call(options, property)) {\n      if (options[property] === undefined) {\n        // [TODO] I challenge you to fix the type\n        delete result[property];\n      } else {\n        // [TODO] I challenge you to fix the type\n        result[property] = options[property];\n      }\n    }\n  }\n\n  setInternalDefaultOptions(result);\n}\n\n// Fallback for modularized imports:\nexport default setDefaultOptions;\n"], "mappings": "AAAA,SACEA,iBAAiB,EACjBC,iBAAiB,IAAIC,yBAAyB,QACzC,0BAA0B;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASD,iBAAiBA,CAACE,OAAO,EAAE;EACzC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,cAAc,GAAGL,iBAAiB,CAAC,CAAC;EAE1C,KAAK,MAAMM,QAAQ,IAAID,cAAc,EAAE;IACrC,IAAIE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,cAAc,EAAEC,QAAQ,CAAC,EAAE;MAClE;MACAF,MAAM,CAACE,QAAQ,CAAC,GAAGD,cAAc,CAACC,QAAQ,CAAC;IAC7C;EACF;EAEA,KAAK,MAAMA,QAAQ,IAAIH,OAAO,EAAE;IAC9B,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,OAAO,EAAEG,QAAQ,CAAC,EAAE;MAC3D,IAAIH,OAAO,CAACG,QAAQ,CAAC,KAAKK,SAAS,EAAE;QACnC;QACA,OAAOP,MAAM,CAACE,QAAQ,CAAC;MACzB,CAAC,MAAM;QACL;QACAF,MAAM,CAACE,QAAQ,CAAC,GAAGH,OAAO,CAACG,QAAQ,CAAC;MACtC;IACF;EACF;EAEAJ,yBAAyB,CAACE,MAAM,CAAC;AACnC;;AAEA;AACA,eAAeH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}