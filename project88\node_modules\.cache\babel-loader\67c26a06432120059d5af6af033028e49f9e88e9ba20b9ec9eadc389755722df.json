{"ast": null, "code": "import api from \"./axiosClient\";\nconst getAllEmployees = (page, size, filter) => {\n  return api.get('/admin', {\n    params: {\n      page,\n      size,\n      ...filter\n    }\n  });\n};\nconst getEmployeeByUsername = () => {\n  return api.get(`/users/profile`);\n};\nconst createEmployee = body => {\n  return api.post('/admin/create-employee', body);\n};\nconst employeeAPI = {\n  getAllEmployees,\n  getEmployeeByUsername,\n  createEmployee\n};\nexport default employeeAPI;", "map": {"version": 3, "names": ["api", "getAllEmployees", "page", "size", "filter", "get", "params", "getEmployeeByUsername", "createEmployee", "body", "post", "employeeAPI"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/api/EmployeeAPI.js"], "sourcesContent": ["import api from \"./axiosClient\";\r\n\r\n\r\nconst getAllEmployees = (page, size, filter) => {\r\n    return api.get('/admin', {\r\n        params: {\r\n            page,\r\n            size,\r\n            ...filter\r\n        }\r\n    })\r\n}\r\n\r\nconst getEmployeeByUsername = () => {\r\n    return api.get(`/users/profile`);\r\n}\r\n\r\nconst createEmployee = (body) => {\r\n    return api.post('/admin/create-employee', body);\r\n}\r\n\r\nconst employeeAPI = {\r\n    getAllEmployees,\r\n    getEmployeeByUsername,\r\n    createEmployee\r\n}\r\n\r\nexport default employeeAPI;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,eAAe;AAG/B,MAAMC,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,KAAK;EAC5C,OAAOJ,GAAG,CAACK,GAAG,CAAC,QAAQ,EAAE;IACrBC,MAAM,EAAE;MACJJ,IAAI;MACJC,IAAI;MACJ,GAAGC;IACP;EACJ,CAAC,CAAC;AACN,CAAC;AAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;EAChC,OAAOP,GAAG,CAACK,GAAG,CAAC,gBAAgB,CAAC;AACpC,CAAC;AAED,MAAMG,cAAc,GAAIC,IAAI,IAAK;EAC7B,OAAOT,GAAG,CAACU,IAAI,CAAC,wBAAwB,EAAED,IAAI,CAAC;AACnD,CAAC;AAED,MAAME,WAAW,GAAG;EAChBV,eAAe;EACfM,qBAAqB;EACrBC;AACJ,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}