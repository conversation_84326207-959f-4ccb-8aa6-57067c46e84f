{"ast": null, "code": "export function buildMatchFn(args) {\n  return function (string) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const width = options.width;\n    const matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n    const parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    const key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, pattern => pattern.test(matchedString)) :\n    // [TODO] -- I challenge you to fix the type\n    findKey(parsePatterns, pattern => pattern.test(matchedString));\n    let value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ?\n    // [TODO] -- I challenge you to fix the type\n    options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return {\n      value,\n      rest\n    };\n  };\n}\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["buildMatchFn", "args", "string", "options", "arguments", "length", "undefined", "width", "matchPattern", "matchPatterns", "defaultMatchWidth", "matchResult", "match", "matchedString", "parsePatterns", "defaultParseWidth", "key", "Array", "isArray", "findIndex", "pattern", "test", "<PERSON><PERSON><PERSON>", "value", "valueCallback", "rest", "slice", "object", "predicate", "Object", "prototype", "hasOwnProperty", "call", "array"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/locale/_lib/buildMatchFn.js"], "sourcesContent": ["export function buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n\n    const matchPattern =\n      (width && args.matchPatterns[width]) ||\n      args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n\n    const parsePatterns =\n      (width && args.parsePatterns[width]) ||\n      args.parsePatterns[args.defaultParseWidth];\n\n    const key = Array.isArray(parsePatterns)\n      ? findIndex(parsePatterns, (pattern) => pattern.test(matchedString))\n      : // [TODO] -- I challenge you to fix the type\n        findKey(parsePatterns, (pattern) => pattern.test(matchedString));\n\n    let value;\n\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback\n      ? // [TODO] -- I challenge you to fix the type\n        options.valueCallback(value)\n      : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (\n      Object.prototype.hasOwnProperty.call(object, key) &&\n      predicate(object[key])\n    ) {\n      return key;\n    }\n  }\n  return undefined;\n}\n\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\n"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,UAACC,MAAM,EAAmB;IAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC1B,MAAMG,KAAK,GAAGJ,OAAO,CAACI,KAAK;IAE3B,MAAMC,YAAY,GACfD,KAAK,IAAIN,IAAI,CAACQ,aAAa,CAACF,KAAK,CAAC,IACnCN,IAAI,CAACQ,aAAa,CAACR,IAAI,CAACS,iBAAiB,CAAC;IAC5C,MAAMC,WAAW,GAAGT,MAAM,CAACU,KAAK,CAACJ,YAAY,CAAC;IAE9C,IAAI,CAACG,WAAW,EAAE;MAChB,OAAO,IAAI;IACb;IACA,MAAME,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;IAEpC,MAAMG,aAAa,GAChBP,KAAK,IAAIN,IAAI,CAACa,aAAa,CAACP,KAAK,CAAC,IACnCN,IAAI,CAACa,aAAa,CAACb,IAAI,CAACc,iBAAiB,CAAC;IAE5C,MAAMC,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACJ,aAAa,CAAC,GACpCK,SAAS,CAACL,aAAa,EAAGM,OAAO,IAAKA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC,CAAC;IAClE;IACAS,OAAO,CAACR,aAAa,EAAGM,OAAO,IAAKA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC,CAAC;IAEpE,IAAIU,KAAK;IAETA,KAAK,GAAGtB,IAAI,CAACuB,aAAa,GAAGvB,IAAI,CAACuB,aAAa,CAACR,GAAG,CAAC,GAAGA,GAAG;IAC1DO,KAAK,GAAGpB,OAAO,CAACqB,aAAa;IACzB;IACArB,OAAO,CAACqB,aAAa,CAACD,KAAK,CAAC,GAC5BA,KAAK;IAET,MAAME,IAAI,GAAGvB,MAAM,CAACwB,KAAK,CAACb,aAAa,CAACR,MAAM,CAAC;IAE/C,OAAO;MAAEkB,KAAK;MAAEE;IAAK,CAAC;EACxB,CAAC;AACH;AAEA,SAASH,OAAOA,CAACK,MAAM,EAAEC,SAAS,EAAE;EAClC,KAAK,MAAMZ,GAAG,IAAIW,MAAM,EAAE;IACxB,IACEE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEX,GAAG,CAAC,IACjDY,SAAS,CAACD,MAAM,CAACX,GAAG,CAAC,CAAC,EACtB;MACA,OAAOA,GAAG;IACZ;EACF;EACA,OAAOV,SAAS;AAClB;AAEA,SAASa,SAASA,CAACc,KAAK,EAAEL,SAAS,EAAE;EACnC,KAAK,IAAIZ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGiB,KAAK,CAAC5B,MAAM,EAAEW,GAAG,EAAE,EAAE;IAC3C,IAAIY,SAAS,CAACK,KAAK,CAACjB,GAAG,CAAC,CAAC,EAAE;MACzB,OAAOA,GAAG;IACZ;EACF;EACA,OAAOV,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}