{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfHour} function options.\n */\n\n/**\n * @name startOfHour\n * @category Hour Helpers\n * @summary Return the start of an hour for the given date.\n *\n * @description\n * Return the start of an hour for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an hour\n *\n * @example\n * // The start of an hour for 2 September 2014 11:55:00:\n * const result = startOfHour(new Date(2014, 8, 2, 11, 55))\n * //=> Tue Sep 02 2014 11:00:00\n */\nexport function startOfHour(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMinutes(0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfHour;", "map": {"version": 3, "names": ["toDate", "startOfHour", "date", "options", "_date", "in", "setMinutes"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/startOfHour.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfHour} function options.\n */\n\n/**\n * @name startOfHour\n * @category Hour Helpers\n * @summary Return the start of an hour for the given date.\n *\n * @description\n * Return the start of an hour for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an hour\n *\n * @example\n * // The start of an hour for 2 September 2014 11:55:00:\n * const result = startOfHour(new Date(2014, 8, 2, 11, 55))\n * //=> Tue Sep 02 2014 11:00:00\n */\nexport function startOfHour(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMinutes(0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfHour;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB,OAAOF,KAAK;AACd;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}