{"ast": null, "code": "import employeeAPI from \"../../api/EmployeeAPI\";\nimport { getEmployeeByUsername } from \"../../redux/slices/employeeSlice\";\nconst EmployeeService = {\n  getAllEmployees: async (page, size, filter) => {\n    return await employeeAPI.getAllEmployees(page, size, filter);\n  },\n  getEmployeeByUsername: async () => {\n    return await employeeAPI.getEmployeeByUsername();\n  },\n  createEmployee: async body => {\n    return await employeeAPI.createEmployee(body);\n  }\n};\nexport default EmployeeService;", "map": {"version": 3, "names": ["employeeAPI", "getEmployeeByUsername", "EmployeeService", "getAllEmployees", "page", "size", "filter", "createEmployee", "body"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/Employee/EmployeeService.js"], "sourcesContent": ["import employeeAPI from \"../../api/EmployeeAPI\";\r\nimport { getEmployeeByUsername } from \"../../redux/slices/employeeSlice\";\r\n\r\nconst EmployeeService = {\r\n    getAllEmployees: async (page, size, filter) => {\r\n        return await employeeAPI.getAllEmployees(page, size, filter);\r\n    },\r\n    getEmployeeByUsername: async () => {\r\n        return await employeeAPI.getEmployeeByUsername();\r\n    },\r\n    createEmployee: async (body) => {\r\n        return await employeeAPI.createEmployee(body);\r\n    }\r\n\r\n}\r\n\r\nexport default EmployeeService;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,uBAAuB;AAC/C,SAASC,qBAAqB,QAAQ,kCAAkC;AAExE,MAAMC,eAAe,GAAG;EACpBC,eAAe,EAAE,MAAAA,CAAOC,IAAI,EAAEC,IAAI,EAAEC,MAAM,KAAK;IAC3C,OAAO,MAAMN,WAAW,CAACG,eAAe,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,CAAC;EAChE,CAAC;EACDL,qBAAqB,EAAE,MAAAA,CAAA,KAAY;IAC/B,OAAO,MAAMD,WAAW,CAACC,qBAAqB,CAAC,CAAC;EACpD,CAAC;EACDM,cAAc,EAAE,MAAOC,IAAI,IAAK;IAC5B,OAAO,MAAMR,WAAW,CAACO,cAAc,CAACC,IAAI,CAAC;EACjD;AAEJ,CAAC;AAED,eAAeN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}