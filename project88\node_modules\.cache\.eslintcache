[{"C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\LoginPage.js": "5", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\HomePage.js": "6", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\FogotPassword.js": "7", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\ChangePassword.js": "8", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\Register.js": "9", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Profile.js": "10", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\registerSlice.js": "11", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileContent.js": "12", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationChangePassword.js": "13", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Footer.js": "14", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Form.js": "15", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\Validation.js": "16", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\RegisterService.js": "17", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Header.js": "18", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationLogin.js": "19", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserApi.js": "20", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\PaymentModal.js": "21", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationModal.js": "22", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\SuccessModal.js": "23", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\FailureModal.js": "24", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\DepositModal.js": "25", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationPopup.js": "26", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Test.js": "27", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\transactionSlice.js": "28", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Transaction.js": "29", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\MyDatePicker.js": "30", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\TransactionService.js": "31", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Table.js": "32", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\TopUp.js": "33", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\TransactionAPI.js": "34", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Redeem.js": "35", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Transfer.js": "36", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\userSlice.js": "37", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TransferForm.js": "38", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileService.js": "39", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserAPIv2.js": "40", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\axiosClient.js": "41", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\VerifyPage.js": "42", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\EmployeeList.js": "43", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\employeeSlice.js": "44", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\depositSlice.js": "45", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\SavingTotal.js": "46", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AuthAPI.js": "47", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\AdminContent.js": "48", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TranferUserBalance.js": "49", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserContent.js": "50", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserService.js": "51", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\DepositService.js": "52", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Deposit.js": "53", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Search.js": "54", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Pagination.js": "55", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Bills.js": "56", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\DepositAPI.js": "57", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AdminAPI.js": "58", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\EditUserModal.js": "59", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\EditUserValidatiion.js": "60", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\CreateEmployee.js": "61", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\utils\\auth.js": "62", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\CreateEmployeeValidation.js": "63", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Employee\\EmployeeContent.js": "64", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UsersList.js": "65", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TransactionModal.js": "66", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Employee\\EmployeeService.js": "67", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\EmployeeAPI.js": "68"}, {"size": 630, "mtime": 1747316253164, "results": "69", "hashOfConfig": "70"}, {"size": 2445, "mtime": 1751259017603, "results": "71", "hashOfConfig": "70"}, {"size": 375, "mtime": 1747026816582, "results": "72", "hashOfConfig": "70"}, {"size": 756, "mtime": 1750306694193, "results": "73", "hashOfConfig": "70"}, {"size": 1980, "mtime": 1751259017618, "results": "74", "hashOfConfig": "70"}, {"size": 1100, "mtime": 1751337712634, "results": "75", "hashOfConfig": "70"}, {"size": 2247, "mtime": 1751259017618, "results": "76", "hashOfConfig": "70"}, {"size": 1543, "mtime": 1749909139877, "results": "77", "hashOfConfig": "70"}, {"size": 1800, "mtime": 1751259017634, "results": "78", "hashOfConfig": "70"}, {"size": 363, "mtime": 1750306694168, "results": "79", "hashOfConfig": "70"}, {"size": 1479, "mtime": 1747316253167, "results": "80", "hashOfConfig": "70"}, {"size": 7091, "mtime": 1751259017634, "results": "81", "hashOfConfig": "70"}, {"size": 414, "mtime": 1747317593325, "results": "82", "hashOfConfig": "70"}, {"size": 153, "mtime": 1750306694127, "results": "83", "hashOfConfig": "70"}, {"size": 4637, "mtime": 1751259017603, "results": "84", "hashOfConfig": "70"}, {"size": 1835, "mtime": 1751259017650, "results": "85", "hashOfConfig": "70"}, {"size": 670, "mtime": 1749731390975, "results": "86", "hashOfConfig": "70"}, {"size": 5240, "mtime": 1751259017603, "results": "87", "hashOfConfig": "70"}, {"size": 243, "mtime": 1747124305813, "results": "88", "hashOfConfig": "70"}, {"size": 1173, "mtime": 1750306694119, "results": "89", "hashOfConfig": "70"}, {"size": 2542, "mtime": 1748951277658, "results": "90", "hashOfConfig": "70"}, {"size": 1042, "mtime": 1747485672539, "results": "91", "hashOfConfig": "70"}, {"size": 1717, "mtime": 1747796635496, "results": "92", "hashOfConfig": "70"}, {"size": 1308, "mtime": 1747486104191, "results": "93", "hashOfConfig": "70"}, {"size": 3826, "mtime": 1747796665260, "results": "94", "hashOfConfig": "70"}, {"size": 1495, "mtime": 1747796197314, "results": "95", "hashOfConfig": "70"}, {"size": 1026, "mtime": 1751259017618, "results": "96", "hashOfConfig": "70"}, {"size": 5095, "mtime": 1750306694187, "results": "97", "hashOfConfig": "70"}, {"size": 1210, "mtime": 1750306694173, "results": "98", "hashOfConfig": "70"}, {"size": 1195, "mtime": 1750306694134, "results": "99", "hashOfConfig": "70"}, {"size": 507, "mtime": 1750306694175, "results": "100", "hashOfConfig": "70"}, {"size": 6896, "mtime": 1750306694140, "results": "101", "hashOfConfig": "70"}, {"size": 2121, "mtime": 1749731390971, "results": "102", "hashOfConfig": "70"}, {"size": 516, "mtime": 1750306694116, "results": "103", "hashOfConfig": "70"}, {"size": 13552, "mtime": 1751259017634, "results": "104", "hashOfConfig": "70"}, {"size": 3643, "mtime": 1751259017634, "results": "105", "hashOfConfig": "70"}, {"size": 5586, "mtime": 1751259017650, "results": "106", "hashOfConfig": "70"}, {"size": 2186, "mtime": 1748834496576, "results": "107", "hashOfConfig": "70"}, {"size": 191, "mtime": 1748834496592, "results": "108", "hashOfConfig": "70"}, {"size": 717, "mtime": 1748951277651, "results": "109", "hashOfConfig": "70"}, {"size": 1157, "mtime": 1750306694122, "results": "110", "hashOfConfig": "70"}, {"size": 1292, "mtime": 1749731390952, "results": "111", "hashOfConfig": "70"}, {"size": 1273, "mtime": 1750306694152, "results": "112", "hashOfConfig": "70"}, {"size": 3351, "mtime": 1751337718411, "results": "113", "hashOfConfig": "70"}, {"size": 1526, "mtime": 1749650960789, "results": "114", "hashOfConfig": "70"}, {"size": 3741, "mtime": 1749731390981, "results": "115", "hashOfConfig": "70"}, {"size": 576, "mtime": 1749909139877, "results": "116", "hashOfConfig": "70"}, {"size": 4634, "mtime": 1751259017618, "results": "117", "hashOfConfig": "70"}, {"size": 924, "mtime": 1748951277665, "results": "118", "hashOfConfig": "70"}, {"size": 8854, "mtime": 1751259017634, "results": "119", "hashOfConfig": "70"}, {"size": 492, "mtime": 1750306694180, "results": "120", "hashOfConfig": "70"}, {"size": 419, "mtime": 1749650960782, "results": "121", "hashOfConfig": "70"}, {"size": 7200, "mtime": 1751259017634, "results": "122", "hashOfConfig": "70"}, {"size": 1876, "mtime": 1749731390939, "results": "123", "hashOfConfig": "70"}, {"size": 1455, "mtime": 1750306694137, "results": "124", "hashOfConfig": "70"}, {"size": 1555, "mtime": 1751259017634, "results": "125", "hashOfConfig": "70"}, {"size": 552, "mtime": 1749731390929, "results": "126", "hashOfConfig": "70"}, {"size": 265, "mtime": 1750306694110, "results": "127", "hashOfConfig": "70"}, {"size": 3086, "mtime": 1751259017603, "results": "128", "hashOfConfig": "70"}, {"size": 1036, "mtime": 1751259017650, "results": "129", "hashOfConfig": "70"}, {"size": 1486, "mtime": 1751259017618, "results": "130", "hashOfConfig": "70"}, {"size": 511, "mtime": 1751259017650, "results": "131", "hashOfConfig": "70"}, {"size": 1503, "mtime": 1751259017650, "results": "132", "hashOfConfig": "70"}, {"size": 6387, "mtime": 1751259017618, "results": "133", "hashOfConfig": "70"}, {"size": 1244, "mtime": 1750306694183, "results": "134", "hashOfConfig": "70"}, {"size": 3933, "mtime": 1750306694142, "results": "135", "hashOfConfig": "70"}, {"size": 1063, "mtime": 1751337778089, "results": "136", "hashOfConfig": "70"}, {"size": 528, "mtime": 1751259017603, "results": "137", "hashOfConfig": "70"}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hzh87o", {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\LoginPage.js", ["342"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\HomePage.js", ["343", "344", "345"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\FogotPassword.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\ChangePassword.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\Register.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Profile.js", ["346", "347"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\registerSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileContent.js", ["348", "349", "350"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationChangePassword.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Form.js", ["351"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\Validation.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\RegisterService.js", ["352"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationLogin.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserApi.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\PaymentModal.js", ["353"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\SuccessModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\FailureModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\DepositModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationPopup.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Test.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\transactionSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Transaction.js", ["354", "355", "356", "357", "358"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\MyDatePicker.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\TransactionService.js", ["359"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Table.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\TopUp.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\TransactionAPI.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Redeem.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Transfer.js", ["360", "361"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\userSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TransferForm.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileService.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserAPIv2.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\axiosClient.js", ["362", "363"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\VerifyPage.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\EmployeeList.js", ["364", "365", "366", "367", "368", "369", "370", "371", "372", "373"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\employeeSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\depositSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\SavingTotal.js", ["374", "375", "376"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AuthAPI.js", ["377"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\AdminContent.js", ["378", "379", "380", "381", "382", "383", "384", "385"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TranferUserBalance.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserContent.js", ["386", "387", "388", "389", "390", "391", "392", "393"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserService.js", ["394"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\DepositService.js", ["395"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Deposit.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Search.js", ["396"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Bills.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\DepositAPI.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AdminAPI.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\EditUserModal.js", ["397", "398", "399"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\EditUserValidatiion.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\CreateEmployee.js", ["400"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\utils\\auth.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\CreateEmployeeValidation.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Employee\\EmployeeContent.js", ["401", "402", "403", "404", "405", "406", "407", "408", "409"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UsersList.js", ["410", "411", "412", "413", "414"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TransactionModal.js", ["415", "416", "417", "418"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Employee\\EmployeeService.js", ["419"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\EmployeeAPI.js", [], [], {"ruleId": "420", "severity": 1, "message": "421", "line": 20, "column": 13, "nodeType": "422", "messageId": "423", "endLine": 20, "endColumn": 19}, {"ruleId": "420", "severity": 1, "message": "424", "line": 2, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 14}, {"ruleId": "420", "severity": 1, "message": "425", "line": 3, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 3, "endColumn": 14}, {"ruleId": "420", "severity": 1, "message": "426", "line": 19, "column": 11, "nodeType": "422", "messageId": "423", "endLine": 19, "endColumn": 22}, {"ruleId": "420", "severity": 1, "message": "424", "line": 2, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 14}, {"ruleId": "420", "severity": 1, "message": "425", "line": 3, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 3, "endColumn": 14}, {"ruleId": "420", "severity": 1, "message": "427", "line": 4, "column": 18, "nodeType": "422", "messageId": "423", "endLine": 4, "endColumn": 21}, {"ruleId": "420", "severity": 1, "message": "428", "line": 17, "column": 12, "nodeType": "422", "messageId": "423", "endLine": 17, "endColumn": 20}, {"ruleId": "429", "severity": 1, "message": "430", "line": 70, "column": 33, "nodeType": "431", "endLine": 70, "endColumn": 147}, {"ruleId": "420", "severity": 1, "message": "432", "line": 1, "column": 17, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 26}, {"ruleId": "420", "severity": 1, "message": "433", "line": 1, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 15}, {"ruleId": "420", "severity": 1, "message": "434", "line": 1, "column": 17, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 25}, {"ruleId": "420", "severity": 1, "message": "427", "line": 5, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 5, "endColumn": 13}, {"ruleId": "420", "severity": 1, "message": "435", "line": 6, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 6, "endColumn": 17}, {"ruleId": "420", "severity": 1, "message": "436", "line": 14, "column": 27, "nodeType": "422", "messageId": "423", "endLine": 14, "endColumn": 34}, {"ruleId": "420", "severity": 1, "message": "437", "line": 14, "column": 36, "nodeType": "422", "messageId": "423", "endLine": 14, "endColumn": 41}, {"ruleId": "420", "severity": 1, "message": "438", "line": 15, "column": 18, "nodeType": "422", "messageId": "423", "endLine": 15, "endColumn": 25}, {"ruleId": "420", "severity": 1, "message": "439", "line": 2, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 32}, {"ruleId": "420", "severity": 1, "message": "440", "line": 48, "column": 13, "nodeType": "422", "messageId": "423", "endLine": 48, "endColumn": 21}, {"ruleId": "441", "severity": 1, "message": "442", "line": 110, "column": 6, "nodeType": "443", "endLine": 110, "endColumn": 8, "suggestions": "444"}, {"ruleId": "445", "severity": 1, "message": "446", "line": 30, "column": 28, "nodeType": "447", "messageId": "448", "endLine": 30, "endColumn": 30}, {"ruleId": "445", "severity": 1, "message": "446", "line": 30, "column": 61, "nodeType": "447", "messageId": "448", "endLine": 30, "endColumn": 63}, {"ruleId": "420", "severity": 1, "message": "434", "line": 1, "column": 17, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 25}, {"ruleId": "420", "severity": 1, "message": "449", "line": 2, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 14}, {"ruleId": "420", "severity": 1, "message": "450", "line": 2, "column": 16, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 22}, {"ruleId": "420", "severity": 1, "message": "451", "line": 2, "column": 24, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 30}, {"ruleId": "420", "severity": 1, "message": "452", "line": 2, "column": 32, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 36}, {"ruleId": "420", "severity": 1, "message": "453", "line": 6, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 6, "endColumn": 16}, {"ruleId": "420", "severity": 1, "message": "454", "line": 10, "column": 11, "nodeType": "422", "messageId": "423", "endLine": 10, "endColumn": 19}, {"ruleId": "420", "severity": 1, "message": "436", "line": 14, "column": 24, "nodeType": "422", "messageId": "423", "endLine": 14, "endColumn": 31}, {"ruleId": "420", "severity": 1, "message": "437", "line": 14, "column": 33, "nodeType": "422", "messageId": "423", "endLine": 14, "endColumn": 38}, {"ruleId": "441", "severity": 1, "message": "455", "line": 20, "column": 23, "nodeType": "456", "endLine": 20, "endColumn": 24}, {"ruleId": "420", "severity": 1, "message": "457", "line": 9, "column": 13, "nodeType": "422", "messageId": "423", "endLine": 9, "endColumn": 21}, {"ruleId": "420", "severity": 1, "message": "436", "line": 9, "column": 23, "nodeType": "422", "messageId": "423", "endLine": 9, "endColumn": 30}, {"ruleId": "420", "severity": 1, "message": "437", "line": 9, "column": 32, "nodeType": "422", "messageId": "423", "endLine": 9, "endColumn": 37}, {"ruleId": "420", "severity": 1, "message": "458", "line": 1, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 22}, {"ruleId": "420", "severity": 1, "message": "459", "line": 2, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 13}, {"ruleId": "420", "severity": 1, "message": "460", "line": 7, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 7, "endColumn": 21}, {"ruleId": "420", "severity": 1, "message": "461", "line": 22, "column": 12, "nodeType": "422", "messageId": "423", "endLine": 22, "endColumn": 24}, {"ruleId": "420", "severity": 1, "message": "462", "line": 22, "column": 26, "nodeType": "422", "messageId": "423", "endLine": 22, "endColumn": 41}, {"ruleId": "420", "severity": 1, "message": "463", "line": 23, "column": 12, "nodeType": "422", "messageId": "423", "endLine": 23, "endColumn": 20}, {"ruleId": "420", "severity": 1, "message": "464", "line": 23, "column": 22, "nodeType": "422", "messageId": "423", "endLine": 23, "endColumn": 33}, {"ruleId": "420", "severity": 1, "message": "465", "line": 27, "column": 13, "nodeType": "422", "messageId": "423", "endLine": 27, "endColumn": 26}, {"ruleId": "420", "severity": 1, "message": "466", "line": 27, "column": 40, "nodeType": "422", "messageId": "423", "endLine": 27, "endColumn": 51}, {"ruleId": "420", "severity": 1, "message": "467", "line": 1, "column": 17, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 20}, {"ruleId": "420", "severity": 1, "message": "468", "line": 4, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 4, "endColumn": 15}, {"ruleId": "420", "severity": 1, "message": "469", "line": 5, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 5, "endColumn": 14}, {"ruleId": "420", "severity": 1, "message": "470", "line": 15, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 15, "endColumn": 21}, {"ruleId": "420", "severity": 1, "message": "466", "line": 57, "column": 23, "nodeType": "422", "messageId": "423", "endLine": 57, "endColumn": 34}, {"ruleId": "420", "severity": 1, "message": "440", "line": 108, "column": 13, "nodeType": "422", "messageId": "423", "endLine": 108, "endColumn": 21}, {"ruleId": "441", "severity": 1, "message": "471", "line": 150, "column": 6, "nodeType": "443", "endLine": 150, "endColumn": 15, "suggestions": "472"}, {"ruleId": "441", "severity": 1, "message": "473", "line": 156, "column": 6, "nodeType": "443", "endLine": 156, "endColumn": 34, "suggestions": "474"}, {"ruleId": "420", "severity": 1, "message": "475", "line": 2, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 20}, {"ruleId": "420", "severity": 1, "message": "476", "line": 1, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 13}, {"ruleId": "441", "severity": 1, "message": "477", "line": 29, "column": 8, "nodeType": "443", "endLine": 29, "endColumn": 17, "suggestions": "478"}, {"ruleId": "420", "severity": 1, "message": "434", "line": 1, "column": 17, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 25}, {"ruleId": "420", "severity": 1, "message": "479", "line": 4, "column": 8, "nodeType": "422", "messageId": "423", "endLine": 4, "endColumn": 15}, {"ruleId": "420", "severity": 1, "message": "480", "line": 5, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 5, "endColumn": 18}, {"ruleId": "420", "severity": 1, "message": "481", "line": 16, "column": 19, "nodeType": "422", "messageId": "423", "endLine": 16, "endColumn": 22}, {"ruleId": "420", "severity": 1, "message": "482", "line": 15, "column": 12, "nodeType": "422", "messageId": "423", "endLine": 15, "endColumn": 18}, {"ruleId": "420", "severity": 1, "message": "483", "line": 15, "column": 20, "nodeType": "422", "messageId": "423", "endLine": 15, "endColumn": 29}, {"ruleId": "420", "severity": 1, "message": "484", "line": 20, "column": 12, "nodeType": "422", "messageId": "423", "endLine": 20, "endColumn": 29}, {"ruleId": "420", "severity": 1, "message": "485", "line": 20, "column": 31, "nodeType": "422", "messageId": "423", "endLine": 20, "endColumn": 51}, {"ruleId": "420", "severity": 1, "message": "486", "line": 21, "column": 12, "nodeType": "422", "messageId": "423", "endLine": 21, "endColumn": 28}, {"ruleId": "420", "severity": 1, "message": "487", "line": 21, "column": 30, "nodeType": "422", "messageId": "423", "endLine": 21, "endColumn": 49}, {"ruleId": "420", "severity": 1, "message": "466", "line": 31, "column": 25, "nodeType": "422", "messageId": "423", "endLine": 31, "endColumn": 36}, {"ruleId": "420", "severity": 1, "message": "436", "line": 33, "column": 33, "nodeType": "422", "messageId": "423", "endLine": 33, "endColumn": 40}, {"ruleId": "420", "severity": 1, "message": "437", "line": 33, "column": 42, "nodeType": "422", "messageId": "423", "endLine": 33, "endColumn": 47}, {"ruleId": "420", "severity": 1, "message": "467", "line": 1, "column": 17, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 20}, {"ruleId": "420", "severity": 1, "message": "438", "line": 9, "column": 18, "nodeType": "422", "messageId": "423", "endLine": 9, "endColumn": 25}, {"ruleId": "420", "severity": 1, "message": "436", "line": 20, "column": 20, "nodeType": "422", "messageId": "423", "endLine": 20, "endColumn": 27}, {"ruleId": "420", "severity": 1, "message": "437", "line": 20, "column": 29, "nodeType": "422", "messageId": "423", "endLine": 20, "endColumn": 34}, {"ruleId": "441", "severity": 1, "message": "455", "line": 23, "column": 23, "nodeType": "456", "endLine": 23, "endColumn": 24}, {"ruleId": "420", "severity": 1, "message": "476", "line": 1, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 1, "endColumn": 13}, {"ruleId": "420", "severity": 1, "message": "466", "line": 15, "column": 47, "nodeType": "422", "messageId": "423", "endLine": 15, "endColumn": 58}, {"ruleId": "420", "severity": 1, "message": "436", "line": 15, "column": 60, "nodeType": "422", "messageId": "423", "endLine": 15, "endColumn": 67}, {"ruleId": "420", "severity": 1, "message": "437", "line": 15, "column": 69, "nodeType": "422", "messageId": "423", "endLine": 15, "endColumn": 74}, {"ruleId": "420", "severity": 1, "message": "488", "line": 2, "column": 10, "nodeType": "422", "messageId": "423", "endLine": 2, "endColumn": 31}, "no-unused-vars", "'userId' is assigned a value but never used.", "Identifier", "unusedVar", "'Header' is defined but never used.", "'Footer' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "'set' is defined but never used.", "'imageUrl' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'useEffect' is defined but never used.", "'userApi' is defined but never used.", "'useState' is defined but never used.", "'current' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setSize' is assigned a value but never used.", "'getTransactionByUserID' is defined but never used.", "'response' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserBalance'. Either include it or remove the dependency array.", "ArrayExpression", ["489"], "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'Edit' is defined but never used.", "'Trash2' is defined but never used.", "'Search' is defined but never used.", "'Plus' is defined but never used.", "'filter' is defined but never used.", "'navigate' is assigned a value but never used.", "Assignments to the 'currentPage' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "Literal", "'deposits' is assigned a value but never used.", "'ChangePassword' is defined but never used.", "'TopUp' is defined but never used.", "'EditUserModal' is defined but never used.", "'selectedUser' is assigned a value but never used.", "'setSelectedUser' is assigned a value but never used.", "'showEdit' is assigned a value but never used.", "'setShowEdit' is assigned a value but never used.", "'totalElements' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'use' is defined but never used.", "'Deposit' is defined but never used.", "'Redeem' is defined but never used.", "'userProfile' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchBills', 'fetchTransaction', 'fetchUserBalance', and 'fetchUserById'. Either include them or remove the dependency array.", ["490"], "React Hook useEffect has a missing dependency: 'fetchTransaction'. Either include it or remove the dependency array.", ["491"], "'getBalance' is defined but never used.", "'get' is defined but never used.", "React Hook useEffect has a missing dependency: 'onChangeSearch'. Either include it or remove the dependency array. If 'onChangeSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["492"], "'UserApi' is defined but never used.", "'debounce' is defined but never used.", "'res' is assigned a value but never used.", "'search' is assigned a value but never used.", "'setSearch' is assigned a value but never used.", "'showDeleteConfirm' is assigned a value but never used.", "'setShowDeleteConfirm' is assigned a value but never used.", "'customerToDelete' is assigned a value but never used.", "'setCustomerToDelete' is assigned a value but never used.", "'getEmployeeByUsername' is defined but never used.", {"desc": "493", "fix": "494"}, {"desc": "495", "fix": "496"}, {"desc": "497", "fix": "498"}, {"desc": "499", "fix": "500"}, "Update the dependencies array to be: [fetchUserBalance]", {"range": "501", "text": "502"}, "Update the dependencies array to be: [balance, fetchBills, fetchTransaction, fetchUserBalance, fetchUserById]", {"range": "503", "text": "504"}, "Update the dependencies array to be: [params, startDate, endDate, fetchTransaction]", {"range": "505", "text": "506"}, "Update the dependencies array to be: [isReset, onChangeSearch]", {"range": "507", "text": "508"}, [3152, 3154], "[fetchUserBalance]", [4656, 4665], "[balance, fetchBills, fetchTransaction, fetchUserBalance, fetchUserById]", [4842, 4870], "[params, startDate, endDate, fetchTransaction]", [790, 799], "[isR<PERSON><PERSON>, onChangeSearch]"]