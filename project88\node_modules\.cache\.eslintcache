[{"C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\LoginPage.js": "5", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\HomePage.js": "6", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\FogotPassword.js": "7", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\ChangePassword.js": "8", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\Register.js": "9", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Profile.js": "10", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\registerSlice.js": "11", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileContent.js": "12", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationChangePassword.js": "13", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Footer.js": "14", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Form.js": "15", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\Validation.js": "16", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\RegisterService.js": "17", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Header.js": "18", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationLogin.js": "19", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserApi.js": "20", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\PaymentModal.js": "21", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationModal.js": "22", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\SuccessModal.js": "23", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\FailureModal.js": "24", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\DepositModal.js": "25", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationPopup.js": "26", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Test.js": "27", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\transactionSlice.js": "28", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Transaction.js": "29", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\MyDatePicker.js": "30", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\TransactionService.js": "31", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Table.js": "32", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\TopUp.js": "33", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\TransactionAPI.js": "34", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Redeem.js": "35", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Transfer.js": "36", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\userSlice.js": "37", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TransferForm.js": "38", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileService.js": "39", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserAPIv2.js": "40", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\axiosClient.js": "41", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\VerifyPage.js": "42", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\EmployeeList.js": "43", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\employeeSlice.js": "44", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\depositSlice.js": "45", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\SavingTotal.js": "46", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AuthAPI.js": "47", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\AdminContent.js": "48", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TranferUserBalance.js": "49", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserContent.js": "50", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserService.js": "51", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\DepositService.js": "52", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Deposit.js": "53", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Search.js": "54", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Pagination.js": "55", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Bills.js": "56", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\DepositAPI.js": "57", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AdminAPI.js": "58", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\EditUserModal.js": "59", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\EditUserValidatiion.js": "60", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\CreateEmployee.js": "61", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\utils\\auth.js": "62", "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\CreateEmployeeValidation.js": "63"}, {"size": 630, "mtime": 1747316253164, "results": "64", "hashOfConfig": "65"}, {"size": 2445, "mtime": 1751259017603, "results": "66", "hashOfConfig": "65"}, {"size": 375, "mtime": 1747026816582, "results": "67", "hashOfConfig": "65"}, {"size": 756, "mtime": 1750306694193, "results": "68", "hashOfConfig": "65"}, {"size": 1980, "mtime": 1751259017618, "results": "69", "hashOfConfig": "65"}, {"size": 1100, "mtime": 1751259017634, "results": "70", "hashOfConfig": "65"}, {"size": 2247, "mtime": 1751259017618, "results": "71", "hashOfConfig": "65"}, {"size": 1543, "mtime": 1749909139877, "results": "72", "hashOfConfig": "65"}, {"size": 1800, "mtime": 1751259017634, "results": "73", "hashOfConfig": "65"}, {"size": 363, "mtime": 1750306694168, "results": "74", "hashOfConfig": "65"}, {"size": 1479, "mtime": 1747316253167, "results": "75", "hashOfConfig": "65"}, {"size": 7091, "mtime": 1751259017634, "results": "76", "hashOfConfig": "65"}, {"size": 414, "mtime": 1747317593325, "results": "77", "hashOfConfig": "65"}, {"size": 153, "mtime": 1750306694127, "results": "78", "hashOfConfig": "65"}, {"size": 4637, "mtime": 1751259017603, "results": "79", "hashOfConfig": "65"}, {"size": 1835, "mtime": 1751259017650, "results": "80", "hashOfConfig": "65"}, {"size": 670, "mtime": 1749731390975, "results": "81", "hashOfConfig": "65"}, {"size": 5240, "mtime": 1751259017603, "results": "82", "hashOfConfig": "65"}, {"size": 243, "mtime": 1747124305813, "results": "83", "hashOfConfig": "65"}, {"size": 1173, "mtime": 1750306694119, "results": "84", "hashOfConfig": "65"}, {"size": 2542, "mtime": 1748951277658, "results": "85", "hashOfConfig": "65"}, {"size": 1042, "mtime": 1747485672539, "results": "86", "hashOfConfig": "65"}, {"size": 1717, "mtime": 1747796635496, "results": "87", "hashOfConfig": "65"}, {"size": 1308, "mtime": 1747486104191, "results": "88", "hashOfConfig": "65"}, {"size": 3826, "mtime": 1747796665260, "results": "89", "hashOfConfig": "65"}, {"size": 1495, "mtime": 1747796197314, "results": "90", "hashOfConfig": "65"}, {"size": 1026, "mtime": 1751259017618, "results": "91", "hashOfConfig": "65"}, {"size": 5095, "mtime": 1750306694187, "results": "92", "hashOfConfig": "65"}, {"size": 1210, "mtime": 1750306694173, "results": "93", "hashOfConfig": "65"}, {"size": 1195, "mtime": 1750306694134, "results": "94", "hashOfConfig": "65"}, {"size": 507, "mtime": 1750306694175, "results": "95", "hashOfConfig": "65"}, {"size": 6896, "mtime": 1750306694140, "results": "96", "hashOfConfig": "65"}, {"size": 2121, "mtime": 1749731390971, "results": "97", "hashOfConfig": "65"}, {"size": 516, "mtime": 1750306694116, "results": "98", "hashOfConfig": "65"}, {"size": 13552, "mtime": 1751259017634, "results": "99", "hashOfConfig": "65"}, {"size": 3643, "mtime": 1751259017634, "results": "100", "hashOfConfig": "65"}, {"size": 5586, "mtime": 1751259017650, "results": "101", "hashOfConfig": "65"}, {"size": 2186, "mtime": 1748834496576, "results": "102", "hashOfConfig": "65"}, {"size": 191, "mtime": 1748834496592, "results": "103", "hashOfConfig": "65"}, {"size": 717, "mtime": 1748951277651, "results": "104", "hashOfConfig": "65"}, {"size": 1157, "mtime": 1750306694122, "results": "105", "hashOfConfig": "65"}, {"size": 1292, "mtime": 1749731390952, "results": "106", "hashOfConfig": "65"}, {"size": 1273, "mtime": 1750306694152, "results": "107", "hashOfConfig": "65"}, {"size": 3351, "mtime": 1751259017650, "results": "108", "hashOfConfig": "65"}, {"size": 1526, "mtime": 1749650960789, "results": "109", "hashOfConfig": "65"}, {"size": 3741, "mtime": 1749731390981, "results": "110", "hashOfConfig": "65"}, {"size": 576, "mtime": 1749909139877, "results": "111", "hashOfConfig": "65"}, {"size": 4634, "mtime": 1751259017618, "results": "112", "hashOfConfig": "65"}, {"size": 924, "mtime": 1748951277665, "results": "113", "hashOfConfig": "65"}, {"size": 8854, "mtime": 1751259017634, "results": "114", "hashOfConfig": "65"}, {"size": 492, "mtime": 1750306694180, "results": "115", "hashOfConfig": "65"}, {"size": 419, "mtime": 1749650960782, "results": "116", "hashOfConfig": "65"}, {"size": 7200, "mtime": 1751259017634, "results": "117", "hashOfConfig": "65"}, {"size": 1876, "mtime": 1749731390939, "results": "118", "hashOfConfig": "65"}, {"size": 1455, "mtime": 1750306694137, "results": "119", "hashOfConfig": "65"}, {"size": 1555, "mtime": 1751259017634, "results": "120", "hashOfConfig": "65"}, {"size": 552, "mtime": 1749731390929, "results": "121", "hashOfConfig": "65"}, {"size": 265, "mtime": 1750306694110, "results": "122", "hashOfConfig": "65"}, {"size": 3086, "mtime": 1751259017603, "results": "123", "hashOfConfig": "65"}, {"size": 1036, "mtime": 1751259017650, "results": "124", "hashOfConfig": "65"}, {"size": 1486, "mtime": 1751259017618, "results": "125", "hashOfConfig": "65"}, {"size": 511, "mtime": 1751259017650, "results": "126", "hashOfConfig": "65"}, {"size": 1503, "mtime": 1751259017650, "results": "127", "hashOfConfig": "65"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hzh87o", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\LoginPage.js", ["317"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\HomePage.js", ["318", "319", "320"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\FogotPassword.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\ChangePassword.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\Register.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Profile.js", ["321", "322"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\registerSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileContent.js", ["323", "324", "325"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationChangePassword.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Form.js", ["326"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\Validation.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\register\\RegisterService.js", ["327"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\ValidationLogin.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserApi.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\PaymentModal.js", ["328"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\SuccessModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\FailureModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\DepositModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\ConfirmationPopup.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\Test.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\transactionSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Transaction.js", ["329", "330", "331", "332", "333"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\MyDatePicker.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\TransactionService.js", ["334"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Table.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\TopUp.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\TransactionAPI.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Redeem.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Transfer.js", ["335", "336"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\userSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TransferForm.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\ProfileService.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\UserAPIv2.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\axiosClient.js", ["337", "338"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\auth\\VerifyPage.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\EmployeeList.js", ["339", "340", "341", "342", "343", "344", "345", "346", "347", "348"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\employeeSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\redux\\slices\\depositSlice.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\SavingTotal.js", ["349", "350", "351"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AuthAPI.js", ["352"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\AdminContent.js", ["353", "354", "355", "356", "357", "358", "359", "360"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\TranferUserBalance.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserContent.js", ["361", "362", "363", "364", "365", "366", "367", "368"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\UserService.js", ["369"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\DepositService.js", ["370"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\user\\Deposit.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Search.js", ["371"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\home\\Bills.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\DepositAPI.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\api\\AdminAPI.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\components\\EditUserModal.js", ["372", "373", "374"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\EditUserValidatiion.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\features\\admin\\CreateEmployee.js", ["375"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\utils\\auth.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Project88_FE\\project88\\src\\validation\\CreateEmployeeValidation.js", [], [], {"ruleId": "376", "severity": 1, "message": "377", "line": 20, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 20, "endColumn": 19}, {"ruleId": "376", "severity": 1, "message": "380", "line": 2, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "381", "line": 3, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 3, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "382", "line": 19, "column": 11, "nodeType": "378", "messageId": "379", "endLine": 19, "endColumn": 22}, {"ruleId": "376", "severity": 1, "message": "380", "line": 2, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "381", "line": 3, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 3, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "383", "line": 4, "column": 18, "nodeType": "378", "messageId": "379", "endLine": 4, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "384", "line": 17, "column": 12, "nodeType": "378", "messageId": "379", "endLine": 17, "endColumn": 20}, {"ruleId": "385", "severity": 1, "message": "386", "line": 70, "column": 33, "nodeType": "387", "endLine": 70, "endColumn": 147}, {"ruleId": "376", "severity": 1, "message": "388", "line": 1, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 26}, {"ruleId": "376", "severity": 1, "message": "389", "line": 1, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 15}, {"ruleId": "376", "severity": 1, "message": "390", "line": 1, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 25}, {"ruleId": "376", "severity": 1, "message": "383", "line": 5, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 5, "endColumn": 13}, {"ruleId": "376", "severity": 1, "message": "391", "line": 6, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 6, "endColumn": 17}, {"ruleId": "376", "severity": 1, "message": "392", "line": 14, "column": 27, "nodeType": "378", "messageId": "379", "endLine": 14, "endColumn": 34}, {"ruleId": "376", "severity": 1, "message": "393", "line": 14, "column": 36, "nodeType": "378", "messageId": "379", "endLine": 14, "endColumn": 41}, {"ruleId": "376", "severity": 1, "message": "394", "line": 15, "column": 18, "nodeType": "378", "messageId": "379", "endLine": 15, "endColumn": 25}, {"ruleId": "376", "severity": 1, "message": "395", "line": 2, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 32}, {"ruleId": "376", "severity": 1, "message": "396", "line": 48, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 48, "endColumn": 21}, {"ruleId": "397", "severity": 1, "message": "398", "line": 110, "column": 6, "nodeType": "399", "endLine": 110, "endColumn": 8, "suggestions": "400"}, {"ruleId": "401", "severity": 1, "message": "402", "line": 30, "column": 28, "nodeType": "403", "messageId": "404", "endLine": 30, "endColumn": 30}, {"ruleId": "401", "severity": 1, "message": "402", "line": 30, "column": 61, "nodeType": "403", "messageId": "404", "endLine": 30, "endColumn": 63}, {"ruleId": "376", "severity": 1, "message": "390", "line": 1, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 25}, {"ruleId": "376", "severity": 1, "message": "405", "line": 2, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "406", "line": 2, "column": 16, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 22}, {"ruleId": "376", "severity": 1, "message": "407", "line": 2, "column": 24, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 30}, {"ruleId": "376", "severity": 1, "message": "408", "line": 2, "column": 32, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 36}, {"ruleId": "376", "severity": 1, "message": "409", "line": 6, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 6, "endColumn": 16}, {"ruleId": "376", "severity": 1, "message": "410", "line": 10, "column": 11, "nodeType": "378", "messageId": "379", "endLine": 10, "endColumn": 19}, {"ruleId": "376", "severity": 1, "message": "392", "line": 14, "column": 24, "nodeType": "378", "messageId": "379", "endLine": 14, "endColumn": 31}, {"ruleId": "376", "severity": 1, "message": "393", "line": 14, "column": 33, "nodeType": "378", "messageId": "379", "endLine": 14, "endColumn": 38}, {"ruleId": "397", "severity": 1, "message": "411", "line": 20, "column": 23, "nodeType": "412", "endLine": 20, "endColumn": 24}, {"ruleId": "376", "severity": 1, "message": "413", "line": 9, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 9, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "392", "line": 9, "column": 23, "nodeType": "378", "messageId": "379", "endLine": 9, "endColumn": 30}, {"ruleId": "376", "severity": 1, "message": "393", "line": 9, "column": 32, "nodeType": "378", "messageId": "379", "endLine": 9, "endColumn": 37}, {"ruleId": "376", "severity": 1, "message": "414", "line": 1, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 22}, {"ruleId": "376", "severity": 1, "message": "415", "line": 2, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 13}, {"ruleId": "376", "severity": 1, "message": "416", "line": 7, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 7, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "417", "line": 22, "column": 12, "nodeType": "378", "messageId": "379", "endLine": 22, "endColumn": 24}, {"ruleId": "376", "severity": 1, "message": "418", "line": 22, "column": 26, "nodeType": "378", "messageId": "379", "endLine": 22, "endColumn": 41}, {"ruleId": "376", "severity": 1, "message": "419", "line": 23, "column": 12, "nodeType": "378", "messageId": "379", "endLine": 23, "endColumn": 20}, {"ruleId": "376", "severity": 1, "message": "420", "line": 23, "column": 22, "nodeType": "378", "messageId": "379", "endLine": 23, "endColumn": 33}, {"ruleId": "376", "severity": 1, "message": "421", "line": 27, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 27, "endColumn": 26}, {"ruleId": "376", "severity": 1, "message": "422", "line": 27, "column": 40, "nodeType": "378", "messageId": "379", "endLine": 27, "endColumn": 51}, {"ruleId": "376", "severity": 1, "message": "423", "line": 1, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 20}, {"ruleId": "376", "severity": 1, "message": "424", "line": 4, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 4, "endColumn": 15}, {"ruleId": "376", "severity": 1, "message": "425", "line": 5, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 5, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "426", "line": 15, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 15, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "422", "line": 57, "column": 23, "nodeType": "378", "messageId": "379", "endLine": 57, "endColumn": 34}, {"ruleId": "376", "severity": 1, "message": "396", "line": 108, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 108, "endColumn": 21}, {"ruleId": "397", "severity": 1, "message": "427", "line": 150, "column": 6, "nodeType": "399", "endLine": 150, "endColumn": 15, "suggestions": "428"}, {"ruleId": "397", "severity": 1, "message": "429", "line": 156, "column": 6, "nodeType": "399", "endLine": 156, "endColumn": 34, "suggestions": "430"}, {"ruleId": "376", "severity": 1, "message": "431", "line": 2, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 2, "endColumn": 20}, {"ruleId": "376", "severity": 1, "message": "432", "line": 1, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 13}, {"ruleId": "397", "severity": 1, "message": "433", "line": 29, "column": 8, "nodeType": "399", "endLine": 29, "endColumn": 17, "suggestions": "434"}, {"ruleId": "376", "severity": 1, "message": "390", "line": 1, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 1, "endColumn": 25}, {"ruleId": "376", "severity": 1, "message": "435", "line": 4, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 4, "endColumn": 15}, {"ruleId": "376", "severity": 1, "message": "436", "line": 5, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 5, "endColumn": 18}, {"ruleId": "376", "severity": 1, "message": "437", "line": 16, "column": 19, "nodeType": "378", "messageId": "379", "endLine": 16, "endColumn": 22}, "no-unused-vars", "'userId' is assigned a value but never used.", "Identifier", "unusedVar", "'Header' is defined but never used.", "'Footer' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "'set' is defined but never used.", "'imageUrl' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'useEffect' is defined but never used.", "'userApi' is defined but never used.", "'useState' is defined but never used.", "'current' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setSize' is assigned a value but never used.", "'getTransactionByUserID' is defined but never used.", "'response' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserBalance'. Either include it or remove the dependency array.", "ArrayExpression", ["438"], "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'Edit' is defined but never used.", "'Trash2' is defined but never used.", "'Search' is defined but never used.", "'Plus' is defined but never used.", "'filter' is defined but never used.", "'navigate' is assigned a value but never used.", "Assignments to the 'currentPage' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "Literal", "'deposits' is assigned a value but never used.", "'ChangePassword' is defined but never used.", "'TopUp' is defined but never used.", "'EditUserModal' is defined but never used.", "'selectedUser' is assigned a value but never used.", "'setSelectedUser' is assigned a value but never used.", "'showEdit' is assigned a value but never used.", "'setShowEdit' is assigned a value but never used.", "'totalElements' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'use' is defined but never used.", "'Deposit' is defined but never used.", "'Redeem' is defined but never used.", "'userProfile' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchBills', 'fetchTransaction', 'fetchUserBalance', and 'fetchUserById'. Either include them or remove the dependency array.", ["439"], "React Hook useEffect has a missing dependency: 'fetchTransaction'. Either include it or remove the dependency array.", ["440"], "'getBalance' is defined but never used.", "'get' is defined but never used.", "React Hook useEffect has a missing dependency: 'onChangeSearch'. Either include it or remove the dependency array. If 'onChangeSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["441"], "'UserApi' is defined but never used.", "'debounce' is defined but never used.", "'res' is assigned a value but never used.", {"desc": "442", "fix": "443"}, {"desc": "444", "fix": "445"}, {"desc": "446", "fix": "447"}, {"desc": "448", "fix": "449"}, "Update the dependencies array to be: [fetchUserBalance]", {"range": "450", "text": "451"}, "Update the dependencies array to be: [balance, fetchBills, fetchTransaction, fetchUserBalance, fetchUserById]", {"range": "452", "text": "453"}, "Update the dependencies array to be: [params, startDate, endDate, fetchTransaction]", {"range": "454", "text": "455"}, "Update the dependencies array to be: [isReset, onChangeSearch]", {"range": "456", "text": "457"}, [3152, 3154], "[fetchUserBalance]", [4656, 4665], "[balance, fetchBills, fetchTransaction, fetchUserBalance, fetchUserById]", [4842, 4870], "[params, startDate, endDate, fetchTransaction]", [790, 799], "[isR<PERSON><PERSON>, onChangeSearch]"]