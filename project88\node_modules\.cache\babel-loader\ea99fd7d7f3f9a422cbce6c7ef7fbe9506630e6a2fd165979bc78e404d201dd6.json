{"ast": null, "code": "import { z } from \"zod\";\nimport UserApi from \"../api/UserApi\";\nexport const CreateEmployeeValidation = z.object({\n  firstName: z.string().nonempty(\"H<PERSON> phải bắt buộc\"),\n  lastName: z.string().nonempty(\"Tên phải bắt buộc\"),\n  username: z.string().min(6, \"Tên tài khoản phải có ít nhất 6 ký tự\"),\n  email: z.string().nonempty(\"Email không được để trống\").email(\"Không đúng định dạng email\").refine(async value => {\n    const res = await UserApi.isExistEmail(value);\n    return !res.data;\n  }, {\n    message: \"Email đã tồn tại\"\n  }),\n  cccd: z.string().nonempty({\n    message: \"CCCD không được để trống\"\n  }).regex(/^\\d{12}$/, {\n    message: \"CCCD phải gồm đúng 12 chữ số\"\n  }),\n  gender: z.enum(['Male', 'Female', 'Other'], {\n    errorMap: () => ({\n      message: '<PERSON>ui lòng chọn giới tính'\n    })\n  }),\n  birth: z.string().refine(val => !isNaN(Date.parse(val)), {\n    message: \"Ngày không hợp lệ\"\n  }),\n  phone: z.string().nonempty({\n    message: \"Số điện thoại không được để trống\"\n  }).regex(/^\\d{10}$/, {\n    message: \"Số điện thoại phải gồm đúng 10 chữ số\"\n  }).refine(async value => {\n    const rs = await UserApi.isExistPhone(value);\n    return !rs.data;\n  }, {\n    message: \"SDT đã tồn tại\"\n  })\n});", "map": {"version": 3, "names": ["z", "UserApi", "CreateEmployeeValidation", "object", "firstName", "string", "nonempty", "lastName", "username", "min", "email", "refine", "value", "res", "isExistEmail", "data", "message", "cccd", "regex", "gender", "enum", "errorMap", "birth", "val", "isNaN", "Date", "parse", "phone", "rs", "isExistPhone"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/validation/CreateEmployeeValidation.js"], "sourcesContent": ["import { z } from \"zod\";\r\nimport UserApi from \"../api/UserApi\";\r\n\r\nexport const CreateEmployeeValidation = z.object({\r\n    firstName: z.string().nonempty(\"H<PERSON> phải bắt buộc\"),\r\n    lastName: z.string().nonempty(\"Tên phải bắt buộc\"),\r\n    username: z.string().min(6, \"Tên tài khoản phải có ít nhất 6 ký tự\"),\r\n    email: z.string().nonempty(\"Email không được để trống\").email(\"Không đúng định dạng email\")\r\n        .refine(async (value) => {\r\n            const res = await UserApi.isExistEmail(value);\r\n            return !res.data;\r\n        }, {\r\n            message: \"Email đã tồn tại\"\r\n        }),\r\n    cccd: z\r\n        .string()\r\n        .nonempty({ message: \"CCCD không được để trống\" })\r\n        .regex(/^\\d{12}$/, { message: \"CC<PERSON> phải gồm đúng 12 chữ số\" }),\r\n    gender: z.enum(['Male', 'Female', 'Other'], {\r\n        errorMap: () => ({ message: '<PERSON><PERSON> lòng chọn giới tính' })\r\n    }),\r\n    birth: z.string().refine(val => !isNaN(Date.parse(val)), {\r\n        message: \"Ngày không hợp lệ\"\r\n    }),\r\n    phone: z\r\n        .string()\r\n        .nonempty({ message: \"Số điện thoại không được để trống\" })\r\n        .regex(/^\\d{10}$/, { message: \"Số điện thoại phải gồm đúng 10 chữ số\" })\r\n        .refine(async (value) => {\r\n            const rs = await UserApi.isExistPhone(value);\r\n            return !rs.data\r\n        }, {\r\n            message: \"SDT đã tồn tại\"\r\n        }),\r\n})"], "mappings": "AAAA,SAASA,CAAC,QAAQ,KAAK;AACvB,OAAOC,OAAO,MAAM,gBAAgB;AAEpC,OAAO,MAAMC,wBAAwB,GAAGF,CAAC,CAACG,MAAM,CAAC;EAC7CC,SAAS,EAAEJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CAAC;EAClDC,QAAQ,EAAEP,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,mBAAmB,CAAC;EAClDE,QAAQ,EAAER,CAAC,CAACK,MAAM,CAAC,CAAC,CAACI,GAAG,CAAC,CAAC,EAAE,uCAAuC,CAAC;EACpEC,KAAK,EAAEV,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,2BAA2B,CAAC,CAACI,KAAK,CAAC,4BAA4B,CAAC,CACtFC,MAAM,CAAC,MAAOC,KAAK,IAAK;IACrB,MAAMC,GAAG,GAAG,MAAMZ,OAAO,CAACa,YAAY,CAACF,KAAK,CAAC;IAC7C,OAAO,CAACC,GAAG,CAACE,IAAI;EACpB,CAAC,EAAE;IACCC,OAAO,EAAE;EACb,CAAC,CAAC;EACNC,IAAI,EAAEjB,CAAC,CACFK,MAAM,CAAC,CAAC,CACRC,QAAQ,CAAC;IAAEU,OAAO,EAAE;EAA2B,CAAC,CAAC,CACjDE,KAAK,CAAC,UAAU,EAAE;IAAEF,OAAO,EAAE;EAA+B,CAAC,CAAC;EACnEG,MAAM,EAAEnB,CAAC,CAACoB,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE;IACxCC,QAAQ,EAAEA,CAAA,MAAO;MAAEL,OAAO,EAAE;IAA0B,CAAC;EAC3D,CAAC,CAAC;EACFM,KAAK,EAAEtB,CAAC,CAACK,MAAM,CAAC,CAAC,CAACM,MAAM,CAACY,GAAG,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,CAAC,EAAE;IACrDP,OAAO,EAAE;EACb,CAAC,CAAC;EACFW,KAAK,EAAE3B,CAAC,CACHK,MAAM,CAAC,CAAC,CACRC,QAAQ,CAAC;IAAEU,OAAO,EAAE;EAAoC,CAAC,CAAC,CAC1DE,KAAK,CAAC,UAAU,EAAE;IAAEF,OAAO,EAAE;EAAwC,CAAC,CAAC,CACvEL,MAAM,CAAC,MAAOC,KAAK,IAAK;IACrB,MAAMgB,EAAE,GAAG,MAAM3B,OAAO,CAAC4B,YAAY,CAACjB,KAAK,CAAC;IAC5C,OAAO,CAACgB,EAAE,CAACb,IAAI;EACnB,CAAC,EAAE;IACCC,OAAO,EAAE;EACb,CAAC;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}