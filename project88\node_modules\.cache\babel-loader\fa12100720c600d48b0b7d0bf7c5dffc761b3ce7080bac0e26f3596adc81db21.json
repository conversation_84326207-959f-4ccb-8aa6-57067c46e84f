{"ast": null, "code": "// src/index.ts\nfunction createThunkMiddleware(extraArgument) {\n  const middleware = _ref => {\n    let {\n      dispatch,\n      getState\n    } = _ref;\n    return next => action => {\n      if (typeof action === \"function\") {\n        return action(dispatch, getState, extraArgument);\n      }\n      return next(action);\n    };\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware();\nvar withExtraArgument = createThunkMiddleware;\nexport { thunk, withExtraArgument };", "map": {"version": 3, "names": ["createThunkMiddleware", "extraArgument", "middleware", "_ref", "dispatch", "getState", "next", "action", "thunk", "withExtraArgument"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/redux-thunk/dist/redux-thunk.mjs"], "sourcesContent": ["// src/index.ts\nfunction createThunkMiddleware(extraArgument) {\n  const middleware = ({ dispatch, getState }) => (next) => (action) => {\n    if (typeof action === \"function\") {\n      return action(dispatch, getState, extraArgument);\n    }\n    return next(action);\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware();\nvar withExtraArgument = createThunkMiddleware;\nexport {\n  thunk,\n  withExtraArgument\n};\n"], "mappings": "AAAA;AACA,SAASA,qBAAqBA,CAACC,aAAa,EAAE;EAC5C,MAAMC,UAAU,GAAGC,IAAA;IAAA,IAAC;MAAEC,QAAQ;MAAEC;IAAS,CAAC,GAAAF,IAAA;IAAA,OAAMG,IAAI,IAAMC,MAAM,IAAK;MACnE,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;QAChC,OAAOA,MAAM,CAACH,QAAQ,EAAEC,QAAQ,EAAEJ,aAAa,CAAC;MAClD;MACA,OAAOK,IAAI,CAACC,MAAM,CAAC;IACrB,CAAC;EAAA;EACD,OAAOL,UAAU;AACnB;AACA,IAAIM,KAAK,GAAGR,qBAAqB,CAAC,CAAC;AACnC,IAAIS,iBAAiB,GAAGT,qBAAqB;AAC7C,SACEQ,KAAK,EACLC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}