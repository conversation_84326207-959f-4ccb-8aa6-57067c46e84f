{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\user\\\\UserContent.js\",\n  _s = $RefreshSig$();\nimport React, { use, useEffect, useState } from \"react\";\nimport Transaction from \"./Transaction\";\nimport Transfer from \"../home/<USER>\";\nimport Deposit from \"./Deposit\";\nimport Redeem from \"../home/<USER>\";\nimport UserAPIv2 from \"../../api/UserAPIv2\";\nimport Bills from \"../home/<USER>\";\nimport { transaction } from \"../../redux/slices/transactionSlice\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport Search from \"../../components/Search\";\nimport MyDatePicker from \"../../components/MyDatePicker\";\nimport Pagination from \"../../components/Pagination\";\nimport SavingTotal from \"./SavingTotal\";\nimport { useNavigate } from \"react-router-dom\";\nimport { userProfile } from \"../../redux/slices/userSlice\";\nimport { getEmployeeByUsername } from \"../../redux/slices/employeeSlice\";\nimport { getUserId } from \"../../utils/auth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserContent = () => {\n  _s();\n  const userID = getUserId();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const handleNameClick = () => {\n    navigate(\"/profile\");\n  };\n  const [user, setUser] = useState({\n    firstName: \"\",\n    lastName: \"\",\n    fullname: \"\",\n    username: \"\",\n    balance: null\n  });\n  const [showTransfer, setShowTransfer] = useState(false);\n  // const [showDeposit, setShowDeposit] = useState(false);\n  // const [showRedeem, setShowRedeem] = useState(false);\n  const [balance, setBalance] = useState(0);\n  const [bills, setBills] = useState([]);\n  // const [savingsTotal, setSavingsTotal] = useState(0);\n\n  // State để lưu trữ các tham số tìm kiếm\n  const [params, setParams] = useState(\"\");\n  const [startDate, setStartDate] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [isReset, setIsReset] = useState(false);\n\n  // Ngày bắt đầu và kết thúc cho ngày hôm nay\n  const today = new Date();\n  const startToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n  const endToday = new Date(today);\n  endToday.setHours(23, 59, 59, 999);\n\n  // Lấy thông tin tổng số trang, tổng số phần tử và trang hiện tại từ Redux store\n  const {\n    totalPages,\n    currentPage\n  } = useSelector(state => state.transaction);\n  const [page, setPage] = useState(1);\n  useEffect(() => {\n    dispatch(getEmployeeByUsername());\n  }, [dispatch]);\n  const {\n    employeeByUsername\n  } = useSelector(state => state.employee);\n  const fetchUserById = async () => {\n    try {\n      const response = await UserAPIv2.FindUserById(userID);\n      if (response && response.data) {\n        setUser({\n          firstName: response.data.firstName,\n          lastName: response.data.lastName,\n          fullname: `${response.data.lastName} ${response.data.firstName}`,\n          username: response.data.username,\n          balance: response.data.balance\n        });\n      }\n    } catch (error) {\n      console.error(\"Error fetching user by ID:\", error);\n    }\n  };\n  const fetchUserBalance = async () => {\n    try {\n      const response = await UserAPIv2.FindUserById(userID);\n      if (response && response.data) {\n        setBalance(response.data.balance);\n      }\n    } catch (error) {\n      console.error(\"Error fetching user balance:\", error);\n    }\n  };\n  const fetchBills = async () => {\n    try {\n      const response = await UserAPIv2.GetBillsByUserId(userID);\n      if (response && response.data) {\n        setBills(response.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching bills:\", error);\n    }\n  };\n  const fetchPayBill = async billId => {\n    try {\n      const response = await UserAPIv2.PayBill(billId);\n      alert(\"Thanh toán thành công!\");\n      fetchBills();\n      fetchUserBalance();\n      fetchTransaction();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      alert(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Error paying bill\");\n      console.error(\"Error paying bill:\", error);\n    }\n  };\n  const fetchTransaction = async (pageNumber = page) => {\n    try {\n      const response = await dispatch(transaction({\n        page: pageNumber,\n        size: 5,\n        filter: {\n          startDate,\n          endDate,\n          name: params\n        }\n      }));\n      if (response && response.data) {}\n    } catch (error) {\n      console.error(\"Error fetching transactions:\", error);\n    }\n  };\n  const handleAfterTransfer = () => {\n    fetchTransaction();\n    fetchUserBalance();\n  };\n\n  // Callback sau khi gửi tiết kiệm hoặc tất toán\n  const handleAfterDepositAction = () => {\n    fetchUserBalance();\n    fetchTransaction();\n  };\n  useEffect(() => {\n    fetchUserById();\n    fetchUserBalance();\n    fetchBills();\n    fetchTransaction();\n  }, [balance]);\n\n  // về trang 1 khi thay đổi tham số tìm kiếm\n  useEffect(() => {\n    setPage(1);\n    fetchTransaction(1); // Gọi luôn trang 1 khi đổi params, startDate, endDate\n  }, [params, startDate, endDate]);\n  const handleResetTable = () => {\n    setIsReset(true);\n    setParams(\"\");\n    setStartDate(null);\n    setEndDate(null);\n    setPage(1);\n    setTimeout(() => setIsReset(false), 0);\n  };\n  const onPageChange = currentPage => {\n    setPage(currentPage);\n  };\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    className: \"flex-1 p-8 flex flex-col lg:flex-row gap-8 bg-gray-100 h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center text-xl font-bold text-red-600\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: employeeByUsername.avatarUrl,\n              alt: \"Avatar\",\n              className: \"w-full h-full rounded-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold cursor-pointer hover:text-red-600\",\n              onClick: handleNameClick,\n              children: user.fullname\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex w-full mt-5 justify-between space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 text-center bg-white p-6 rounded shadow\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-500 \",\n                children: \"S\\u1ED1 d\\u01B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold\",\n                children: [Number(balance).toLocaleString(\"de-DE\"), \" VND\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between space-x-4 mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full px-4 py-2 bg-red-100 text-red-600 rounded hover:bg-red-200\",\n                  onClick: () => setShowTransfer(true),\n                  children: \"Chuy\\u1EC3n kho\\u1EA3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SavingTotal, {\n              onAfterDeposit: handleAfterDepositAction\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-bold mb-4\",\n          children: \"L\\u1ECBch s\\u1EED giao d\\u1ECBch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-500\",\n              children: \"T\\u1EEB ng\\xE0y:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MyDatePicker, {\n              value: startDate,\n              onChange: date => {\n                setStartDate(date);\n              },\n              typeDate: startToday\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-500\",\n              children: \"\\u0111\\u1EBFn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MyDatePicker, {\n              value: endDate,\n              onChange: date => setEndDate(date),\n              typeDate: endToday\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Search, {\n            onChangeSearch: setParams,\n            isReset: isReset\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Transaction, {\n          params: params,\n          startDate: startDate,\n          endDate: endDate,\n          currentPage: page\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-4 py-2 bg-gray-100 bg-red-100 text-red-600 rounded hover:bg-red-200\",\n            onClick: handleResetTable,\n            children: \"T\\u1EA3i l\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n            totalPages: totalPages,\n            currentPage: page,\n            onPageChange: onPageChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Bills, {\n      data: bills,\n      balance: balance,\n      fetchPayBill: fetchPayBill\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), showTransfer && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full p-6\",\n        children: /*#__PURE__*/_jsxDEV(Transfer, {\n          setShowTransfer: setShowTransfer,\n          onAfterTransfer: handleAfterTransfer\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(UserContent, \"bUfNDU79WvEpj3/qXnSb78g5RrM=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector];\n});\n_c = UserContent;\nexport default UserContent;\nvar _c;\n$RefreshReg$(_c, \"UserContent\");", "map": {"version": 3, "names": ["React", "use", "useEffect", "useState", "Transaction", "Transfer", "<PERSON><PERSON><PERSON><PERSON>", "Redeem", "UserAPIv2", "Bills", "transaction", "useDispatch", "useSelector", "Search", "MyDatePicker", "Pagination", "SavingTotal", "useNavigate", "userProfile", "getEmployeeByUsername", "getUserId", "jsxDEV", "_jsxDEV", "UserContent", "_s", "userID", "dispatch", "navigate", "handleNameClick", "user", "setUser", "firstName", "lastName", "fullname", "username", "balance", "showTransfer", "setShowTransfer", "setBalance", "bills", "setBills", "params", "setParams", "startDate", "setStartDate", "endDate", "setEndDate", "isReset", "setIsReset", "today", "Date", "startToday", "getFullYear", "getMonth", "getDate", "endToday", "setHours", "totalPages", "currentPage", "state", "page", "setPage", "employeeByUsername", "employee", "fetchUserById", "response", "FindUserById", "data", "error", "console", "fetchUserBalance", "fetchBills", "GetBillsByUserId", "fetchPayBill", "billId", "PayBill", "alert", "fetchTransaction", "_error$response", "_error$response$data", "message", "pageNumber", "size", "filter", "name", "handleAfterTransfer", "handleAfterDepositAction", "handleResetTable", "setTimeout", "onPageChange", "className", "children", "src", "avatarUrl", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "Number", "toLocaleString", "onAfterDeposit", "value", "onChange", "date", "typeDate", "onChangeSearch", "onAfterTransfer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/user/UserContent.js"], "sourcesContent": ["import React, { use, useEffect, useState } from \"react\";\r\nimport Transaction from \"./Transaction\";\r\nimport Transfer from \"../home/<USER>\";\r\nimport Deposit from \"./Deposit\";\r\nimport Redeem from \"../home/<USER>\";\r\nimport UserAPIv2 from \"../../api/UserAPIv2\";\r\nimport Bills from \"../home/<USER>\";\r\nimport { transaction } from \"../../redux/slices/transactionSlice\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport Search from \"../../components/Search\";\r\nimport MyDatePicker from \"../../components/MyDatePicker\";\r\nimport Pagination from \"../../components/Pagination\";\r\nimport SavingTotal from \"./SavingTotal\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { userProfile } from \"../../redux/slices/userSlice\";\r\nimport { getEmployeeByUsername } from \"../../redux/slices/employeeSlice\";\r\nimport { getUserId } from \"../../utils/auth\";\r\n\r\nconst UserContent = () => {\r\n  const userID = getUserId();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n\r\n  const handleNameClick = () => {\r\n    navigate(\"/profile\");\r\n  };\r\n\r\n  const [user, setUser] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    fullname: \"\",\r\n    username: \"\",\r\n    balance: null\r\n  });\r\n\r\n  const [showTransfer, setShowTransfer] = useState(false);\r\n  // const [showDeposit, setShowDeposit] = useState(false);\r\n  // const [showRedeem, setShowRedeem] = useState(false);\r\n  const [balance, setBalance] = useState(0);\r\n  const [bills, setBills] = useState([]);\r\n  // const [savingsTotal, setSavingsTotal] = useState(0);\r\n\r\n  // State để lưu trữ các tham số tìm kiếm\r\n  const [params, setParams] = useState(\"\")\r\n  const [startDate, setStartDate] = useState(\"\");\r\n  const [endDate, setEndDate] = useState(\"\");\r\n  const [isReset, setIsReset] = useState(false);\r\n\r\n  // Ngày bắt đầu và kết thúc cho ngày hôm nay\r\n  const today = new Date();\r\n  const startToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());\r\n\r\n  const endToday = new Date(today);\r\n  endToday.setHours(23, 59, 59, 999);\r\n\r\n  // Lấy thông tin tổng số trang, tổng số phần tử và trang hiện tại từ Redux store\r\n  const { totalPages, currentPage } = useSelector((state) => state.transaction);\r\n  const [page, setPage] = useState(1);\r\n\r\n  useEffect(() => {\r\n    dispatch(getEmployeeByUsername());\r\n  }, [dispatch])\r\n\r\n  const { employeeByUsername } = useSelector((state) => state.employee);\r\n\r\n\r\n  const fetchUserById = async () => {\r\n    try {\r\n      const response = await UserAPIv2.FindUserById(userID);\r\n      if (response && response.data) {\r\n        setUser({\r\n          firstName: response.data.firstName,\r\n          lastName: response.data.lastName,\r\n          fullname: `${response.data.lastName} ${response.data.firstName}`,\r\n          username: response.data.username,\r\n          balance: response.data.balance\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching user by ID:\", error);\r\n    }\r\n  }\r\n\r\n  const fetchUserBalance = async () => {\r\n    try {\r\n      const response = await UserAPIv2.FindUserById(userID);\r\n      if (response && response.data) {\r\n        setBalance(response.data.balance);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching user balance:\", error);\r\n    }\r\n  }\r\n\r\n  const fetchBills = async () => {\r\n    try {\r\n      const response = await UserAPIv2.GetBillsByUserId(userID);\r\n      if (response && response.data) {\r\n        setBills(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching bills:\", error);\r\n    }\r\n  }\r\n\r\n  const fetchPayBill = async (billId) => {\r\n    try {\r\n      const response = await UserAPIv2.PayBill(billId);\r\n      alert(\"Thanh toán thành công!\");\r\n      fetchBills();\r\n      fetchUserBalance();\r\n      fetchTransaction();\r\n    }\r\n    catch (error) {\r\n      alert(error.response?.data?.message || \"Error paying bill\");\r\n      console.error(\"Error paying bill:\", error);\r\n    }\r\n  }\r\n\r\n  const fetchTransaction = async (pageNumber = page) => {\r\n    try {\r\n      const response = await dispatch(transaction({\r\n        page: pageNumber,\r\n        size: 5,\r\n        filter: { startDate, endDate, name: params }\r\n      }));\r\n      if (response && response.data) {\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching transactions:\", error);\r\n    }\r\n  }\r\n\r\n  const handleAfterTransfer = () => {\r\n    fetchTransaction()\r\n    fetchUserBalance();\r\n  };\r\n\r\n  // Callback sau khi gửi tiết kiệm hoặc tất toán\r\n  const handleAfterDepositAction = () => {\r\n    fetchUserBalance();\r\n    fetchTransaction();\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchUserById();\r\n    fetchUserBalance();\r\n    fetchBills();\r\n    fetchTransaction();\r\n  }, [balance]);\r\n\r\n  // về trang 1 khi thay đổi tham số tìm kiếm\r\n  useEffect(() => {\r\n    setPage(1);\r\n    fetchTransaction(1); // Gọi luôn trang 1 khi đổi params, startDate, endDate\r\n  }, [params, startDate, endDate]);\r\n\r\n\r\n\r\n  const handleResetTable = () => {\r\n    setIsReset(true);\r\n    setParams(\"\");\r\n    setStartDate(null);\r\n    setEndDate(null);\r\n    setPage(1);\r\n    setTimeout(() => setIsReset(false), 0);\r\n  }\r\n\r\n\r\n\r\n  const onPageChange = (currentPage) => {\r\n    setPage(currentPage);\r\n  }\r\n\r\n  return (\r\n    <main className=\"flex-1 p-8 flex flex-col lg:flex-row gap-8 bg-gray-100 h-screen\">\r\n      {/* Left Section */}\r\n      <div className=\"flex-1 space-y-8\">\r\n        {/* User Info */}\r\n        <div className=\"bg-white p-6 rounded shadow\">\r\n          <div className=\"flex items-center space-x-4 \">\r\n            <div className=\"w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center text-xl font-bold text-red-600\">\r\n              <img src={employeeByUsername.avatarUrl} alt=\"Avatar\" className=\"w-full h-full rounded-full object-cover\" />\r\n            </div>\r\n            <div>\r\n              <h2 className=\"text-xl font-bold cursor-pointer hover:text-red-600\"\r\n                onClick={handleNameClick}>\r\n                {user.fullname}\r\n              </h2>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex w-full mt-5 justify-between space-x-4\">\r\n              <div className=\"flex-1 text-center bg-white p-6 rounded shadow\">\r\n                <div className=\"text-gray-500 \">Số dư</div>\r\n                <div className=\"text-2xl font-bold\">\r\n                  {Number(balance).toLocaleString(\"de-DE\")} VND\r\n                </div>\r\n                <div className=\"flex justify-between space-x-4 mt-2\">\r\n                  <button className=\"w-full px-4 py-2 bg-red-100 text-red-600 rounded hover:bg-red-200\"\r\n                    onClick={() => setShowTransfer(true)}>\r\n                    Chuyển khoản\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <SavingTotal onAfterDeposit={handleAfterDepositAction} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Transaction History */}\r\n        <div className=\"bg-white p-6 rounded shadow\">\r\n          <h3 className=\"text-lg font-bold mb-4\">Lịch sử giao dịch</h3>\r\n          <div className=\"flex justify-between mb-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <span className=\"text-gray-500\">Từ ngày:</span>\r\n              <MyDatePicker value={startDate} onChange={(date) => { setStartDate(date) }} typeDate={startToday}></MyDatePicker>\r\n              <span className=\"text-gray-500\">đến</span>\r\n              <MyDatePicker value={endDate} onChange={(date) => setEndDate(date)} typeDate={endToday}></MyDatePicker>\r\n            </div>\r\n            <Search onChangeSearch={setParams} isReset={isReset}></Search>\r\n          </div>\r\n          <Transaction params={params} startDate={startDate} endDate={endDate} currentPage={page}></Transaction>\r\n\r\n          {/* Page */}\r\n          <div className=\"flex justify-between items-center mt-4\">\r\n            <button className=\"px-4 py-2 bg-gray-100 bg-red-100 text-red-600 rounded hover:bg-red-200\" onClick={handleResetTable}>\r\n              Tải lại\r\n            </button>\r\n            <Pagination totalPages={totalPages} currentPage={page} onPageChange={onPageChange} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Thanh toán hóa đơn */}\r\n      <Bills\r\n        data={bills}\r\n        balance={balance}\r\n        fetchPayBill={fetchPayBill}\r\n      />\r\n\r\n      {/* Transfer Modal */}\r\n      {showTransfer && (\r\n        <div className=\"fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50\">\r\n          <div className=\"w-full p-6\">\r\n            <Transfer\r\n              setShowTransfer={setShowTransfer}\r\n              onAfterTransfer={handleAfterTransfer}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n\r\n    </main>\r\n  );\r\n};\r\nexport default UserContent;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACvD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,qBAAqB,QAAQ,kCAAkC;AACxE,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,MAAM,GAAGL,SAAS,CAAC,CAAC;EAC1B,MAAMM,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,eAAe,GAAGA,CAAA,KAAM;IAC5BD,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC;IAC/B4B,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA;EACA,MAAM,CAACgC,OAAO,EAAEG,UAAU,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC;;EAEA;EACA,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM8C,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;EACxB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,KAAK,CAACG,WAAW,CAAC,CAAC,EAAEH,KAAK,CAACI,QAAQ,CAAC,CAAC,EAAEJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;EAEnF,MAAMC,QAAQ,GAAG,IAAIL,IAAI,CAACD,KAAK,CAAC;EAChCM,QAAQ,CAACC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;;EAElC;EACA,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAG9C,WAAW,CAAE+C,KAAK,IAAKA,KAAK,CAACjD,WAAW,CAAC;EAC7E,MAAM,CAACkD,IAAI,EAAEC,OAAO,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACdwB,QAAQ,CAACP,qBAAqB,CAAC,CAAC,CAAC;EACnC,CAAC,EAAE,CAACO,QAAQ,CAAC,CAAC;EAEd,MAAM;IAAEoC;EAAmB,CAAC,GAAGlD,WAAW,CAAE+C,KAAK,IAAKA,KAAK,CAACI,QAAQ,CAAC;EAGrE,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMzD,SAAS,CAAC0D,YAAY,CAACzC,MAAM,CAAC;MACrD,IAAIwC,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;QAC7BrC,OAAO,CAAC;UACNC,SAAS,EAAEkC,QAAQ,CAACE,IAAI,CAACpC,SAAS;UAClCC,QAAQ,EAAEiC,QAAQ,CAACE,IAAI,CAACnC,QAAQ;UAChCC,QAAQ,EAAE,GAAGgC,QAAQ,CAACE,IAAI,CAACnC,QAAQ,IAAIiC,QAAQ,CAACE,IAAI,CAACpC,SAAS,EAAE;UAChEG,QAAQ,EAAE+B,QAAQ,CAACE,IAAI,CAACjC,QAAQ;UAChCC,OAAO,EAAE8B,QAAQ,CAACE,IAAI,CAAChC;QACzB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMzD,SAAS,CAAC0D,YAAY,CAACzC,MAAM,CAAC;MACrD,IAAIwC,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;QAC7B7B,UAAU,CAAC2B,QAAQ,CAACE,IAAI,CAAChC,OAAO,CAAC;MACnC;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMzD,SAAS,CAACgE,gBAAgB,CAAC/C,MAAM,CAAC;MACzD,IAAIwC,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;QAC7B3B,QAAQ,CAACyB,QAAQ,CAACE,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMK,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMzD,SAAS,CAACmE,OAAO,CAACD,MAAM,CAAC;MAChDE,KAAK,CAAC,wBAAwB,CAAC;MAC/BL,UAAU,CAAC,CAAC;MACZD,gBAAgB,CAAC,CAAC;MAClBO,gBAAgB,CAAC,CAAC;IACpB,CAAC,CACD,OAAOT,KAAK,EAAE;MAAA,IAAAU,eAAA,EAAAC,oBAAA;MACZH,KAAK,CAAC,EAAAE,eAAA,GAAAV,KAAK,CAACH,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,mBAAmB,CAAC;MAC3DX,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF,CAAC;EAED,MAAMS,gBAAgB,GAAG,MAAAA,CAAOI,UAAU,GAAGrB,IAAI,KAAK;IACpD,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMvC,QAAQ,CAAChB,WAAW,CAAC;QAC1CkD,IAAI,EAAEqB,UAAU;QAChBC,IAAI,EAAE,CAAC;QACPC,MAAM,EAAE;UAAExC,SAAS;UAAEE,OAAO;UAAEuC,IAAI,EAAE3C;QAAO;MAC7C,CAAC,CAAC,CAAC;MACH,IAAIwB,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE,CAC/B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMiB,mBAAmB,GAAGA,CAAA,KAAM;IAChCR,gBAAgB,CAAC,CAAC;IAClBP,gBAAgB,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMgB,wBAAwB,GAAGA,CAAA,KAAM;IACrChB,gBAAgB,CAAC,CAAC;IAClBO,gBAAgB,CAAC,CAAC;EACpB,CAAC;EAED3E,SAAS,CAAC,MAAM;IACd8D,aAAa,CAAC,CAAC;IACfM,gBAAgB,CAAC,CAAC;IAClBC,UAAU,CAAC,CAAC;IACZM,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC1C,OAAO,CAAC,CAAC;;EAEb;EACAjC,SAAS,CAAC,MAAM;IACd2D,OAAO,CAAC,CAAC,CAAC;IACVgB,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,CAACpC,MAAM,EAAEE,SAAS,EAAEE,OAAO,CAAC,CAAC;EAIhC,MAAM0C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvC,UAAU,CAAC,IAAI,CAAC;IAChBN,SAAS,CAAC,EAAE,CAAC;IACbE,YAAY,CAAC,IAAI,CAAC;IAClBE,UAAU,CAAC,IAAI,CAAC;IAChBe,OAAO,CAAC,CAAC,CAAC;IACV2B,UAAU,CAAC,MAAMxC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;EACxC,CAAC;EAID,MAAMyC,YAAY,GAAI/B,WAAW,IAAK;IACpCG,OAAO,CAACH,WAAW,CAAC;EACtB,CAAC;EAED,oBACEpC,OAAA;IAAMoE,SAAS,EAAC,iEAAiE;IAAAC,QAAA,gBAE/ErE,OAAA;MAAKoE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BrE,OAAA;QAAKoE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrE,OAAA;UAAKoE,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CrE,OAAA;YAAKoE,SAAS,EAAC,oGAAoG;YAAAC,QAAA,eACjHrE,OAAA;cAAKsE,GAAG,EAAE9B,kBAAkB,CAAC+B,SAAU;cAACC,GAAG,EAAC,QAAQ;cAACJ,SAAS,EAAC;YAAyC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eACN5E,OAAA;YAAAqE,QAAA,eACErE,OAAA;cAAIoE,SAAS,EAAC,qDAAqD;cACjES,OAAO,EAAEvE,eAAgB;cAAA+D,QAAA,EACxB9D,IAAI,CAACI;YAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5E,OAAA;UAAKoE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDrE,OAAA;YAAKoE,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDrE,OAAA;cAAKoE,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7DrE,OAAA;gBAAKoE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3C5E,OAAA;gBAAKoE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAChCS,MAAM,CAACjE,OAAO,CAAC,CAACkE,cAAc,CAAC,OAAO,CAAC,EAAC,MAC3C;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5E,OAAA;gBAAKoE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDrE,OAAA;kBAAQoE,SAAS,EAAC,mEAAmE;kBACnFS,OAAO,EAAEA,CAAA,KAAM9D,eAAe,CAAC,IAAI,CAAE;kBAAAsD,QAAA,EAAC;gBAExC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5E,OAAA,CAACN,WAAW;cAACsF,cAAc,EAAEhB;YAAyB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAKoE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrE,OAAA;UAAIoE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7D5E,OAAA;UAAKoE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCrE,OAAA;YAAKoE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CrE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/C5E,OAAA,CAACR,YAAY;cAACyF,KAAK,EAAE5D,SAAU;cAAC6D,QAAQ,EAAGC,IAAI,IAAK;gBAAE7D,YAAY,CAAC6D,IAAI,CAAC;cAAC,CAAE;cAACC,QAAQ,EAAEvD;YAAW;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACjH5E,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C5E,OAAA,CAACR,YAAY;cAACyF,KAAK,EAAE1D,OAAQ;cAAC2D,QAAQ,EAAGC,IAAI,IAAK3D,UAAU,CAAC2D,IAAI,CAAE;cAACC,QAAQ,EAAEnD;YAAS;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG,CAAC,eACN5E,OAAA,CAACT,MAAM;YAAC8F,cAAc,EAAEjE,SAAU;YAACK,OAAO,EAAEA;UAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACN5E,OAAA,CAAClB,WAAW;UAACqC,MAAM,EAAEA,MAAO;UAACE,SAAS,EAAEA,SAAU;UAACE,OAAO,EAAEA,OAAQ;UAACa,WAAW,EAAEE;QAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAGtG5E,OAAA;UAAKoE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDrE,OAAA;YAAQoE,SAAS,EAAC,wEAAwE;YAACS,OAAO,EAAEZ,gBAAiB;YAAAI,QAAA,EAAC;UAEtH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5E,OAAA,CAACP,UAAU;YAAC0C,UAAU,EAAEA,UAAW;YAACC,WAAW,EAAEE,IAAK;YAAC6B,YAAY,EAAEA;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5E,OAAA,CAACb,KAAK;MACJ0D,IAAI,EAAE5B,KAAM;MACZJ,OAAO,EAAEA,OAAQ;MACjBsC,YAAY,EAAEA;IAAa;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,EAGD9D,YAAY,iBACXd,OAAA;MAAKoE,SAAS,EAAC,0EAA0E;MAAAC,QAAA,eACvFrE,OAAA;QAAKoE,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBrE,OAAA,CAACjB,QAAQ;UACPgC,eAAe,EAAEA,eAAgB;UACjCuE,eAAe,EAAEvB;QAAoB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGG,CAAC;AAEX,CAAC;AAAC1E,EAAA,CA9OID,WAAW;EAAA,QAEEZ,WAAW,EACXM,WAAW,EAmCQL,WAAW,EAOhBA,WAAW;AAAA;AAAAiG,EAAA,GA7CtCtF,WAAW;AA+OjB,eAAeA,WAAW;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}