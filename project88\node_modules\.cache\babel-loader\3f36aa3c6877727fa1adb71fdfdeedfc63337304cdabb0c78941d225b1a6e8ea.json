{"ast": null, "code": "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The subMonths function options.\n */\n\n/**\n * @name subMonths\n * @category Month Helpers\n * @summary Subtract the specified number of months from the given date.\n *\n * @description\n * Subtract the specified number of months from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the months subtracted\n *\n * @example\n * // Subtract 5 months from 1 February 2015:\n * const result = subMonths(new Date(2015, 1, 1), 5)\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function subMonths(date, amount, options) {\n  return addMonths(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMonths;", "map": {"version": 3, "names": ["addMonths", "subMonths", "date", "amount", "options"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/subMonths.js"], "sourcesContent": ["import { addMonths } from \"./addMonths.js\";\n\n/**\n * The subMonths function options.\n */\n\n/**\n * @name subMonths\n * @category Month Helpers\n * @summary Subtract the specified number of months from the given date.\n *\n * @description\n * Subtract the specified number of months from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the months subtracted\n *\n * @example\n * // Subtract 5 months from 1 February 2015:\n * const result = subMonths(new Date(2015, 1, 1), 5)\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function subMonths(date, amount, options) {\n  return addMonths(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMonths;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/C,OAAOJ,SAAS,CAACE,IAAI,EAAE,CAACC,MAAM,EAAEC,OAAO,CAAC;AAC1C;;AAEA;AACA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}