import employeeAPI from "../../api/EmployeeAPI";
import { getEmployeeByUsername } from "../../redux/slices/employeeSlice";

const EmployeeService = {
    getAllEmployees: async (page, size, filter) => {
        return await employeeAPI.getAllEmployees(page, size, filter);
    },
    getEmployeeByUsername: async () => {
        return await employeeAPI.getEmployeeByUsername();
    },
    createEmployee: async (body) => {
        return await employeeAPI.createEmployee(body);
    },
    getEmployeeById: async (id) => {
        return await employeeAPI.getEmployeeById(id);
    },
    updateEmployee: async (id, body) => {
        return await employeeAPI.updateEmployee(id, body);
    },
    deleteEmployee: async (id) => {
        return await employeeAPI.deleteEmployee(id);
    },
    restoreEmployee: async (id) => {
        return await employeeAPI.restoreEmployee(id);
    },
    permanentDeleteEmployee: async (id) => {
        return await employeeAPI.permanentDeleteEmployee(id);
    }
}

export default EmployeeService;