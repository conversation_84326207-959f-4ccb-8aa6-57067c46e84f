{"ast": null, "code": "import { createAsyncThunk, createSlice } from \"@reduxjs/toolkit\";\nimport ProfileService from \"../../features/user/ProfileService\";\nimport UserService from \"../../features/user/UserService\";\nexport const userProfile = createAsyncThunk('userProfile', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await ProfileService.getProfile();\n    if (response.status >= 200 && response.status < 300) {\n      return {\n        profile: response.data || \"Profile fetched successfully\"\n      };\n    } else {\n      return rejectWithValue(\"Unexpected response from server.\");\n    }\n  } catch (error) {\n    return rejectWithValue(error.response.data);\n  }\n});\nexport const getAllUsers = createAsyncThunk('getAllUsers', async ({\n  page,\n  size,\n  filter\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const params = {\n      name: filter.name\n    };\n    const response = await UserService.getAllUsers(page, size, params);\n    if (response.status >= 200 && response.status < 300) {\n      return {\n        users: response.data || \"Users fetched successfully\"\n      };\n    } else {\n      return rejectWithValue(\"Unexpected response from server.\");\n    }\n  } catch (error) {\n    return rejectWithValue(error.response.data);\n  }\n});\nexport const getBalance = createAsyncThunk('getBalance', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await UserService.getBalance();\n    if (response.status >= 200 && response.status < 300) {\n      return {\n        balance: response.data || \"Balance fetched successfully\"\n      };\n    } else {\n      return rejectWithValue(\"Unexpected response from server.\");\n    }\n  } catch (error) {\n    return rejectWithValue(error.response.data);\n  }\n});\nexport const editUserByEmployee = createAsyncThunk('editUserByEmployee', async ({\n  userID,\n  body\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await UserService.editUserByEmployee(userID, body);\n    if (response.status >= 200 && response.status < 300) {\n      return {\n        message: response.data || \"User edited successfully\"\n      };\n    } else {\n      return rejectWithValue(\"Unexpected response from server.\");\n    }\n  } catch (error) {\n    return rejectWithValue(error.response.data);\n  }\n});\nconst profileSlice = createSlice({\n  name: 'profile',\n  initialState: {\n    profile: null,\n    loading: false,\n    error: null\n  },\n  reducers: {},\n  extraReducers: builder => {\n    builder.addCase(userProfile.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(userProfile.fulfilled, (state, action) => {\n      state.profile = action.payload;\n      state.loading = false;\n    }).addCase(userProfile.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    });\n  }\n});\nconst userSlice = createSlice({\n  name: 'user',\n  initialState: {\n    users: [],\n    totalPages: 0,\n    currentPage: 0,\n    totalElements: 0,\n    loading: false,\n    error: null\n  },\n  reducers: {},\n  extraReducers: builder => {\n    builder.addCase(getAllUsers.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(getAllUsers.fulfilled, (state, action) => {\n      state.users = action.payload.users;\n      state.currentPage = action.payload.users.currentPage;\n      state.totalElements = action.payload.users.totalElements;\n      state.totalPages = action.payload.users.totalPages;\n      state.loading = false;\n    }).addCase(getAllUsers.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    }).addCase(editUserByEmployee.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(editUserByEmployee.fulfilled, (state, action) => {\n      state.loading = false;\n      state.error = null;\n    }).addCase(editUserByEmployee.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    });\n  }\n});\nconst balanceSlice = createSlice({\n  name: 'balance',\n  initialState: {\n    balance: '',\n    loading: false,\n    error: null\n  },\n  reducers: {},\n  extraReducers: builder => {\n    builder.addCase(getBalance.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(getBalance.fulfilled, (state, action) => {\n      state.loading = false;\n      state.balance = action.payload;\n    }).addCase(getBalance.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    });\n  }\n});\nexport const profileReducer = profileSlice.reducer;\nexport const userReducer = userSlice.reducer;\nexport const balanceReducer = balanceSlice.reducer;", "map": {"version": 3, "names": ["createAsyncThunk", "createSlice", "ProfileService", "UserService", "userProfile", "_", "rejectWithValue", "response", "getProfile", "status", "profile", "data", "error", "getAllUsers", "page", "size", "filter", "params", "name", "users", "getBalance", "balance", "editUserByEmployee", "userID", "body", "message", "profileSlice", "initialState", "loading", "reducers", "extraReducers", "builder", "addCase", "pending", "state", "fulfilled", "action", "payload", "rejected", "userSlice", "totalPages", "currentPage", "totalElements", "balanceSlice", "profileReducer", "reducer", "userReducer", "balanceReducer"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/redux/slices/userSlice.js"], "sourcesContent": ["import { createAsyncThunk, createSlice } from \"@reduxjs/toolkit\";\r\nimport ProfileService from \"../../features/user/ProfileService\";\r\nimport UserService from \"../../features/user/UserService\";\r\n\r\nexport const userProfile = createAsyncThunk('userProfile', async (_, { rejectWithValue }) => {\r\n    try {\r\n        const response = await ProfileService.getProfile();\r\n\r\n        if (response.status >= 200 && response.status < 300) {\r\n            return {\r\n                profile: response.data || \"Profile fetched successfully\",\r\n            };\r\n        } else {\r\n            return rejectWithValue(\"Unexpected response from server.\");\r\n        }\r\n    } catch (error) {\r\n        return rejectWithValue(error.response.data);\r\n    }\r\n});\r\n\r\nexport const getAllUsers = createAsyncThunk('getAllUsers', async ({ page, size, filter }, { rejectWithValue }) => {\r\n    try {\r\n        const params = { name: filter.name };\r\n        const response = await UserService.getAllUsers(page, size, params);\r\n\r\n        if (response.status >= 200 && response.status < 300) {\r\n            return {\r\n                users: response.data || \"Users fetched successfully\",\r\n            };\r\n        } else {\r\n            return rejectWithValue(\"Unexpected response from server.\");\r\n        }\r\n    } catch (error) {\r\n        return rejectWithValue(error.response.data);\r\n    }\r\n})\r\n\r\nexport const getBalance = createAsyncThunk('getBalance', async (_, { rejectWithValue }) => {\r\n    try {\r\n        const response = await UserService.getBalance();\r\n\r\n        if (response.status >= 200 && response.status < 300) {\r\n            return {\r\n                balance: response.data || \"Balance fetched successfully\",\r\n            };\r\n        } else {\r\n            return rejectWithValue(\"Unexpected response from server.\")\r\n        }\r\n    } catch (error) {\r\n        return rejectWithValue(error.response.data);\r\n    }\r\n})\r\n\r\nexport const editUserByEmployee = createAsyncThunk('editUserByEmployee', async ({ userID, body }, { rejectWithValue }) => {\r\n    try {\r\n        const response = await UserService.editUserByEmployee(userID, body);\r\n\r\n        if (response.status >= 200 && response.status < 300) {\r\n            return {\r\n                message: response.data || \"User edited successfully\",\r\n            };\r\n        } else {\r\n            return rejectWithValue(\"Unexpected response from server.\");\r\n        }\r\n    } catch (error) {\r\n        return rejectWithValue(error.response.data);\r\n    }\r\n})\r\n\r\nconst profileSlice = createSlice({\r\n    name: 'profile',\r\n    initialState: {\r\n        profile: null,\r\n        loading: false,\r\n        error: null\r\n    },\r\n    reducers: {},\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(userProfile.pending, (state) => {\r\n                state.loading = true;\r\n                state.error = null;\r\n            })\r\n            .addCase(userProfile.fulfilled, (state, action) => {\r\n                state.profile = action.payload;\r\n                state.loading = false;\r\n            })\r\n            .addCase(userProfile.rejected, (state, action) => {\r\n                state.loading = false;\r\n                state.error = action.payload;\r\n            });\r\n    }\r\n})\r\n\r\nconst userSlice = createSlice({\r\n    name: 'user',\r\n    initialState: {\r\n        users: [],\r\n        totalPages: 0,\r\n        currentPage: 0,\r\n        totalElements: 0,\r\n        loading: false,\r\n        error: null\r\n    },\r\n    reducers: {},\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(getAllUsers.pending, (state) => {\r\n                state.loading = true;\r\n                state.error = null;\r\n            })\r\n            .addCase(getAllUsers.fulfilled, (state, action) => {\r\n                state.users = action.payload.users;\r\n                state.currentPage = action.payload.users.currentPage;\r\n                state.totalElements = action.payload.users.totalElements;\r\n                state.totalPages = action.payload.users.totalPages;\r\n                state.loading = false;\r\n            })\r\n            .addCase(getAllUsers.rejected, (state, action) => {\r\n                state.loading = false;\r\n                state.error = action.payload;\r\n            })\r\n            .addCase(editUserByEmployee.pending, (state) => {\r\n                state.loading = true;\r\n                state.error = null;\r\n            })\r\n            .addCase(editUserByEmployee.fulfilled, (state, action) => {\r\n                state.loading = false;\r\n                state.error = null;\r\n            })\r\n            .addCase(editUserByEmployee.rejected, (state, action) => {\r\n                state.loading = false;\r\n                state.error = action.payload;\r\n            });\r\n\r\n    }\r\n})\r\n\r\nconst balanceSlice = createSlice({\r\n    name: 'balance',\r\n    initialState: {\r\n        balance: '',\r\n        loading: false,\r\n        error: null,\r\n    },\r\n    reducers: {},\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(getBalance.pending, (state) => {\r\n                state.loading = true;\r\n                state.error = null;\r\n            })\r\n            .addCase(getBalance.fulfilled, (state, action) => {\r\n                state.loading = false;\r\n                state.balance = action.payload;\r\n            })\r\n            .addCase(getBalance.rejected, (state, action) => {\r\n                state.loading = false;\r\n                state.error = action.payload;\r\n            })\r\n    }\r\n})\r\n\r\nexport const profileReducer = profileSlice.reducer;\r\nexport const userReducer = userSlice.reducer;\r\nexport const balanceReducer = balanceSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;AAChE,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,WAAW,MAAM,iCAAiC;AAEzD,OAAO,MAAMC,WAAW,GAAGJ,gBAAgB,CAAC,aAAa,EAAE,OAAOK,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACzF,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAML,cAAc,CAACM,UAAU,CAAC,CAAC;IAElD,IAAID,QAAQ,CAACE,MAAM,IAAI,GAAG,IAAIF,QAAQ,CAACE,MAAM,GAAG,GAAG,EAAE;MACjD,OAAO;QACHC,OAAO,EAAEH,QAAQ,CAACI,IAAI,IAAI;MAC9B,CAAC;IACL,CAAC,MAAM;MACH,OAAOL,eAAe,CAAC,kCAAkC,CAAC;IAC9D;EACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;IACZ,OAAON,eAAe,CAACM,KAAK,CAACL,QAAQ,CAACI,IAAI,CAAC;EAC/C;AACJ,CAAC,CAAC;AAEF,OAAO,MAAME,WAAW,GAAGb,gBAAgB,CAAC,aAAa,EAAE,OAAO;EAAEc,IAAI;EAAEC,IAAI;EAAEC;AAAO,CAAC,EAAE;EAAEV;AAAgB,CAAC,KAAK;EAC9G,IAAI;IACA,MAAMW,MAAM,GAAG;MAAEC,IAAI,EAAEF,MAAM,CAACE;IAAK,CAAC;IACpC,MAAMX,QAAQ,GAAG,MAAMJ,WAAW,CAACU,WAAW,CAACC,IAAI,EAAEC,IAAI,EAAEE,MAAM,CAAC;IAElE,IAAIV,QAAQ,CAACE,MAAM,IAAI,GAAG,IAAIF,QAAQ,CAACE,MAAM,GAAG,GAAG,EAAE;MACjD,OAAO;QACHU,KAAK,EAAEZ,QAAQ,CAACI,IAAI,IAAI;MAC5B,CAAC;IACL,CAAC,MAAM;MACH,OAAOL,eAAe,CAAC,kCAAkC,CAAC;IAC9D;EACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;IACZ,OAAON,eAAe,CAACM,KAAK,CAACL,QAAQ,CAACI,IAAI,CAAC;EAC/C;AACJ,CAAC,CAAC;AAEF,OAAO,MAAMS,UAAU,GAAGpB,gBAAgB,CAAC,YAAY,EAAE,OAAOK,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACvF,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,WAAW,CAACiB,UAAU,CAAC,CAAC;IAE/C,IAAIb,QAAQ,CAACE,MAAM,IAAI,GAAG,IAAIF,QAAQ,CAACE,MAAM,GAAG,GAAG,EAAE;MACjD,OAAO;QACHY,OAAO,EAAEd,QAAQ,CAACI,IAAI,IAAI;MAC9B,CAAC;IACL,CAAC,MAAM;MACH,OAAOL,eAAe,CAAC,kCAAkC,CAAC;IAC9D;EACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;IACZ,OAAON,eAAe,CAACM,KAAK,CAACL,QAAQ,CAACI,IAAI,CAAC;EAC/C;AACJ,CAAC,CAAC;AAEF,OAAO,MAAMW,kBAAkB,GAAGtB,gBAAgB,CAAC,oBAAoB,EAAE,OAAO;EAAEuB,MAAM;EAAEC;AAAK,CAAC,EAAE;EAAElB;AAAgB,CAAC,KAAK;EACtH,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,WAAW,CAACmB,kBAAkB,CAACC,MAAM,EAAEC,IAAI,CAAC;IAEnE,IAAIjB,QAAQ,CAACE,MAAM,IAAI,GAAG,IAAIF,QAAQ,CAACE,MAAM,GAAG,GAAG,EAAE;MACjD,OAAO;QACHgB,OAAO,EAAElB,QAAQ,CAACI,IAAI,IAAI;MAC9B,CAAC;IACL,CAAC,MAAM;MACH,OAAOL,eAAe,CAAC,kCAAkC,CAAC;IAC9D;EACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;IACZ,OAAON,eAAe,CAACM,KAAK,CAACL,QAAQ,CAACI,IAAI,CAAC;EAC/C;AACJ,CAAC,CAAC;AAEF,MAAMe,YAAY,GAAGzB,WAAW,CAAC;EAC7BiB,IAAI,EAAE,SAAS;EACfS,YAAY,EAAE;IACVjB,OAAO,EAAE,IAAI;IACbkB,OAAO,EAAE,KAAK;IACdhB,KAAK,EAAE;EACX,CAAC;EACDiB,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC5B,WAAW,CAAC6B,OAAO,EAAGC,KAAK,IAAK;MACrCA,KAAK,CAACN,OAAO,GAAG,IAAI;MACpBM,KAAK,CAACtB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDoB,OAAO,CAAC5B,WAAW,CAAC+B,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC/CF,KAAK,CAACxB,OAAO,GAAG0B,MAAM,CAACC,OAAO;MAC9BH,KAAK,CAACN,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDI,OAAO,CAAC5B,WAAW,CAACkC,QAAQ,EAAE,CAACJ,KAAK,EAAEE,MAAM,KAAK;MAC9CF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACtB,KAAK,GAAGwB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,MAAME,SAAS,GAAGtC,WAAW,CAAC;EAC1BiB,IAAI,EAAE,MAAM;EACZS,YAAY,EAAE;IACVR,KAAK,EAAE,EAAE;IACTqB,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBd,OAAO,EAAE,KAAK;IACdhB,KAAK,EAAE;EACX,CAAC;EACDiB,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACnB,WAAW,CAACoB,OAAO,EAAGC,KAAK,IAAK;MACrCA,KAAK,CAACN,OAAO,GAAG,IAAI;MACpBM,KAAK,CAACtB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDoB,OAAO,CAACnB,WAAW,CAACsB,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC/CF,KAAK,CAACf,KAAK,GAAGiB,MAAM,CAACC,OAAO,CAAClB,KAAK;MAClCe,KAAK,CAACO,WAAW,GAAGL,MAAM,CAACC,OAAO,CAAClB,KAAK,CAACsB,WAAW;MACpDP,KAAK,CAACQ,aAAa,GAAGN,MAAM,CAACC,OAAO,CAAClB,KAAK,CAACuB,aAAa;MACxDR,KAAK,CAACM,UAAU,GAAGJ,MAAM,CAACC,OAAO,CAAClB,KAAK,CAACqB,UAAU;MAClDN,KAAK,CAACN,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDI,OAAO,CAACnB,WAAW,CAACyB,QAAQ,EAAE,CAACJ,KAAK,EAAEE,MAAM,KAAK;MAC9CF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACtB,KAAK,GAAGwB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC,CACDL,OAAO,CAACV,kBAAkB,CAACW,OAAO,EAAGC,KAAK,IAAK;MAC5CA,KAAK,CAACN,OAAO,GAAG,IAAI;MACpBM,KAAK,CAACtB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDoB,OAAO,CAACV,kBAAkB,CAACa,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MACtDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACtB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDoB,OAAO,CAACV,kBAAkB,CAACgB,QAAQ,EAAE,CAACJ,KAAK,EAAEE,MAAM,KAAK;MACrDF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACtB,KAAK,GAAGwB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC;EAEV;AACJ,CAAC,CAAC;AAEF,MAAMM,YAAY,GAAG1C,WAAW,CAAC;EAC7BiB,IAAI,EAAE,SAAS;EACfS,YAAY,EAAE;IACVN,OAAO,EAAE,EAAE;IACXO,OAAO,EAAE,KAAK;IACdhB,KAAK,EAAE;EACX,CAAC;EACDiB,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACZ,UAAU,CAACa,OAAO,EAAGC,KAAK,IAAK;MACpCA,KAAK,CAACN,OAAO,GAAG,IAAI;MACpBM,KAAK,CAACtB,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC,CACDoB,OAAO,CAACZ,UAAU,CAACe,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC9CF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACb,OAAO,GAAGe,MAAM,CAACC,OAAO;IAClC,CAAC,CAAC,CACDL,OAAO,CAACZ,UAAU,CAACkB,QAAQ,EAAE,CAACJ,KAAK,EAAEE,MAAM,KAAK;MAC7CF,KAAK,CAACN,OAAO,GAAG,KAAK;MACrBM,KAAK,CAACtB,KAAK,GAAGwB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAMO,cAAc,GAAGlB,YAAY,CAACmB,OAAO;AAClD,OAAO,MAAMC,WAAW,GAAGP,SAAS,CAACM,OAAO;AAC5C,OAAO,MAAME,cAAc,GAAGJ,YAAY,CAACE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}