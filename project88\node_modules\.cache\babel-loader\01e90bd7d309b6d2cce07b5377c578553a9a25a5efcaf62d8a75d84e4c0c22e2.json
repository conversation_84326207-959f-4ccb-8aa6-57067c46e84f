{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nimport { parseNDigitsSigned } from \"../utils.js\";\nexport class ExtendedYearParser extends Parser {\n  priority = 130;\n  parse(dateString, token) {\n    if (token === \"u\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n    return parseNDigitsSigned(token.length, dateString);\n  }\n  set(date, _flags, value) {\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"G\", \"y\", \"Y\", \"R\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "parseNDigitsSigned", "<PERSON><PERSON>ear<PERSON><PERSON><PERSON>", "priority", "parse", "dateString", "token", "length", "set", "date", "_flags", "value", "setFullYear", "setHours", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/node_modules/date-fns/parse/_lib/parsers/ExtendedYearParser.js"], "sourcesContent": ["import { Parser } from \"../Parser.js\";\n\nimport { parseNDigitsSigned } from \"../utils.js\";\n\nexport class ExtendedYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token) {\n    if (token === \"u\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n\n    return parseNDigitsSigned(token.length, dateString);\n  }\n\n  set(date, _flags, value) {\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"G\", \"y\", \"Y\", \"R\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,kBAAkB,QAAQ,aAAa;AAEhD,OAAO,MAAMC,kBAAkB,SAASF,MAAM,CAAC;EAC7CG,QAAQ,GAAG,GAAG;EAEdC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,KAAK,GAAG,EAAE;MACjB,OAAOL,kBAAkB,CAAC,CAAC,EAAEI,UAAU,CAAC;IAC1C;IAEA,OAAOJ,kBAAkB,CAACK,KAAK,CAACC,MAAM,EAAEF,UAAU,CAAC;EACrD;EAEAG,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACvBF,IAAI,CAACG,WAAW,CAACD,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7BF,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAOJ,IAAI;EACb;EAEAK,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}