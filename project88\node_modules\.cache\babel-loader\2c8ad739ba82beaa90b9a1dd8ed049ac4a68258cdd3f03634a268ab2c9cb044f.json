{"ast": null, "code": "// import { parse } from 'date-fns';\nimport { z } from 'zod';\nimport UserApi from '../api/UserApi';\nexport const Validation = z.object({\n  username: z.string().min(6, \"Tên tài khoản phải có ít nhất 6 ký tự\"),\n  firstName: z.string().nonempty(\"Họ phải bắt buộc\"),\n  lastName: z.string().nonempty(\"Tên phải bắt buộc\"),\n  email: z.string().nonempty(\"Email phải bắt buộc\").email(\"Email không hợp lệ\").refine(async value => {\n    const rs = await UserApi.isExistEmail(value);\n    return !rs.data;\n  }, {\n    message: \"Email đã tồn tại\"\n  }),\n  birth: z.string().refine(val => !isNaN(Date.parse(val)), {\n    message: \"<PERSON><PERSON><PERSON> không hợp lệ\"\n  }),\n  cccd: z.string().nonempty({\n    message: \"CCCD không được để trống\"\n  }).regex(/^\\d{12}$/, {\n    message: \"CCCD phải gồm đúng 12 chữ số\"\n  }),\n  gender: z.enum(['Male', 'Female', 'Other'], {\n    errorMap: () => ({\n      message: 'Vui lòng chọn giới tính'\n    })\n  }),\n  phone: z.string().nonempty({\n    message: \"Số điện thoại không được để trống\"\n  }).regex(/^\\d{10}$/, {\n    message: \"Số điện thoại phải gồm đúng 10 chữ số\"\n  }).refine(async value => {\n    const rs = await UserApi.isExistPhone(value);\n    return !rs.data;\n  }, {\n    message: \"SDT đã tồn tại\"\n  }),\n  password: z.string().min(6, \"Mật khẩu cần ít nhất 6 ký tựtự\"),\n  confirmPassword: z.string().min(6, \"Xác nhận mật khẩu ít nhất 6 ký tự\")\n}).refine(_c = data => data.password === data.confirmPassword, {\n  message: \"Mật khẩu không khớp\",\n  path: [\"confirmPassword\"]\n});\n_c2 = Validation;\nvar _c, _c2;\n$RefreshReg$(_c, \"Validation$z.object({\\r\\n    username: z.string().min(6, \\\"T\\xEAn t\\xE0i kho\\u1EA3n ph\\u1EA3i c\\xF3 \\xEDt nh\\u1EA5t 6 k\\xFD t\\u1EF1\\\"),\\r\\n    firstName: z.string().nonempty(\\\"H\\u1ECD ph\\u1EA3i b\\u1EAFt bu\\u1ED9c\\\"),\\r\\n    lastName: z.string().nonempty(\\\"T\\xEAn ph\\u1EA3i b\\u1EAFt bu\\u1ED9c\\\"),\\r\\n    email: z.string().nonempty(\\\"Email ph\\u1EA3i b\\u1EAFt bu\\u1ED9c\\\").email(\\\"Email kh\\xF4ng h\\u1EE3p l\\u1EC7\\\")\\r\\n        .refine(async (value) => {\\r\\n            const rs = await UserApi.isExistEmail(value);\\r\\n            return !rs.data\\r\\n        }, {\\r\\n            message: \\\"Email \\u0111\\xE3 t\\u1ED3n t\\u1EA1i\\\"\\r\\n        }),\\r\\n    birth: z.string().refine(val => !isNaN(Date.parse(val)), {\\r\\n        message: \\\"Ng\\xE0y kh\\xF4ng h\\u1EE3p l\\u1EC7\\\"\\r\\n    }),\\r\\n    cccd: z\\r\\n        .string()\\r\\n        .nonempty({ message: \\\"CCCD kh\\xF4ng \\u0111\\u01B0\\u1EE3c \\u0111\\u1EC3 tr\\u1ED1ng\\\" })\\r\\n        .regex(/^\\\\d{12}$/, { message: \\\"CCCD ph\\u1EA3i g\\u1ED3m \\u0111\\xFAng 12 ch\\u1EEF s\\u1ED1\\\" }),\\r\\n    gender: z.enum(['Male', 'Female', 'Other'], {\\r\\n        errorMap: () => ({ message: 'Vui l\\xF2ng ch\\u1ECDn gi\\u1EDBi t\\xEDnh' })\\r\\n    }),\\r\\n    phone: z\\r\\n        .string()\\r\\n        .nonempty({ message: \\\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i kh\\xF4ng \\u0111\\u01B0\\u1EE3c \\u0111\\u1EC3 tr\\u1ED1ng\\\" })\\r\\n        .regex(/^\\\\d{10}$/, { message: \\\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i ph\\u1EA3i g\\u1ED3m \\u0111\\xFAng 10 ch\\u1EEF s\\u1ED1\\\" })\\r\\n        .refine(async (value) => {\\r\\n            const rs = await UserApi.isExistPhone(value);\\r\\n            return !rs.data\\r\\n        }, {\\r\\n            message: \\\"SDT \\u0111\\xE3 t\\u1ED3n t\\u1EA1i\\\"\\r\\n        }),\\r\\n    password: z.string().min(6, \\\"M\\u1EADt kh\\u1EA9u c\\u1EA7n \\xEDt nh\\u1EA5t 6 k\\xFD t\\u1EF1t\\u1EF1\\\"),\\r\\n    confirmPassword: z\\r\\n        .string()\\r\\n        .min(6, \\\"X\\xE1c nh\\u1EADn m\\u1EADt kh\\u1EA9u \\xEDt nh\\u1EA5t 6 k\\xFD t\\u1EF1\\\"),\\r\\n}).refine\");\n$RefreshReg$(_c2, \"Validation\");", "map": {"version": 3, "names": ["z", "UserApi", "Validation", "object", "username", "string", "min", "firstName", "nonempty", "lastName", "email", "refine", "value", "rs", "isExistEmail", "data", "message", "birth", "val", "isNaN", "Date", "parse", "cccd", "regex", "gender", "enum", "errorMap", "phone", "isExistPhone", "password", "confirmPassword", "_c", "path", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/validation/Validation.js"], "sourcesContent": ["// import { parse } from 'date-fns';\r\nimport { z } from 'zod';\r\nimport UserApi from '../api/UserApi';\r\n\r\nexport const Validation = z.object({\r\n    username: z.string().min(6, \"Tên tài khoản phải có ít nhất 6 ký tự\"),\r\n    firstName: z.string().nonempty(\"Họ phải bắt buộc\"),\r\n    lastName: z.string().nonempty(\"Tên phải bắt buộc\"),\r\n    email: z.string().nonempty(\"Email phải bắt buộc\").email(\"Email không hợp lệ\")\r\n        .refine(async (value) => {\r\n            const rs = await UserApi.isExistEmail(value);\r\n            return !rs.data\r\n        }, {\r\n            message: \"Email đã tồn tại\"\r\n        }),\r\n    birth: z.string().refine(val => !isNaN(Date.parse(val)), {\r\n        message: \"<PERSON><PERSON><PERSON> không hợp lệ\"\r\n    }),\r\n    cccd: z\r\n        .string()\r\n        .nonempty({ message: \"CCCD không được để trống\" })\r\n        .regex(/^\\d{12}$/, { message: \"CCCD phải gồm đúng 12 chữ số\" }),\r\n    gender: z.enum(['Male', 'Female', 'Other'], {\r\n        errorMap: () => ({ message: 'Vui lòng chọn giới tính' })\r\n    }),\r\n    phone: z\r\n        .string()\r\n        .nonempty({ message: \"Số điện thoại không được để trống\" })\r\n        .regex(/^\\d{10}$/, { message: \"Số điện thoại phải gồm đúng 10 chữ số\" })\r\n        .refine(async (value) => {\r\n            const rs = await UserApi.isExistPhone(value);\r\n            return !rs.data\r\n        }, {\r\n            message: \"SDT đã tồn tại\"\r\n        }),\r\n    password: z.string().min(6, \"Mật khẩu cần ít nhất 6 ký tựtự\"),\r\n    confirmPassword: z\r\n        .string()\r\n        .min(6, \"Xác nhận mật khẩu ít nhất 6 ký tự\"),\r\n}).refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Mật khẩu không khớp\",\r\n    path: [\"confirmPassword\"],\r\n})"], "mappings": "AAAA;AACA,SAASA,CAAC,QAAQ,KAAK;AACvB,OAAOC,OAAO,MAAM,gBAAgB;AAEpC,OAAO,MAAMC,UAAU,GAAGF,CAAC,CAACG,MAAM,CAAC;EAC/BC,QAAQ,EAAEJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,uCAAuC,CAAC;EACpEC,SAAS,EAAEP,CAAC,CAACK,MAAM,CAAC,CAAC,CAACG,QAAQ,CAAC,kBAAkB,CAAC;EAClDC,QAAQ,EAAET,CAAC,CAACK,MAAM,CAAC,CAAC,CAACG,QAAQ,CAAC,mBAAmB,CAAC;EAClDE,KAAK,EAAEV,CAAC,CAACK,MAAM,CAAC,CAAC,CAACG,QAAQ,CAAC,qBAAqB,CAAC,CAACE,KAAK,CAAC,oBAAoB,CAAC,CACxEC,MAAM,CAAC,MAAOC,KAAK,IAAK;IACrB,MAAMC,EAAE,GAAG,MAAMZ,OAAO,CAACa,YAAY,CAACF,KAAK,CAAC;IAC5C,OAAO,CAACC,EAAE,CAACE,IAAI;EACnB,CAAC,EAAE;IACCC,OAAO,EAAE;EACb,CAAC,CAAC;EACNC,KAAK,EAAEjB,CAAC,CAACK,MAAM,CAAC,CAAC,CAACM,MAAM,CAACO,GAAG,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,CAAC,EAAE;IACrDF,OAAO,EAAE;EACb,CAAC,CAAC;EACFM,IAAI,EAAEtB,CAAC,CACFK,MAAM,CAAC,CAAC,CACRG,QAAQ,CAAC;IAAEQ,OAAO,EAAE;EAA2B,CAAC,CAAC,CACjDO,KAAK,CAAC,UAAU,EAAE;IAAEP,OAAO,EAAE;EAA+B,CAAC,CAAC;EACnEQ,MAAM,EAAExB,CAAC,CAACyB,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE;IACxCC,QAAQ,EAAEA,CAAA,MAAO;MAAEV,OAAO,EAAE;IAA0B,CAAC;EAC3D,CAAC,CAAC;EACFW,KAAK,EAAE3B,CAAC,CACHK,MAAM,CAAC,CAAC,CACRG,QAAQ,CAAC;IAAEQ,OAAO,EAAE;EAAoC,CAAC,CAAC,CAC1DO,KAAK,CAAC,UAAU,EAAE;IAAEP,OAAO,EAAE;EAAwC,CAAC,CAAC,CACvEL,MAAM,CAAC,MAAOC,KAAK,IAAK;IACrB,MAAMC,EAAE,GAAG,MAAMZ,OAAO,CAAC2B,YAAY,CAAChB,KAAK,CAAC;IAC5C,OAAO,CAACC,EAAE,CAACE,IAAI;EACnB,CAAC,EAAE;IACCC,OAAO,EAAE;EACb,CAAC,CAAC;EACNa,QAAQ,EAAE7B,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,gCAAgC,CAAC;EAC7DwB,eAAe,EAAE9B,CAAC,CACbK,MAAM,CAAC,CAAC,CACRC,GAAG,CAAC,CAAC,EAAE,mCAAmC;AACnD,CAAC,CAAC,CAACK,MAAM,CAAAoB,EAAA,GAAEhB,IAAI,IAAKA,IAAI,CAACc,QAAQ,KAAKd,IAAI,CAACe,eAAe,EAAE;EACxDd,OAAO,EAAE,qBAAqB;EAC9BgB,IAAI,EAAE,CAAC,iBAAiB;AAC5B,CAAC,CAAC;AAAAC,GAAA,GAtCW/B,UAAU;AAAA,IAAA6B,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}