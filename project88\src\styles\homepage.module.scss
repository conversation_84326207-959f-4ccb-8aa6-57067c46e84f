body, html {
  margin: 0;
  font-family: Arial, sans-serif;
  background-color: #fff;
  color: #000;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 900px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px;
  border-bottom: 1px solid #e5e5e5;
  background-color: #999;
}

.logo {
  color: #f97316;
  font-size: 24px;
  font-weight: bold;
}

.header-right button {
  margin-right: 16px;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: red;
  border-radius: 50%;
  display: inline-block;
}

.footer {
  text-align: center;
  padding: 16px;
  font-size: 13px;
  color: #777;
  border-top: 1px solid #e5e5e5;
  background-color: #999;
}

.mode-btn{
 margin-top: 8px;
  padding: 4px 10px;
  background-color: #6b7a78;
  color: #000405;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer; 
}
.content {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 24px;
  padding: 32px;
  flex: 1;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.avatar {
  width: 64px;
  height: 64px;
  background-color: #ddd;
  border-radius: 50%;
  font-weight: bold;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.balance-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.card {
  border: 1px solid #e5e5e5;
  border-radius: 10px;
  padding: 16px;
}

.card-title {
  font-size: 12px;
  color: #666;
}

.card-amount {
  font-size: 20px;
  font-weight: bold;
}

.transfer-btn {
  margin-top: 8px;
  color: #f97316;
  font-size: 13px;
  background: none;
  border: none;
  cursor: pointer;
}

.transaction-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.transaction-table th,
.transaction-table td {
  padding: 8px;
  border: 1px solid #e5e5e5;
}

.transaction-table thead {
  background-color: #f9f9f9;
}

.pagination {
  display: flex;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  align-items: center;
  justify-content: center;
}

.bill-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bill-card {
  border: 1px solid #e5e5e5;
  border-radius: 10px;
  padding: 16px;
}

.bill-title {
  font-size: 16px;
  font-weight: 600;
}

.bill-details {
  font-size: 12px;
  color: #555;
  margin-top: 8px;
}

.pay-btn {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  padding: 6px 16px;
  background-color: #fed7aa;
  color: #c2410c;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
}