{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\auth\\\\FogotPassword.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useForm } from 'react-hook-form';\nimport { useNavigate } from 'react-router-dom';\nimport AuthAPI from '../../api/AuthAPI';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FogotPassword = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const FogotPassword = async email => {\n    try {\n      // Gọi API để gửi email reset mật khẩu\n      const response = await AuthAPI.forgotPassword(email);\n      // Hiển thị thông báo thành công\n      alert(response.data);\n    } catch (error) {\n      console.error('Error sending email:', error);\n      alert('<PERSON><PERSON> x<PERSON>y ra lỗi khi gửi email. Vui lòng thử lại sau.');\n    }\n  };\n  const onSubmit = async data => {\n    await FogotPassword(data.email);\n    navigate('/login');\n    // sau khi gửi email thành công => popup thông báo => chuyển hướng đến trang login\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-8 rounded-lg shadow-md w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-center mb-6\",\n        children: \"Qu\\xEAn M\\u1EADt Kh\\u1EA9u\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ...register('email', {\n              required: 'Email is required',\n              pattern: {\n                value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$/,\n                message: 'Invalid email address'\n              }\n            }),\n            type: \"email\",\n            placeholder: \"Enter your email\",\n            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.email.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"w-full bg-red-100 text-red-600 py-2 px-4 rounded-md hover:bg-red-200 transition\",\n          children: \"X\\xE1c nh\\u1EADn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(FogotPassword, \"hvZci8bfHEyrOM2r7yZN4BE/YxQ=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = FogotPassword;\nexport default FogotPassword;\nvar _c;\n$RefreshReg$(_c, \"FogotPassword\");", "map": {"version": 3, "names": ["React", "useForm", "useNavigate", "AuthAPI", "jsxDEV", "_jsxDEV", "FogotPassword", "_s", "register", "handleSubmit", "formState", "errors", "navigate", "email", "response", "forgotPassword", "alert", "data", "error", "console", "onSubmit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "required", "pattern", "value", "message", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/auth/FogotPassword.js"], "sourcesContent": ["import React from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport AuthAPI from '../../api/AuthAPI';\r\n\r\nconst FogotPassword = () => {\r\n  const { register, handleSubmit, formState: { errors } } = useForm();\r\n  const navigate = useNavigate();\r\n\r\n  const FogotPassword = async (email) => {\r\n    try {\r\n      // Gọi API để gửi email reset mật khẩu\r\n      const response = await AuthAPI.forgotPassword(email);\r\n      // Hiển thị thông báo thành công\r\n      alert(response.data)\r\n    } catch (error) {\r\n      console.error('Error sending email:', error);\r\n      alert('Đ<PERSON> xảy ra lỗi khi gửi email. Vui lòng thử lại sau.');\r\n    }\r\n  }\r\n\r\n  const onSubmit = async (data) => {\r\n\r\n    await FogotPassword(data.email);\r\n    navigate('/login')\r\n    // sau khi gửi email thành công => popup thông báo => chuyển hướng đến trang login\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-100\">\r\n      <div className=\"bg-white p-8 rounded-lg shadow-md w-full max-w-md\">\r\n        <h1 className=\"text-2xl font-bold text-center mb-6\">Quên Mật Khẩu</h1>\r\n        <form onSubmit={handleSubmit(onSubmit)}>\r\n          <div className=\"mb-4\">\r\n            <input\r\n              {...register('email', {\r\n                required: 'Email is required',\r\n                pattern: {\r\n                  value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$/,\r\n                  message: 'Invalid email address',\r\n                },\r\n              })}\r\n              type=\"email\"\r\n              placeholder=\"Enter your email\"\r\n              className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n            />\r\n            {errors.email && <p className=\"text-red-500 text-sm mt-1\">{errors.email.message}</p>}\r\n          </div>\r\n          <button\r\n            type=\"submit\"\r\n            className=\"w-full bg-red-100 text-red-600 py-2 px-4 rounded-md hover:bg-red-200 transition\"\r\n          >\r\n            Xác nhận\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FogotPassword;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnE,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMI,aAAa,GAAG,MAAOO,KAAK,IAAK;IACrC,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMX,OAAO,CAACY,cAAc,CAACF,KAAK,CAAC;MACpD;MACAG,KAAK,CAACF,QAAQ,CAACG,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CF,KAAK,CAAC,oDAAoD,CAAC;IAC7D;EACF,CAAC;EAED,MAAMI,QAAQ,GAAG,MAAOH,IAAI,IAAK;IAE/B,MAAMX,aAAa,CAACW,IAAI,CAACJ,KAAK,CAAC;IAC/BD,QAAQ,CAAC,QAAQ,CAAC;IAClB;EACF,CAAC;EAED,oBACEP,OAAA;IAAKgB,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxEjB,OAAA;MAAKgB,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChEjB,OAAA;QAAIgB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtErB,OAAA;QAAMe,QAAQ,EAAEX,YAAY,CAACW,QAAQ,CAAE;QAAAE,QAAA,gBACrCjB,OAAA;UAAKgB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjB,OAAA;YAAA,GACMG,QAAQ,CAAC,OAAO,EAAE;cACpBmB,QAAQ,EAAE,mBAAmB;cAC7BC,OAAO,EAAE;gBACPC,KAAK,EAAE,mDAAmD;gBAC1DC,OAAO,EAAE;cACX;YACF,CAAC,CAAC;YACFC,IAAI,EAAC,OAAO;YACZC,WAAW,EAAC,kBAAkB;YAC9BX,SAAS,EAAC;UAA0I;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrJ,CAAC,EACDf,MAAM,CAACE,KAAK,iBAAIR,OAAA;YAAGgB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEX,MAAM,CAACE,KAAK,CAACiB;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eACNrB,OAAA;UACE0B,IAAI,EAAC,QAAQ;UACbV,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CArDID,aAAa;EAAA,QACyCL,OAAO,EAChDC,WAAW;AAAA;AAAA+B,EAAA,GAFxB3B,aAAa;AAuDnB,eAAeA,aAAa;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}