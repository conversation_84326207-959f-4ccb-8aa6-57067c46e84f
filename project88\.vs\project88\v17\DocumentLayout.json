{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\App.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\RegisterService.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\features\\register\\RegisterService.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\Register.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\features\\register\\Register.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\Test.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\features\\Test.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "App.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.js", "RelativeDocumentMoniker": "src\\App.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\App.js", "RelativeToolTip": "src\\App.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAACQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:06:37.758Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "RegisterService.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\RegisterService.js", "RelativeDocumentMoniker": "src\\features\\register\\RegisterService.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\RegisterService.js", "RelativeToolTip": "src\\features\\register\\RegisterService.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-26T14:01:42.349Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Test.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\Test.js", "RelativeDocumentMoniker": "src\\features\\Test.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\Test.js", "RelativeToolTip": "src\\features\\Test.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-22T15:04:32.156Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Register.js", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\Register.js", "RelativeDocumentMoniker": "src\\features\\register\\Register.js", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\Project88\\project88\\src\\features\\register\\Register.js", "RelativeToolTip": "src\\features\\register\\Register.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-22T15:04:27.722Z", "EditorCaption": ""}]}]}]}