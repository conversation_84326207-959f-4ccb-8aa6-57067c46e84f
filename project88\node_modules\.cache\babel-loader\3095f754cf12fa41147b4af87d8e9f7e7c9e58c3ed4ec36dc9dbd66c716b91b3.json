{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Project88_FE\\\\project88\\\\src\\\\features\\\\home\\\\Redeem.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport UserAPIv2 from \"../../api/UserAPIv2\";\nimport DepositAPI from \"../../api/DepositAPI\";\nimport { getUserId } from \"../../utils/auth\";\n\n// Hàm format số tiền\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst formatAmount = value => {\n  const cleanValue = value.toString().replace(/[^0-9]/g, \"\");\n  return cleanValue.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\");\n};\nconst formatCurrency = amount => {\n  return formatAmount(amount.toString()) + \" VND\";\n};\n\n// Hàm tính tiền lãi dựa trên thông tin từ API\nconst calculateInterest = (depositAmount, interestRate, createDate) => {\n  const startDate = new Date(createDate);\n  const now = new Date();\n  startDate.setHours(0, 0, 0, 0);\n  now.setHours(0, 0, 0, 0);\n  const daysPassed = Math.floor((now - startDate) / (1000 * 60 * 60 * 24));\n  const actualDaysPassed = Math.max(0, daysPassed);\n  const yearlyInterest = depositAmount * interestRate / 100;\n  const dailyInterest = yearlyInterest / 365;\n  return Math.round(dailyInterest * actualDaysPassed);\n};\n\n// Hàm tính số ngày còn lại\nconst calculateDaysLeft = (createDate, termMonths) => {\n  const startDate = new Date(createDate);\n  const endDate = new Date(startDate);\n  if (!termMonths || termMonths <= 0) {\n    return 0;\n  }\n  endDate.setMonth(endDate.getMonth() + termMonths);\n  const now = new Date();\n\n  // Reset time\n  endDate.setHours(23, 59, 59, 999); // End of day\n  now.setHours(0, 0, 0, 0); // Start of day\n\n  const daysLeft = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));\n  return Math.max(0, daysLeft);\n};\nexport default function Redeem({\n  setShowRedeem,\n  onRedeemSuccess\n}) {\n  _s();\n  const userID = getUserId();\n  const token = localStorage.getItem(\"token\");\n  const [deposits, setDeposits] = useState([]);\n  const [sortedDeposits, setSortedDeposits] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [balance, setBalance] = useState(null);\n  const [loadingBalance, setLoadingBalance] = useState(true);\n  const [sortOrder, setSortOrder] = useState(null);\n\n  // Fetch balance\n  useEffect(() => {\n    const fetchUserBalance = async () => {\n      setLoadingBalance(true);\n      try {\n        const response = await UserAPIv2.FindUserById(userID);\n        if (response && response.data) {\n          setBalance(response.data.balance);\n        }\n      } catch (err) {\n        console.error(\"Lỗi khi lấy balance:\", err);\n        setError(\"Không thể lấy thông tin số dư\");\n        setBalance(0);\n      } finally {\n        setLoadingBalance(false);\n      }\n    };\n    if (userID) {\n      fetchUserBalance();\n    }\n  }, [userID]);\n\n  // Fetch dữ liệu sổ tiết kiệm\n  useEffect(() => {\n    const fetchDeposits = async () => {\n      try {\n        setLoading(true);\n        const response = await DepositAPI.getDeposit();\n        if (!response) {\n          throw new Error(\"Không thể tải dữ liệu sổ tiết kiệm\");\n        }\n        const rs = response.data;\n\n        // Transform data\n        const transformedData = rs.map(deposit => {\n          const depositAmount = deposit.depositAmount || deposit.amount || 0;\n          const interestRate = deposit.interestRate || 0;\n          const termMonths = deposit.termMonths || deposit.term || 0;\n          const createDate = deposit.createDate;\n          if (!createDate || !depositAmount || !interestRate) {\n            console.warn(\"Invalid deposit data:\", deposit);\n          }\n          return {\n            id: deposit.depositId || deposit.id,\n            name: deposit.depositName || deposit.accountName,\n            principal: depositAmount,\n            interest: calculateInterest(depositAmount, interestRate, createDate),\n            term: termMonths,\n            daysLeft: calculateDaysLeft(createDate, termMonths),\n            interestRate: interestRate,\n            createDate: createDate,\n            transactionId: deposit.transactionId\n          };\n        }).filter(deposit => deposit.principal > 0 && deposit.interestRate > 0);\n        setDeposits(transformedData);\n        setSortedDeposits(transformedData);\n      } catch (err) {\n        setError(err.message);\n        console.error(\"Error fetching deposits:\", err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (userID) {\n      fetchDeposits();\n    }\n  }, [userID]);\n\n  // Sắp xếp theo số ngày còn lại\n  const sortByDaysLeft = () => {\n    let newSortOrder;\n    if (sortOrder === null || sortOrder === 'desc') {\n      newSortOrder = 'asc';\n    } else {\n      newSortOrder = 'desc';\n    }\n    setSortOrder(newSortOrder);\n    const sorted = [...deposits].sort((a, b) => {\n      if (newSortOrder === 'asc') {\n        return a.daysLeft - b.daysLeft;\n      } else {\n        return b.daysLeft - a.daysLeft;\n      }\n    });\n    setSortedDeposits(sorted);\n  };\n\n  // Tính tổng tiền gốc và lãi\n  const totalAmount = deposits.reduce((sum, item) => sum + item.principal + item.interest, 0);\n  const handleRedeem = async deposit => {\n    if (window.confirm(`Bạn có chắc chắn muốn tất toán sổ \"${deposit.name}\"?`)) {\n      try {\n        const currentInterest = calculateInterest(deposit.principal, deposit.interestRate, deposit.createDate);\n        const response = await fetch(`http://localhost:8080/api/v1/deposits/${deposit.id}/redeem`, {\n          method: \"PUT\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n          },\n          body: JSON.stringify({\n            redeemDate: new Date().toISOString().split(\"T\")[0],\n            interestAmount: currentInterest,\n            userId: userID\n          })\n        });\n        if (!response.ok) {\n          const errorData = await response.text();\n          throw new Error(errorData || \"Không thể thực hiện tất toán\");\n        }\n        const updatedDeposits = deposits.filter(d => d.id !== deposit.id);\n        setDeposits(updatedDeposits);\n        setSortedDeposits(updatedDeposits);\n        const balanceResponse = await fetch(`http://localhost:8080/api/v1/users/${userID}/balance`, {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n          }\n        });\n        if (balanceResponse.ok) {\n          const newBalance = await balanceResponse.json();\n          setBalance(newBalance);\n        }\n        alert(`Tất toán thành công sổ \"${deposit.name}\". Tổng tiền nhận được: ${formatCurrency(deposit.principal + currentInterest)}`);\n        if (onRedeemSuccess) {\n          onRedeemSuccess();\n        }\n        window.location.reload();\n      } catch (err) {\n        alert(`Lỗi: ${err.message}`);\n        console.error(\"Error redeeming deposit:\", err);\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto mt-8 rounded-lg shadow-lg bg-white p-8 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-lg\",\n        children: \"\\u0110ang t\\u1EA3i d\\u1EEF li\\u1EC7u...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto mt-8 rounded-lg shadow-lg bg-white p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500 text-center mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors font-medium\",\n          onClick: () => setShowRedeem(false),\n          children: \"\\u0110\\xF3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-6xl mx-auto mt-8 rounded-lg overflow-hidden flex flex-col shadow-lg\",\n    style: {\n      background: \"#fff\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-2xl font-bold px-6 py-5 text-center bg-gray-50\",\n      children: loadingBalance ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500\",\n        children: \"\\u0110ang t\\u1EA3i s\\u1ED1 d\\u01B0...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [\"S\\u1ED1 d\\u01B0 kh\\u1EA3 d\\u1EE5ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-[#E65100]\",\n          children: formatCurrency(balance || 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xl font-bold px-6 py-3 text-center bg-blue-50\",\n      children: [\"T\\u1ED5ng ti\\u1EC1n g\\u1ED1c v\\xE0 l\\xE3i:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-blue-600\",\n        children: formatCurrency(totalAmount)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), deposits.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-6 py-8 text-center text-gray-500\",\n      children: \"B\\u1EA1n ch\\u01B0a c\\xF3 s\\u1ED5 ti\\u1EBFt ki\\u1EC7m n\\xE0o \\u0111ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    /* Table Container với responsive scroll */\n    _jsxDEV(\"div\", {\n      className: \"px-2 sm:px-6 py-2 bg-white overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-h-[500px] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full text-left min-w-[900px]\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"text-gray-500 border-b bg-gray-50 sticky top-0 z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"py-3 px-2 font-medium\",\n                children: \"T\\xEAn s\\u1ED5 ti\\u1EBFt ki\\u1EC7m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"py-3 px-2 font-medium text-right\",\n                children: \"Ti\\u1EC1n g\\u1ED1c\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"py-3 px-2 font-medium text-right\",\n                children: \"L\\xE3i su\\u1EA5t (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"py-3 px-2 font-medium text-right\",\n                children: \"Ti\\u1EC1n l\\xE3i t\\u1EDBi hi\\u1EC7n t\\u1EA1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"py-3 px-2 font-medium text-center\",\n                children: \"K\\u1EF3 h\\u1EA1n (Th\\xE1ng)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"py-3 px-2 font-medium text-center cursor-pointer hover:bg-gray-100 transition-colors\",\n                onClick: sortByDaysLeft,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center\",\n                  children: [\"S\\u1ED1 ng\\xE0y c\\xF2n l\\u1EA1i\", sortOrder === 'asc' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: \"\\u2191\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this), sortOrder === 'desc' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: \"\\u2193\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"py-3 px-2 font-medium text-center\",\n                children: \"Thao t\\xE1c\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: sortedDeposits.map(item => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"border-b last:border-b-0 hover:bg-gray-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-4 px-2 font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\"M\\xE3 GD: \", item.transactionId]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-4 px-2 text-right font-mono\",\n                children: formatCurrency(item.principal)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-4 px-2 text-right font-mono text-blue-600\",\n                children: [item.interestRate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-4 px-2 text-right font-mono text-green-600\",\n                children: formatCurrency(item.interest)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-4 px-2 text-center\",\n                children: item.term\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-4 px-2 text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-sm ${item.daysLeft <= 0 ? \"bg-red-100 text-red-700\" : item.daysLeft <= 5 ? \"bg-orange-100 text-orange-700\" : item.daysLeft <= 15 ? \"bg-yellow-100 text-yellow-700\" : \"bg-green-100 text-green-700\"}`,\n                  children: item.daysLeft <= 0 ? \"Đã đến hạn\" : `${item.daysLeft} ngày`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-4 px-2 text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-sm font-medium\",\n                  onClick: () => handleRedeem(item),\n                  children: \"T\\u1EA5t to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-6 py-4 bg-gray-50 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors font-medium\",\n        onClick: () => setShowRedeem(false),\n        children: \"\\u0110\\xF3ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 262,\n    columnNumber: 5\n  }, this);\n}\n_s(Redeem, \"e6XM8+VqWjyK4ZMbVIEXLePs2Ts=\");\n_c = Redeem;\nvar _c;\n$RefreshReg$(_c, \"Redeem\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "UserAPIv2", "DepositAPI", "getUserId", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "formatAmount", "value", "cleanValue", "toString", "replace", "formatCurrency", "amount", "calculateInterest", "depositAmount", "interestRate", "createDate", "startDate", "Date", "now", "setHours", "daysPassed", "Math", "floor", "actualDaysPassed", "max", "yearlyInterest", "dailyInterest", "round", "calculateDaysLeft", "termMonths", "endDate", "setMonth", "getMonth", "daysLeft", "ceil", "Redeem", "setShowRedeem", "onRedeemSuccess", "_s", "userID", "token", "localStorage", "getItem", "deposits", "setDeposits", "sortedDeposits", "setSortedDeposits", "loading", "setLoading", "error", "setError", "balance", "setBalance", "loadingBalance", "setLoadingBalance", "sortOrder", "setSortOrder", "fetchUserBalance", "response", "FindUserById", "data", "err", "console", "fetchDeposits", "getDeposit", "Error", "rs", "transformedData", "map", "deposit", "term", "warn", "id", "depositId", "name", "depositName", "accountName", "principal", "interest", "transactionId", "filter", "message", "sortByDaysLeft", "newSortOrder", "sorted", "sort", "a", "b", "totalAmount", "reduce", "sum", "item", "handleRedeem", "window", "confirm", "currentInterest", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "redeemDate", "toISOString", "split", "interestAmount", "userId", "ok", "errorData", "text", "updatedDeposits", "d", "balanceResponse", "newBalance", "json", "alert", "location", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "background", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/features/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport UserAPIv2 from \"../../api/UserAPIv2\";\r\nimport DepositAPI from \"../../api/DepositAPI\";\r\nimport { getUserId } from \"../../utils/auth\";\r\n\r\n// Hàm format số tiền\r\nconst formatAmount = (value) => {\r\n  const cleanValue = value.toString().replace(/[^0-9]/g, \"\");\r\n  return cleanValue.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\");\r\n};\r\n\r\nconst formatCurrency = (amount) => {\r\n  return formatAmount(amount.toString()) + \" VND\";\r\n};\r\n\r\n// Hàm tính tiền lãi dựa trên thông tin từ API\r\nconst calculateInterest = (depositAmount, interestRate, createDate) => {\r\n  const startDate = new Date(createDate);\r\n  const now = new Date();\r\n\r\n  startDate.setHours(0, 0, 0, 0);\r\n  now.setHours(0, 0, 0, 0);\r\n\r\n  const daysPassed = Math.floor((now - startDate) / (1000 * 60 * 60 * 24));\r\n  const actualDaysPassed = Math.max(0, daysPassed);\r\n  const yearlyInterest = (depositAmount * interestRate) / 100;\r\n  const dailyInterest = yearlyInterest / 365;\r\n  return Math.round(dailyInterest * actualDaysPassed);\r\n};\r\n\r\n// Hàm tính số ngày còn lại\r\nconst calculateDaysLeft = (createDate, termMonths) => {\r\n  const startDate = new Date(createDate);\r\n  const endDate = new Date(startDate);\r\n\r\n  if (!termMonths || termMonths <= 0) {\r\n    return 0;\r\n  }\r\n  endDate.setMonth(endDate.getMonth() + termMonths);\r\n  const now = new Date();\r\n\r\n  // Reset time\r\n  endDate.setHours(23, 59, 59, 999); // End of day\r\n  now.setHours(0, 0, 0, 0); // Start of day\r\n\r\n  const daysLeft = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));\r\n  return Math.max(0, daysLeft);\r\n};\r\n\r\nexport default function Redeem({ setShowRedeem, onRedeemSuccess }) {\r\n  const userID = getUserId();\r\n  const token = localStorage.getItem(\"token\");\r\n\r\n  const [deposits, setDeposits] = useState([]);\r\n  const [sortedDeposits, setSortedDeposits] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [balance, setBalance] = useState(null);\r\n  const [loadingBalance, setLoadingBalance] = useState(true);\r\n  const [sortOrder, setSortOrder] = useState(null);\r\n\r\n  // Fetch balance\r\n  useEffect(() => {\r\n    const fetchUserBalance = async () => {\r\n      setLoadingBalance(true);\r\n      try {\r\n\r\n        const response = await UserAPIv2.FindUserById(userID);\r\n\r\n        if (response && response.data) {\r\n          setBalance(response.data.balance);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Lỗi khi lấy balance:\", err);\r\n        setError(\"Không thể lấy thông tin số dư\");\r\n        setBalance(0);\r\n      } finally {\r\n        setLoadingBalance(false);\r\n      }\r\n    };\r\n\r\n    if (userID) {\r\n      fetchUserBalance();\r\n    }\r\n  }, [userID]);\r\n\r\n  // Fetch dữ liệu sổ tiết kiệm\r\n  useEffect(() => {\r\n    const fetchDeposits = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await DepositAPI.getDeposit();\r\n\r\n        if (!response) {\r\n          throw new Error(\"Không thể tải dữ liệu sổ tiết kiệm\");\r\n        }\r\n        const rs = response.data;\r\n\r\n        // Transform data\r\n        const transformedData = rs\r\n          .map((deposit) => {\r\n            const depositAmount = deposit.depositAmount || deposit.amount || 0;\r\n            const interestRate = deposit.interestRate || 0;\r\n            const termMonths = deposit.termMonths || deposit.term || 0;\r\n            const createDate = deposit.createDate;\r\n\r\n            if (!createDate || !depositAmount || !interestRate) {\r\n              console.warn(\"Invalid deposit data:\", deposit);\r\n            }\r\n\r\n            return {\r\n              id: deposit.depositId || deposit.id,\r\n              name: deposit.depositName || deposit.accountName,\r\n              principal: depositAmount,\r\n              interest: calculateInterest(\r\n                depositAmount,\r\n                interestRate,\r\n                createDate\r\n              ),\r\n              term: termMonths,\r\n              daysLeft: calculateDaysLeft(createDate, termMonths),\r\n              interestRate: interestRate,\r\n              createDate: createDate,\r\n              transactionId: deposit.transactionId,\r\n            };\r\n          })\r\n          .filter(\r\n            (deposit) => deposit.principal > 0 && deposit.interestRate > 0\r\n          );\r\n\r\n        setDeposits(transformedData);\r\n        setSortedDeposits(transformedData);\r\n      } catch (err) {\r\n        setError(err.message);\r\n        console.error(\"Error fetching deposits:\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (userID) {\r\n      fetchDeposits();\r\n    }\r\n  }, [userID]);\r\n\r\n  // Sắp xếp theo số ngày còn lại\r\n  const sortByDaysLeft = () => {\r\n    let newSortOrder;\r\n    if (sortOrder === null || sortOrder === 'desc') {\r\n      newSortOrder = 'asc';\r\n    } else {\r\n      newSortOrder = 'desc';\r\n    }\r\n    setSortOrder(newSortOrder);\r\n    const sorted = [...deposits].sort((a, b) => {\r\n      if (newSortOrder === 'asc') {\r\n        return a.daysLeft - b.daysLeft;\r\n      } else {\r\n        return b.daysLeft - a.daysLeft;\r\n      }\r\n    });\r\n    setSortedDeposits(sorted);\r\n  };\r\n\r\n  // Tính tổng tiền gốc và lãi\r\n  const totalAmount = deposits.reduce(\r\n    (sum, item) => sum + item.principal + item.interest,\r\n    0\r\n  );\r\n\r\n  const handleRedeem = async (deposit) => {\r\n    if (\r\n      window.confirm(`Bạn có chắc chắn muốn tất toán sổ \"${deposit.name}\"?`)\r\n    ) {\r\n      try {\r\n        const currentInterest = calculateInterest(\r\n          deposit.principal,\r\n          deposit.interestRate,\r\n          deposit.createDate\r\n        );\r\n\r\n        const response = await fetch(\r\n          `http://localhost:8080/api/v1/deposits/${deposit.id}/redeem`,\r\n          {\r\n            method: \"PUT\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${token}`,\r\n            },\r\n            body: JSON.stringify({\r\n              redeemDate: new Date().toISOString().split(\"T\")[0],\r\n              interestAmount: currentInterest,\r\n              userId: userID,\r\n            }),\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          const errorData = await response.text();\r\n          throw new Error(errorData || \"Không thể thực hiện tất toán\");\r\n        }\r\n        \r\n        const updatedDeposits = deposits.filter((d) => d.id !== deposit.id);\r\n        setDeposits(updatedDeposits);\r\n        setSortedDeposits(updatedDeposits);\r\n\r\n        const balanceResponse = await fetch(\r\n          `http://localhost:8080/api/v1/users/${userID}/balance`,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n          }\r\n        );\r\n        if (balanceResponse.ok) {\r\n          const newBalance = await balanceResponse.json();\r\n          setBalance(newBalance);\r\n        }\r\n        alert(\r\n          `Tất toán thành công sổ \"${deposit.name\r\n          }\". Tổng tiền nhận được: ${formatCurrency(\r\n            deposit.principal + currentInterest\r\n          )}`\r\n        );\r\n        if (onRedeemSuccess) {\r\n          onRedeemSuccess();\r\n        }\r\n        window.location.reload();\r\n      } catch (err) {\r\n        alert(`Lỗi: ${err.message}`);\r\n        console.error(\"Error redeeming deposit:\", err);\r\n      }\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"max-w-6xl mx-auto mt-8 rounded-lg shadow-lg bg-white p-8 text-center\">\r\n        <div className=\"text-lg\">Đang tải dữ liệu...</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"max-w-6xl mx-auto mt-8 rounded-lg shadow-lg bg-white p-8\">\r\n        <div className=\"text-red-500 text-center mb-4\">{error}</div>\r\n        <div className=\"text-center\">\r\n          <button\r\n            className=\"px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors font-medium\"\r\n            onClick={() => setShowRedeem(false)}\r\n          >\r\n            Đóng\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"max-w-6xl mx-auto mt-8 rounded-lg overflow-hidden flex flex-col shadow-lg\"\r\n      style={{ background: \"#fff\" }}\r\n    >\r\n      {/* Balance Header */}\r\n      <div className=\"text-2xl font-bold px-6 py-5 text-center bg-gray-50\">\r\n        {loadingBalance ? (\r\n          <div className=\"text-gray-500\">Đang tải số dư...</div>\r\n        ) : (\r\n          <>\r\n            Số dư khả dụng:{\" \"}\r\n            <span className=\"text-[#E65100]\">\r\n              {formatCurrency(balance || 0)}\r\n            </span>\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      {/* Total Amount */}\r\n      <div className=\"text-xl font-bold px-6 py-3 text-center bg-blue-50\">\r\n        Tổng tiền gốc và lãi:{\" \"}\r\n        <span className=\"text-blue-600\">{formatCurrency(totalAmount)}</span>\r\n      </div>\r\n      {deposits.length === 0 ? (\r\n        <div className=\"px-6 py-8 text-center text-gray-500\">\r\n          Bạn chưa có sổ tiết kiệm nào đang hoạt động\r\n        </div>\r\n      ) : (\r\n        /* Table Container với responsive scroll */\r\n        <div className=\"px-2 sm:px-6 py-2 bg-white overflow-x-auto\">\r\n          <div className=\"max-h-[500px] overflow-y-auto\">\r\n          <table className=\"w-full text-left min-w-[900px]\">\r\n            <thead>\r\n            <tr className=\"text-gray-500 border-b bg-gray-50 sticky top-0 z-10\">\r\n                <th className=\"py-3 px-2 font-medium\">Tên sổ tiết kiệm</th>\r\n                <th className=\"py-3 px-2 font-medium text-right\">Tiền gốc</th>\r\n                <th className=\"py-3 px-2 font-medium text-right\">\r\n                  Lãi suất (%)\r\n                </th>\r\n                <th className=\"py-3 px-2 font-medium text-right\">\r\n                  Tiền lãi tới hiện tại\r\n                </th>\r\n                <th className=\"py-3 px-2 font-medium text-center\">\r\n                  Kỳ hạn (Tháng)\r\n                </th>\r\n                <th \r\n                    className=\"py-3 px-2 font-medium text-center cursor-pointer hover:bg-gray-100 transition-colors\"\r\n                    onClick={sortByDaysLeft}\r\n                  >\r\n                    <div className=\"flex items-center justify-center\">\r\n                      Số ngày còn lại\r\n                      {sortOrder === 'asc' && (\r\n                        <span className=\"ml-1\">↑</span>\r\n                      )}\r\n                      {sortOrder === 'desc' && (\r\n                        <span className=\"ml-1\">↓</span>\r\n                      )}\r\n                    </div>\r\n                  </th>\r\n                <th className=\"py-3 px-2 font-medium text-center\">Thao tác</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n            {sortedDeposits.map((item) => (\r\n                <tr\r\n                  key={item.id}\r\n                  className=\"border-b last:border-b-0 hover:bg-gray-50 transition-colors\"\r\n                >\r\n                  <td className=\"py-4 px-2 font-medium\">\r\n                    <div>\r\n                      <div>{item.name}</div>\r\n                      <div className=\"text-xs text-gray-500\">\r\n                        Mã GD: {item.transactionId}\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"py-4 px-2 text-right font-mono\">\r\n                    {formatCurrency(item.principal)}\r\n                  </td>\r\n                  <td className=\"py-4 px-2 text-right font-mono text-blue-600\">\r\n                    {item.interestRate}%\r\n                  </td>\r\n                  <td className=\"py-4 px-2 text-right font-mono text-green-600\">\r\n                    {formatCurrency(item.interest)}\r\n                  </td>\r\n                  <td className=\"py-4 px-2 text-center\">{item.term}</td>\r\n                  <td className=\"py-4 px-2 text-center\">\r\n                    <span\r\n                      className={`px-2 py-1 rounded-full text-sm ${item.daysLeft <= 0\r\n                        ? \"bg-red-100 text-red-700\"\r\n                        : item.daysLeft <= 5\r\n                          ? \"bg-orange-100 text-orange-700\"\r\n                          : item.daysLeft <= 15\r\n                            ? \"bg-yellow-100 text-yellow-700\"\r\n                            : \"bg-green-100 text-green-700\"\r\n                        }`}\r\n                    >\r\n                      {item.daysLeft <= 0\r\n                        ? \"Đã đến hạn\"\r\n                        : `${item.daysLeft} ngày`}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"py-4 px-2 text-center\">\r\n                    <button\r\n                      className=\"px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-sm font-medium\"\r\n                      onClick={() => handleRedeem(item)}\r\n                    >\r\n                      Tất toán\r\n                    </button>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        </div>\r\n      )}\r\n      {/* Footer */}\r\n      <div className=\"px-6 py-4 bg-gray-50 text-center\">\r\n        <button\r\n          className=\"px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors font-medium\"\r\n          onClick={() => setShowRedeem(false)}\r\n        >\r\n          Đóng\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,SAASC,SAAS,QAAQ,kBAAkB;;AAE5C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAIC,KAAK,IAAK;EAC9B,MAAMC,UAAU,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EAC1D,OAAOF,UAAU,CAACE,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC;AACzD,CAAC;AAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;EACjC,OAAON,YAAY,CAACM,MAAM,CAACH,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM;AACjD,CAAC;;AAED;AACA,MAAMI,iBAAiB,GAAGA,CAACC,aAAa,EAAEC,YAAY,EAAEC,UAAU,KAAK;EACrE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACtC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;EAEtBD,SAAS,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9BD,GAAG,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAExB,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,GAAGF,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACxE,MAAMO,gBAAgB,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEJ,UAAU,CAAC;EAChD,MAAMK,cAAc,GAAIZ,aAAa,GAAGC,YAAY,GAAI,GAAG;EAC3D,MAAMY,aAAa,GAAGD,cAAc,GAAG,GAAG;EAC1C,OAAOJ,IAAI,CAACM,KAAK,CAACD,aAAa,GAAGH,gBAAgB,CAAC;AACrD,CAAC;;AAED;AACA,MAAMK,iBAAiB,GAAGA,CAACb,UAAU,EAAEc,UAAU,KAAK;EACpD,MAAMb,SAAS,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACtC,MAAMe,OAAO,GAAG,IAAIb,IAAI,CAACD,SAAS,CAAC;EAEnC,IAAI,CAACa,UAAU,IAAIA,UAAU,IAAI,CAAC,EAAE;IAClC,OAAO,CAAC;EACV;EACAC,OAAO,CAACC,QAAQ,CAACD,OAAO,CAACE,QAAQ,CAAC,CAAC,GAAGH,UAAU,CAAC;EACjD,MAAMX,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;;EAEtB;EACAa,OAAO,CAACX,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;EACnCD,GAAG,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAE1B,MAAMc,QAAQ,GAAGZ,IAAI,CAACa,IAAI,CAAC,CAACJ,OAAO,GAAGZ,GAAG,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACnE,OAAOG,IAAI,CAACG,GAAG,CAAC,CAAC,EAAES,QAAQ,CAAC;AAC9B,CAAC;AAED,eAAe,SAASE,MAAMA,CAAC;EAAEC,aAAa;EAAEC;AAAgB,CAAC,EAAE;EAAAC,EAAA;EACjE,MAAMC,MAAM,GAAGvC,SAAS,CAAC,CAAC;EAC1B,MAAMwC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAE3C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnCH,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAI;QAEF,MAAMI,QAAQ,GAAG,MAAM5D,SAAS,CAAC6D,YAAY,CAACpB,MAAM,CAAC;QAErD,IAAImB,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;UAC7BR,UAAU,CAACM,QAAQ,CAACE,IAAI,CAACT,OAAO,CAAC;QACnC;MACF,CAAC,CAAC,OAAOU,GAAG,EAAE;QACZC,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEY,GAAG,CAAC;QAC1CX,QAAQ,CAAC,+BAA+B,CAAC;QACzCE,UAAU,CAAC,CAAC,CAAC;MACf,CAAC,SAAS;QACRE,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAED,IAAIf,MAAM,EAAE;MACVkB,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;;EAEZ;EACA1C,SAAS,CAAC,MAAM;IACd,MAAMkE,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFf,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMU,QAAQ,GAAG,MAAM3D,UAAU,CAACiE,UAAU,CAAC,CAAC;QAE9C,IAAI,CAACN,QAAQ,EAAE;UACb,MAAM,IAAIO,KAAK,CAAC,oCAAoC,CAAC;QACvD;QACA,MAAMC,EAAE,GAAGR,QAAQ,CAACE,IAAI;;QAExB;QACA,MAAMO,eAAe,GAAGD,EAAE,CACvBE,GAAG,CAAEC,OAAO,IAAK;UAChB,MAAMxD,aAAa,GAAGwD,OAAO,CAACxD,aAAa,IAAIwD,OAAO,CAAC1D,MAAM,IAAI,CAAC;UAClE,MAAMG,YAAY,GAAGuD,OAAO,CAACvD,YAAY,IAAI,CAAC;UAC9C,MAAMe,UAAU,GAAGwC,OAAO,CAACxC,UAAU,IAAIwC,OAAO,CAACC,IAAI,IAAI,CAAC;UAC1D,MAAMvD,UAAU,GAAGsD,OAAO,CAACtD,UAAU;UAErC,IAAI,CAACA,UAAU,IAAI,CAACF,aAAa,IAAI,CAACC,YAAY,EAAE;YAClDgD,OAAO,CAACS,IAAI,CAAC,uBAAuB,EAAEF,OAAO,CAAC;UAChD;UAEA,OAAO;YACLG,EAAE,EAAEH,OAAO,CAACI,SAAS,IAAIJ,OAAO,CAACG,EAAE;YACnCE,IAAI,EAAEL,OAAO,CAACM,WAAW,IAAIN,OAAO,CAACO,WAAW;YAChDC,SAAS,EAAEhE,aAAa;YACxBiE,QAAQ,EAAElE,iBAAiB,CACzBC,aAAa,EACbC,YAAY,EACZC,UACF,CAAC;YACDuD,IAAI,EAAEzC,UAAU;YAChBI,QAAQ,EAAEL,iBAAiB,CAACb,UAAU,EAAEc,UAAU,CAAC;YACnDf,YAAY,EAAEA,YAAY;YAC1BC,UAAU,EAAEA,UAAU;YACtBgE,aAAa,EAAEV,OAAO,CAACU;UACzB,CAAC;QACH,CAAC,CAAC,CACDC,MAAM,CACJX,OAAO,IAAKA,OAAO,CAACQ,SAAS,GAAG,CAAC,IAAIR,OAAO,CAACvD,YAAY,GAAG,CAC/D,CAAC;QAEH8B,WAAW,CAACuB,eAAe,CAAC;QAC5BrB,iBAAiB,CAACqB,eAAe,CAAC;MACpC,CAAC,CAAC,OAAON,GAAG,EAAE;QACZX,QAAQ,CAACW,GAAG,CAACoB,OAAO,CAAC;QACrBnB,OAAO,CAACb,KAAK,CAAC,0BAA0B,EAAEY,GAAG,CAAC;MAChD,CAAC,SAAS;QACRb,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIT,MAAM,EAAE;MACVwB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACxB,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAM2C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,YAAY;IAChB,IAAI5B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC9C4B,YAAY,GAAG,KAAK;IACtB,CAAC,MAAM;MACLA,YAAY,GAAG,MAAM;IACvB;IACA3B,YAAY,CAAC2B,YAAY,CAAC;IAC1B,MAAMC,MAAM,GAAG,CAAC,GAAGzC,QAAQ,CAAC,CAAC0C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,IAAIJ,YAAY,KAAK,KAAK,EAAE;QAC1B,OAAOG,CAAC,CAACrD,QAAQ,GAAGsD,CAAC,CAACtD,QAAQ;MAChC,CAAC,MAAM;QACL,OAAOsD,CAAC,CAACtD,QAAQ,GAAGqD,CAAC,CAACrD,QAAQ;MAChC;IACF,CAAC,CAAC;IACFa,iBAAiB,CAACsC,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMI,WAAW,GAAG7C,QAAQ,CAAC8C,MAAM,CACjC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACd,SAAS,GAAGc,IAAI,CAACb,QAAQ,EACnD,CACF,CAAC;EAED,MAAMc,YAAY,GAAG,MAAOvB,OAAO,IAAK;IACtC,IACEwB,MAAM,CAACC,OAAO,CAAC,sCAAsCzB,OAAO,CAACK,IAAI,IAAI,CAAC,EACtE;MACA,IAAI;QACF,MAAMqB,eAAe,GAAGnF,iBAAiB,CACvCyD,OAAO,CAACQ,SAAS,EACjBR,OAAO,CAACvD,YAAY,EACpBuD,OAAO,CAACtD,UACV,CAAC;QAED,MAAM2C,QAAQ,GAAG,MAAMsC,KAAK,CAC1B,yCAAyC3B,OAAO,CAACG,EAAE,SAAS,EAC5D;UACEyB,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAU3D,KAAK;UAChC,CAAC;UACD4D,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,UAAU,EAAE,IAAItF,IAAI,CAAC,CAAC,CAACuF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClDC,cAAc,EAAEX,eAAe;YAC/BY,MAAM,EAAEpE;UACV,CAAC;QACH,CACF,CAAC;QAED,IAAI,CAACmB,QAAQ,CAACkD,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMnD,QAAQ,CAACoD,IAAI,CAAC,CAAC;UACvC,MAAM,IAAI7C,KAAK,CAAC4C,SAAS,IAAI,8BAA8B,CAAC;QAC9D;QAEA,MAAME,eAAe,GAAGpE,QAAQ,CAACqC,MAAM,CAAEgC,CAAC,IAAKA,CAAC,CAACxC,EAAE,KAAKH,OAAO,CAACG,EAAE,CAAC;QACnE5B,WAAW,CAACmE,eAAe,CAAC;QAC5BjE,iBAAiB,CAACiE,eAAe,CAAC;QAElC,MAAME,eAAe,GAAG,MAAMjB,KAAK,CACjC,sCAAsCzD,MAAM,UAAU,EACtD;UACE2D,OAAO,EAAE;YACPC,aAAa,EAAE,UAAU3D,KAAK,EAAE;YAChC,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QACD,IAAIyE,eAAe,CAACL,EAAE,EAAE;UACtB,MAAMM,UAAU,GAAG,MAAMD,eAAe,CAACE,IAAI,CAAC,CAAC;UAC/C/D,UAAU,CAAC8D,UAAU,CAAC;QACxB;QACAE,KAAK,CACH,2BAA2B/C,OAAO,CAACK,IAAI,2BACZhE,cAAc,CACvC2D,OAAO,CAACQ,SAAS,GAAGkB,eACtB,CAAC,EACH,CAAC;QACD,IAAI1D,eAAe,EAAE;UACnBA,eAAe,CAAC,CAAC;QACnB;QACAwD,MAAM,CAACwB,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOzD,GAAG,EAAE;QACZuD,KAAK,CAAC,QAAQvD,GAAG,CAACoB,OAAO,EAAE,CAAC;QAC5BnB,OAAO,CAACb,KAAK,CAAC,0BAA0B,EAAEY,GAAG,CAAC;MAChD;IACF;EACF,CAAC;EAED,IAAId,OAAO,EAAE;IACX,oBACE7C,OAAA;MAAKqH,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACnFtH,OAAA;QAAKqH,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,IAAI3E,KAAK,EAAE;IACT,oBACE/C,OAAA;MAAKqH,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvEtH,OAAA;QAAKqH,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAEvE;MAAK;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5D1H,OAAA;QAAKqH,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BtH,OAAA;UACEqH,SAAS,EAAC,wFAAwF;UAClGM,OAAO,EAAEA,CAAA,KAAMzF,aAAa,CAAC,KAAK,CAAE;UAAAoF,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1H,OAAA;IACEqH,SAAS,EAAC,2EAA2E;IACrFO,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAO,CAAE;IAAAP,QAAA,gBAG9BtH,OAAA;MAAKqH,SAAS,EAAC,qDAAqD;MAAAC,QAAA,EACjEnE,cAAc,gBACbnD,OAAA;QAAKqH,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAEtD1H,OAAA,CAAAE,SAAA;QAAAoH,QAAA,GAAE,qCACe,EAAC,GAAG,eACnBtH,OAAA;UAAMqH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC7B9G,cAAc,CAACyC,OAAO,IAAI,CAAC;QAAC;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA,eACP;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1H,OAAA;MAAKqH,SAAS,EAAC,oDAAoD;MAAAC,QAAA,GAAC,4CAC7C,EAAC,GAAG,eACzBtH,OAAA;QAAMqH,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE9G,cAAc,CAAC8E,WAAW;MAAC;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,EACLjF,QAAQ,CAACqF,MAAM,KAAK,CAAC,gBACpB9H,OAAA;MAAKqH,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAAC;IAErD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAAA;IAEN;IACA1H,OAAA;MAAKqH,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzDtH,OAAA;QAAKqH,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC9CtH,OAAA;UAAOqH,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC/CtH,OAAA;YAAAsH,QAAA,eACAtH,OAAA;cAAIqH,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAC/DtH,OAAA;gBAAIqH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D1H,OAAA;gBAAIqH,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D1H,OAAA;gBAAIqH,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1H,OAAA;gBAAIqH,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1H,OAAA;gBAAIqH,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1H,OAAA;gBACIqH,SAAS,EAAC,sFAAsF;gBAChGM,OAAO,EAAE3C,cAAe;gBAAAsC,QAAA,eAExBtH,OAAA;kBAAKqH,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,iCAEhD,EAACjE,SAAS,KAAK,KAAK,iBAClBrD,OAAA;oBAAMqH,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC/B,EACArE,SAAS,KAAK,MAAM,iBACnBrD,OAAA;oBAAMqH,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACP1H,OAAA;gBAAIqH,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR1H,OAAA;YAAAsH,QAAA,EACC3E,cAAc,CAACuB,GAAG,CAAEuB,IAAI,iBACrBzF,OAAA;cAEEqH,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAEvEtH,OAAA;gBAAIqH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACnCtH,OAAA;kBAAAsH,QAAA,gBACEtH,OAAA;oBAAAsH,QAAA,EAAM7B,IAAI,CAACjB;kBAAI;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtB1H,OAAA;oBAAKqH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,YAC9B,EAAC7B,IAAI,CAACZ,aAAa;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL1H,OAAA;gBAAIqH,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAC3C9G,cAAc,CAACiF,IAAI,CAACd,SAAS;cAAC;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACL1H,OAAA;gBAAIqH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GACzD7B,IAAI,CAAC7E,YAAY,EAAC,GACrB;cAAA;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1H,OAAA;gBAAIqH,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAC1D9G,cAAc,CAACiF,IAAI,CAACb,QAAQ;cAAC;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACL1H,OAAA;gBAAIqH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE7B,IAAI,CAACrB;cAAI;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtD1H,OAAA;gBAAIqH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACnCtH,OAAA;kBACEqH,SAAS,EAAE,kCAAkC5B,IAAI,CAAC1D,QAAQ,IAAI,CAAC,GAC3D,yBAAyB,GACzB0D,IAAI,CAAC1D,QAAQ,IAAI,CAAC,GAChB,+BAA+B,GAC/B0D,IAAI,CAAC1D,QAAQ,IAAI,EAAE,GACjB,+BAA+B,GAC/B,6BAA6B,EAChC;kBAAAuF,QAAA,EAEJ7B,IAAI,CAAC1D,QAAQ,IAAI,CAAC,GACf,YAAY,GACZ,GAAG0D,IAAI,CAAC1D,QAAQ;gBAAO;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL1H,OAAA;gBAAIqH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACnCtH,OAAA;kBACEqH,SAAS,EAAC,sGAAsG;kBAChHM,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAACD,IAAI,CAAE;kBAAA6B,QAAA,EACnC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GA5CAjC,IAAI,CAACnB,EAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6CV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,eAED1H,OAAA;MAAKqH,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CtH,OAAA;QACEqH,SAAS,EAAC,wFAAwF;QAClGM,OAAO,EAAEA,CAAA,KAAMzF,aAAa,CAAC,KAAK,CAAE;QAAAoF,QAAA,EACrC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtF,EAAA,CApVuBH,MAAM;AAAA8F,EAAA,GAAN9F,MAAM;AAAA,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}