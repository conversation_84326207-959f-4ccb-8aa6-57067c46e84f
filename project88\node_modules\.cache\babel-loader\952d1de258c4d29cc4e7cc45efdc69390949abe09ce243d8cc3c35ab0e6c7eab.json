{"ast": null, "code": "import { jwtDecode } from \"jwt-decode\";\nexport const getToken = () => localStorage.getItem(\"token\");\nexport const getDecodedToken = () => {\n  const token = getToken();\n  if (!token) return null;\n  try {\n    return jwtDecode(token);\n  } catch (e) {\n    return null;\n  }\n};\nexport const getUserRole = () => {\n  const decoded = getDecodedToken();\n  return (decoded === null || decoded === void 0 ? void 0 : decoded.role) || null;\n};\nexport const getUserId = () => {\n  const decoded = getDecodedToken();\n  return (decoded === null || decoded === void 0 ? void 0 : decoded.userId) || null;\n};", "map": {"version": 3, "names": ["jwtDecode", "getToken", "localStorage", "getItem", "getDecodedToken", "token", "e", "getUserRole", "decoded", "role", "getUserId", "userId"], "sources": ["C:/Users/<USER>/Documents/GitHub/Project88_FE/project88/src/utils/auth.js"], "sourcesContent": ["import { jwtDecode } from \"jwt-decode\";\r\n\r\nexport const getToken = () => localStorage.getItem(\"token\");\r\n\r\nexport const getDecodedToken = () => {\r\n  const token = getToken();\r\n  if (!token) return null;\r\n  try {\r\n    return jwtDecode(token);\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n};\r\n\r\nexport const getUserRole = () => {\r\n  const decoded = getDecodedToken();\r\n  return decoded?.role || null;\r\n};\r\n\r\nexport const getUserId = () => {\r\n  const decoded = getDecodedToken();\r\n  return decoded?.userId || null;\r\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AAEtC,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAMC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AAE3D,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EACnC,MAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EACxB,IAAI,CAACI,KAAK,EAAE,OAAO,IAAI;EACvB,IAAI;IACF,OAAOL,SAAS,CAACK,KAAK,CAAC;EACzB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC/B,MAAMC,OAAO,GAAGJ,eAAe,CAAC,CAAC;EACjC,OAAO,CAAAI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,IAAI,KAAI,IAAI;AAC9B,CAAC;AAED,OAAO,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAC7B,MAAMF,OAAO,GAAGJ,eAAe,CAAC,CAAC;EACjC,OAAO,CAAAI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,MAAM,KAAI,IAAI;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}