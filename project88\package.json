{"name": "project88", "version": "0.1.0", "private": true, "proxy": "http://localhost:8080", "dependencies": {"@hookform/resolvers": "^5.0.1", "@reduxjs/toolkit": "^2.8.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-number-format": "^5.4.4", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "sass": "^1.88.0", "sweetalert2": "^11.22.0", "web-vitals": "^2.1.4", "zod": "^3.24.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}